import React, { Fragment, useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import {
	Button,
	Checkbox,
	ColorPicker,
	Input,
	InputNumber,
	Select,
	Slider,
	Switch,
	Tooltip,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { CustomSlider } from '../../../../common/styledComponent';
import { useDispatch, useSelector } from 'react-redux';
import { setAgentParamUserAction } from '../../../../reducer/productDefine';
import { useUpdateFeatureMutation } from '../../../../services/product';
import {
	setContainerLvlLoadingMsg,
	setIsContainerLvlLoadingEnabled,
} from '../../../../reducer/setting';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import {
        agentParamUserActionTypes,
        displayableAgentParams,
        doubleSidedSliderAgentParams,
	extendedRoi,
	heightRange,
	lead2d3dSharedAgentParams,
	lead3DExtTop,
	leadInspection2D,
	leadInspection3D,
	mounting3DExtendedRoiAgentParamNames,
	mountingInspection2D,
	mountingInspection3D,
	polarityCheckThreshold,
	polarityRoi,
	profileRoi,
	profileRoiAgentParamNames,
	solder3DExtendedRoiAgentParamNames,
	solderColorCenterHue,
	solderColorCenterSat,
	solderColorEndHue,
	solderColorEndVal,
	solderColorStartHue,
	solderColorStartVal,
        solderInspection3D,
        solderValidRatioRange,
        mmValueDecimal,
        angleValueDecimal,
        mmAgentParamsNames,
        angleAgentParamsNames,
} from '../../../../common/const';
import { getColorByStr, is2DLeadColorAgentParams, is2DLeadRangeAgentParams, orderAgentParams } from '../../../../common/util';
import { text } from '../../../../common/translation';
import { t } from 'i18next';
import {
	getComponentCenterByRoiDtoObj,
	getComponentRectInfoByFeatures,
	rotatePoint,
} from '../../../../viewer/util';
import { AggregationColor } from 'antd/es/color-picker/color';
import HSVColorRangePicker from '../../../../modal/HSVColorRangePicker';
import './CustomSlider.css';
import { systemApi } from '../../../../services/system';

const AgentParams = (props) => {
	const {
		agentObj,
		selectedFeature,
		lintItemName,
		refetchAllFeatures,
		updateAllFeaturesState,
		setSelectedAgentParam,
		allFeatures,
		refetchAllComponents,
		updateComponent,
	} = props;

	const { t } = useTranslation();

	const [updateFeature] = useUpdateFeatureMutation();

	const isMounting3DExtendedRoiAgent = (lintItemName, agentParamName) => {
		if (lintItemName !== mountingInspection3D) return false;
		return _.includes(mounting3DExtendedRoiAgentParamNames, agentParamName);
	};

	const isSolderExtendedRoiAgent = (lintItemName, agentParamName) => {
		if (lintItemName !== solderInspection3D) return false;
		return _.includes(solder3DExtendedRoiAgentParamNames, agentParamName);
	};

	const isProfileRoiAgent = (agentParamName) => {
		return _.includes(profileRoiAgentParamNames, agentParamName);
	};

	const isMounting3DExtendedRoiEnabled = (agentObj, lineItemName) => {
		if (lineItemName !== mountingInspection3D) return false;
		for (const agentParamName of mounting3DExtendedRoiAgentParamNames) {
			if (!_.get(agentObj, `params.${agentParamName}.active`, false))
				return false;
		}
		return true;
	};

	const isSolder3DEnabled = (selectedFeature) => {
		return _.get(
			selectedFeature,
			`line_item_params.${solderInspection3D}.enabled`,
			false
		);
	};

	const submit = (newFeatureObj, lineItemName, agentParamName) => {
		const run = async (
			newFeatureObj,
			lineItemName,
			agentParamName,
			allFeatures
		) => {
			// if agent is lead2d/3d's shared param then also update the other lead dimension's param
			let modifiedFeatureObj = _.cloneDeep(newFeatureObj);
			if (
				lineItemName === leadInspection2D ||
				lineItemName === leadInspection3D
			) {
				if (_.includes(lead2d3dSharedAgentParams, agentParamName)) {
					const otherLineItemName =
						lineItemName === leadInspection2D
							? leadInspection3D
							: leadInspection2D;
					modifiedFeatureObj = _.set(
						modifiedFeatureObj,
						`line_item_params.${otherLineItemName}.params.${agentParamName}`,
						_.get(
							modifiedFeatureObj,
							`line_item_params.${lineItemName}.params.${agentParamName}`
						)
					);
				}
			}

			const res = await updateFeature({ body: modifiedFeatureObj, params: { allComponents: _.isInteger(modifiedFeatureObj.group_id) } });

			if (res.error) {
				aoiAlert(
					t('notification.error.updateFeature'),
					ALERT_TYPES.COMMON_ERROR
				);
				console.error('update feature failed', res.error.message);
				return;
			}

			// if (lineItemName === mountingInspection3D) {
			// if (lineItemName === leadInspection3D && agentParamName === lead3DExtTop) {
			//   // need to regenerate the component roi shape
			//   const {
			//     pMin,
			//     pMax,
			//     center: newComponentNoRotationCenter
			//   } = getComponentRectInfoByFeatures(, componentObj);
			// }

			// await updateAllFeaturesState([modifiedFeatureObj.feature_id], 'update', [modifiedFeatureObj]);
			if (!_.isEmpty(res.data)) {
				await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
			}
		};

		run(newFeatureObj, lineItemName, agentParamName, allFeatures);
	};

	return (
		<div
      className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4"
      key={selectedFeature.feature_id}
    >
			{_.map(
				_.orderBy(_.keys(_.get(agentObj, 'params')), (o) => {
					return orderAgentParams(o);
				}),
				(agentParamName, id) => {
				if (
					!isMounting3DExtendedRoiAgent(lintItemName, agentParamName) &&
					!isProfileRoiAgent(agentParamName) &&
					!isSolderExtendedRoiAgent(lintItemName, agentParamName) &&
					!is2DLeadColorAgentParams(lintItemName, agentParamName) &&
					!is2DLeadRangeAgentParams(lintItemName, agentParamName)
				) {
					return (
						<div
							className="grid self-stretch items-center grid-cols-[20px_120px_1fr] gap-1"
							key={id}
						>
							<div className="flex items-center self-stretch flex-1">
								{!_.get(agentObj, `params.${agentParamName}.required`, false) && agentParamName !== polarityRoi &&
									<Checkbox
										checked={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
										onClick={() => {
											let newFeatureObj = _.cloneDeep(selectedFeature);
											newFeatureObj = _.set(
												newFeatureObj,
												`line_item_params.${lintItemName}.params.${agentParamName}.active`,
												!_.get(agentObj, `params.${agentParamName}.active`, false)
											);

											// polarity roi and polarity check threshold's enable status are binded
											if (lintItemName === mountingInspection2D && _.includes([polarityCheckThreshold, polarityRoi], agentParamName)) {
												if (agentParamName === polarityCheckThreshold) {
													newFeatureObj = _.set(
														newFeatureObj,
														`line_item_params.${lintItemName}.params.${polarityRoi}.active`,
														!_.get(agentObj, `params.${agentParamName}.active`, false)
													);
												} else if (agentParamName === polarityRoi) {
													newFeatureObj = _.set(
														newFeatureObj,
														`line_item_params.${lintItemName}.params.${polarityCheckThreshold}.active`,
														!_.get(agentObj, `params.${agentParamName}.active`, false)
													);
												}
											}

											submit(newFeatureObj, lintItemName, agentParamName);
										}}
									/>
								}
							</div>
							<div className="flex items-center self-stretch flex-1 gap-2">
								<div className="flex items-center gap-1">
									<span
										className="font-source text-[12px] font-normal leading-[150%] pt-0.5 w-[90px] "
										style={{
											overflow: 'hidden',
											textOverflow: 'ellipsis',
											whiteSpace: 'nowrap',
										}}
										title={t(`agentParamName.${agentObj.name}.${agentParamName}`)}
									>
										{t(`agentParamName.${agentObj.name}.${agentParamName}`)}
									</span>
									{!_.isEmpty(
										_.get(
											text,
											`agentParamDesc.${lintItemName}.${agentParamName}`
										)
									) && (
										<Tooltip
											title={t(
												`agentParamDesc.${lintItemName}.${agentParamName}`
											)}
										>
											<img
												src="/icn/info_white.svg"
												alt="info"
												className="w-3 h-3"
											/>
										</Tooltip>
									)}
								</div>
								{_.includes(
									displayableAgentParams,
									`${lintItemName}.${agentParamName}`
								) && (
									<div
										className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
										style={{
											background: getColorByStr(
												`${lintItemName}.${agentParamName}`
											),
										}}
									/>
								)}
							</div>
							<div className='flex items-center self-stretch flex-1 gap-1'>
								{lintItemName === mountingInspection2D && _.includes(['threshold', 'defect_check_threshold', 'polarity_check_threshold'], agentParamName) &&
									<span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap'>
										{t('common.threshold')}
									</span>
								}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_range`, {})
								) &&
									!_.includes(
										doubleSidedSliderAgentParams,
										`${lintItemName}.${agentParamName}`
									) && (
										<AgentParamRange
											fieldInfo={_.get(
												agentObj,
												`params.${agentParamName}.param_range`,
												{}
											)}
											lintItemName={lintItemName}
											selectedFeature={selectedFeature}
											agentParamName={agentParamName}
											updateFeature={updateFeature}
											refetchAllFeatures={refetchAllFeatures}
											submit={submit}
											active={_.get(
												agentObj,
												`params.${agentParamName}.active`,
												false
											)}
										/>
									)}
								{_.includes(
									doubleSidedSliderAgentParams,
									`${lintItemName}.${agentParamName}`
								) && (
									<DoubleSidedSlider
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_range`,
											{}
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										updateFeature={updateFeature}
										refetchAllFeatures={refetchAllFeatures}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_roi`, {})
								) && (
									<AgentParamRoi
										selectedFeature={selectedFeature}
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_roi`,
											{}
										)}
										agentParamName={agentParamName}
										lintItemName={lintItemName}
										setSelectedAgentParam={setSelectedAgentParam}
										refetchAllFeatures={refetchAllFeatures}
										updateFeature={updateFeature}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
										updateAllFeaturesState={updateAllFeaturesState}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_int`, {})
								) && (
									<AgentParamInt
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_int`,
											{}
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										updateFeature={updateFeature}
										refetchAllFeatures={refetchAllFeatures}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_float`, {})
								) && (
									<AgentParamFloat
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_float`,
											{}
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										updateFeature={updateFeature}
										refetchAllFeatures={refetchAllFeatures}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{_.isBoolean(
									_.get(agentObj, `params.${agentParamName}.param_bool`, false)
								) && (
									<AgentParamBool
										updateFeature={updateFeature}
										refetchAllFeatures={refetchAllFeatures}
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_bool`,
											false
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{_.isString(
									_.get(agentObj, `params.${agentParamName}.param_string`, false)
								) && (
									<AgentParamString
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_string`,
											false
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_enum`, {})
								) && (
									<AgentParamEnum
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_enum`,
											{}
										)}
										lintItemName={lintItemName}
										selectedFeature={selectedFeature}
										agentParamName={agentParamName}
										updateFeature={updateFeature}
										refetchAllFeatures={refetchAllFeatures}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
							</div>
						</div>
					);
				}
			})}
			{lintItemName === leadInspection2D &&
				<Lead2DColorRangeParams
					selectedFeature={selectedFeature}
					lintItemName={lintItemName}
					submit={submit}
				/>
			}
			{isSolder3DEnabled(selectedFeature) && (
				<Fragment>
					<ExtendedRoi
						extLeft={_.get(agentObj, `params.ext_left`, {})}
						extRight={_.get(agentObj, `params.ext_right`, {})}
						extTop={_.get(agentObj, `params.ext_top`, {})}
						extBottom={_.get(agentObj, `params.ext_bottom`, {})}
						featureObj={selectedFeature}
						lintItemName={lintItemName}
						setSelectedAgentParam={setSelectedAgentParam}
						updateFeature={updateFeature}
						refetchAllFeatures={refetchAllFeatures}
						updateAllFeaturesState={updateAllFeaturesState}
					/>
					<ProfileRoi
						extWidth={_.get(agentObj, `params.profile_width`, {})}
						extHeight={_.get(agentObj, `params.profile_height`, {})}
						featureObj={selectedFeature}
						lintItemName={lintItemName}
						setSelectedAgentParam={setSelectedAgentParam}
						updateFeature={updateFeature}
						updateAllFeaturesState={updateAllFeaturesState}
					/>
				</Fragment>
			)}
			{lintItemName === mountingInspection3D && (
				<ExtendedRoi
					extLeft={_.get(agentObj, `params.ext_left`, {})}
					extRight={_.get(agentObj, `params.ext_right`, {})}
					extTop={_.get(agentObj, `params.ext_top`, {})}
					extBottom={_.get(agentObj, `params.ext_bottom`, {})}
					featureObj={selectedFeature}
					lintItemName={lintItemName}
					setSelectedAgentParam={setSelectedAgentParam}
					updateFeature={updateFeature}
					refetchAllFeatures={refetchAllFeatures}
					updateAllFeaturesState={updateAllFeaturesState}
				/>
			)}
		</div>
	);
};

const DoubleSidedSlider = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		// updateFeature,
		// refetchAllFeatures,
		submit,
		active,
	} = props;

	const [displayedOkMinValue, setDisplayedOkMinValue] = useState(
		_.get(fieldInfo, 'ok_min', 0)
	);
	const [displayedOkMaxValue, setDisplayedOkMaxValue] = useState(
		_.get(fieldInfo, 'ok_max', 1)
	);
  const okMinValRef = useRef(displayedOkMinValue);
  const okMaxValRef = useRef(displayedOkMaxValue);
	const curSelectedFeatureRef = useRef(selectedFeature);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_range: {
								..._.get(
									selectedFeature,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_range`
								),
								ok_min: value[0],
								ok_max: value[1],
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedOkMinValue(_.get(fieldInfo, 'ok_min', 0));
		setDisplayedOkMaxValue(_.get(fieldInfo, 'ok_max', 1));
    okMinValRef.current = _.get(fieldInfo, 'ok_min', 0);
    okMaxValRef.current = _.get(fieldInfo, 'ok_max', 1);
		curSelectedFeatureRef.current = selectedFeature;
	}, [fieldInfo, selectedFeature]);

  useEffect(() => {
    return () => {
      // if (okMaxValRef.current !== _.get(fieldInfo, 'ok_max', 1) || okMinValRef.current !== _.get(fieldInfo, 'ok_min', 0)) {
			if (okMaxValRef.current !== _.get(curSelectedFeatureRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_range.ok_max`, 0)) {
        const payload = getPayload(
          [okMinValRef.current, okMaxValRef.current],
          curSelectedFeatureRef.current,
          lintItemName,
          agentParamName
        );
        submit(payload, lintItemName, agentParamName);
      }
    }
	}, []);

	return (
		<div className="flex flex-col self-stretch w-[160px] gap-0.5">
			<div className="flex items-center gap-1">
				<div className="flex items-center gap-1">
					<span className="font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap">
						{t('common.min')}:
					</span>
					<InputNumber
						disabled={!active}
						size="small"
						style={{ width: '100%' }}
						controls={false}
						min={_.get(fieldInfo, 'min', -15)}
						max={_.get(fieldInfo, 'ok_max', 1)}
						value={active ? displayedOkMinValue : null}
						onChange={(value) => {
							if (value >= displayedOkMaxValue) return;
							setDisplayedOkMinValue(value);
              okMinValRef.current = value;
						}}
						onBlur={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[val, _.get(fieldInfo, 'ok_max')],
								selectedFeature,
								lintItemName,
								agentParamName
							);
							submit(payload, lintItemName, agentParamName);
						}}
						onPressEnter={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[val, _.get(fieldInfo, 'ok_max')],
								selectedFeature,
								lintItemName,
								agentParamName
							);
							submit(payload, lintItemName, agentParamName);
						}}
						step={0.001}
					/>
				</div>
			</div>
			<div className="flex items-center gap-1">
				<div className="flex items-center gap-1">
					<span className="font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap">
						{t('common.max')}:
					</span>
					<InputNumber
						disabled={!active}
						size="small"
						style={{ width: '100%' }}
						controls={false}
						min={_.get(fieldInfo, 'ok_min', 0)}
						max={_.get(fieldInfo, 'max', 15)}
						value={active ? displayedOkMaxValue : null}
						onChange={(value) => {
							if (value <= displayedOkMinValue) return;
							setDisplayedOkMaxValue(value);
              okMaxValRef.current = value;
						}}
						onBlur={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[_.get(fieldInfo, 'ok_min'), val],
								selectedFeature,
								lintItemName,
								agentParamName
							);
							submit(payload, lintItemName, agentParamName);
						}}
						onPressEnter={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[_.get(fieldInfo, 'ok_min'), val],
								selectedFeature,
								lintItemName,
								agentParamName
							);
							submit(payload, lintItemName, agentParamName);
						}}
						step={0.001}
					/>
				</div>
			</div>
			<Slider
				disabled={!active}
				range
				step={1}
				styles={{
					track: { background: '#81F499' },
					rail: { background: '#F46D6D' },
				}}
				className="custom-slider"
				style={{
					marginBottom: '10px',
				}}
				// style={{ width: '100%' }}
				min={_.get(fieldInfo, 'min', -15)}
				max={_.get(fieldInfo, 'max', 15)}
				value={[
					active ? displayedOkMinValue : null,
					active ? displayedOkMaxValue : null,
				]}
				onChange={(value) => {
					if (value[0] >= value[1]) {
						return;
					}
					setDisplayedOkMinValue(value[0]);
					setDisplayedOkMaxValue(value[1]);
          okMinValRef.current = value[0];
          okMaxValRef.current = value[1];
				}}
				onChangeComplete={(value) => {
					const payload = getPayload(
						value,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

// specical agent case(includes multi agent params)
const ExtendedRoi = (props) => {
	const {
		extLeft,
		extRight,
		extTop,
		extBottom,
		featureObj,
		lintItemName,
		setSelectedAgentParam,
		updateFeature,
		refetchAllFeatures,
		updateAllFeaturesState,
	} = props;

	const [enabled, setEnabled] = useState(false);

	useEffect(() => {
		if (lintItemName === mountingInspection3D) {
			setEnabled(
				_.get(extLeft, 'active', false) &&
					_.get(extRight, 'active', false) &&
					_.get(extTop, 'active', false) &&
					_.get(extBottom, 'active', false)
			);
		} else if (lintItemName === solderInspection3D) {
			setEnabled(
				_.get(extTop, 'active', false) && _.get(extBottom, 'active', false)
			);
		}
	}, [lintItemName, extLeft, extRight, extTop, extBottom]);

	return (
		<div className="grid self-stretch items-center grid-cols-[20px_120px_1fr] gap-1">
			<div className='flex items-center self-stretch flex-1'>
			</div>
			<div className="flex items-center self-stretch flex-1 gap-2">
				<span
					className="font-source text-[12px] font-normal leading-[150%] pt-0.5 overflow-hidden text-ellipsis whitespace-nowrap w-[90px]"
					title={t(`agentParamName.${lintItemName}.${extendedRoi}`)}
				>
					{t(`agentParamName.${lintItemName}.${extendedRoi}`)}
				</span>
				<div
					className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
					style={{
						background: getColorByStr(`${lintItemName}.${extendedRoi}`),
					}}
				/>
			</div>
			<Button
				disabled={!enabled}
				onClick={() => {
					setSelectedAgentParam(`${lintItemName}.${extendedRoi}`);
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setExtendedRoi')}
				</span>
			</Button>
		</div>
	);
};

const ProfileRoi = (props) => {
	const {
		extWidth,
		extHeight,
		featureObj,
		lintItemName,
		setSelectedAgentParam,
		updateFeature,
		updateAllFeaturesState,
	} = props;

	return (
		<div className="grid self-stretch items-center grid-cols-[20px_120px_1fr] gap-1">
			<div className='flex items-center self-stretch flex-1' />
			{/* <Checkbox
				checked={
					_.get(extWidth, 'active', false) && _.get(extHeight, 'active', false)
				}
				onClick={() => {
					const target = !(
						_.get(extWidth, 'active', false) &&
						_.get(extHeight, 'active', false)
					);
					let newFeatureObj = _.cloneDeep(featureObj);
					for (const agentParamName of profileRoiAgentParamNames) {
						newFeatureObj = _.set(
							newFeatureObj,
							`line_item_params.${lintItemName}.params.${agentParamName}.active`,
							target
						);
					}

					const submit = async (newFeatureObj) => {
						const res = await updateFeature(newFeatureObj);

						if (res.error) {
							aoiAlert(
								t('notification.error.updateFeature'),
								ALERT_TYPES.COMMON_ERROR
							);
							console.error('update feature failed', res.error.message);
							return;
						}

						// await refetchAllFeatures();
						await updateAllFeaturesState([newFeatureObj.feature_id], 'update', [newFeatureObj]);
					};

					submit(newFeatureObj);
				}}
			/> */}
			<div className="flex items-center self-stretch flex-1 gap-2">
				<span
					className="font-source text-[12px] font-normal leading-[150%] pt-0.5 overflow-hidden text-ellipsis whitespace-nowrap w-[90px]"
					title={t(`agentParamName.${lintItemName}.${profileRoi}`)}
				>
					{t(`agentParamName.${lintItemName}.${profileRoi}`)}
				</span>
				<div
					className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
					style={{
						background: getColorByStr(`${lintItemName}.${profileRoi}`),
					}}
				/>
			</div>
			<Button
				disabled={
					!(
						_.get(extWidth, 'active', false) &&
						_.get(extHeight, 'active', false)
					)
				}
				onClick={() => {
					setSelectedAgentParam(`${lintItemName}.${profileRoi}`);
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setProfileRoi')}
				</span>
			</Button>
		</div>
	);
};

// NOTE: not really dynamic
const AgentParamRoi = (props) => {
	const {
		fieldInfo,
		selectedFeature,
		agentParamName,
		lintItemName,
		setSelectedAgentParam,
		refetchAllFeatures,
		updateFeature,
		active,
		updateAllFeaturesState,
	} = props;

	const dispatch = useDispatch();
	const { t } = useTranslation();

	return (
		<div className="flex items-center gap-2 self-stretch">
			<Button
				disabled={!active}
				onClick={() => {
					if (_.get(fieldInfo, 'points', []).length > 0) {
						setSelectedAgentParam(`${lintItemName}.${agentParamName}`);
					} else {
						// submit update feature
						// we init the agent param roi at the center of the feature roi and set dimension to 1/2 of the feature roi
						// agent param roi's coord system is rotate by the feature roi's angle and its origin is at the rotated feature roi's top left
						// and we assume the agent param roi angle = feature roi angle
						let featureCenter = getComponentCenterByRoiDtoObj(
							_.get(selectedFeature, 'roi', {})
						);
						// featureCenter = rotatePoint(featureCenter, _.get(component, 'shape.angle'), getComponentCenterByRoiDtoObj(_.get(component, 'shape', {})));
						let featureTopLeft = {
							x:
								featureCenter.x -
								(_.get(selectedFeature, 'roi.points[1].x') -
									_.get(selectedFeature, 'roi.points[0].x') +
									1) /
									2,
							y:
								featureCenter.y -
								(_.get(selectedFeature, 'roi.points[1].y') -
									_.get(selectedFeature, 'roi.points[0].y') +
									1) /
									2,
						};
						featureTopLeft = rotatePoint(
							featureTopLeft,
							_.get(selectedFeature, 'roi.angle'),
							featureCenter
						);
						// let polarityCenter = rotatePoint(featureCenter, -_.get(selectedFeature, 'roi.angle'), featureTopLeft);
						let polarityCenter = _.cloneDeep(featureCenter);

						const featureRoiInnerDimension = {
							width:
								_.get(selectedFeature, 'roi.points[1].x') -
								_.get(selectedFeature, 'roi.points[0].x') +
								1,
							height:
								_.get(selectedFeature, 'roi.points[1].y') -
								_.get(selectedFeature, 'roi.points[0].y') +
								1,
						};

						const agentParamRoi = {
							type: 'obb',
							points: [
								{
									x: _.round(
										polarityCenter.x -
											featureRoiInnerDimension.width / 4 -
											featureTopLeft.x,
										0
									),
									y: _.round(
										polarityCenter.y -
											featureRoiInnerDimension.height / 4 -
											featureTopLeft.y,
										0
									),
								},
								{
									x: _.round(
										polarityCenter.x +
											featureRoiInnerDimension.width / 4 -
											featureTopLeft.x,
										0
									),
									y: _.round(
										polarityCenter.y +
											featureRoiInnerDimension.height / 4 -
											featureTopLeft.y,
										0
									),
								},
							],
							center: null,
							angle: _.get(selectedFeature, 'roi.angle', 0),
						};

						let payload = _.cloneDeep(selectedFeature);
						payload = _.set(
							payload,
							`line_item_params.${lintItemName}.params.${agentParamName}.param_roi`,
							agentParamRoi
						);

						const submit = async (payload) => {
							const res = await updateFeature({ body: payload, params: { allComponents: _.isInteger(payload.group_id) } });

							if (res.error) {
								aoiAlert(
									t('notification.error.updateFeature'),
									ALERT_TYPES.COMMON_ERROR
								);
								console.error('update feature failed', res.error.message);
								return;
							}

							setSelectedAgentParam(`${lintItemName}.${agentParamName}`);
							// await updateAllFeaturesState(
							// 	[payload.feature_id],
							// 	'update',
							// 	[payload]
							// );
							if (!_.isEmpty(res.data)) {
								await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
							}
						};

						submit(payload);
					}
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setRoi')}
				</span>
			</Button>
		</div>
	);
};

const AgentParamRange = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		// updateFeature,
		// refetchAllFeatures,
		submit,
		active,
	} = props;

	const dispatch = useDispatch();

	const { t } = useTranslation();

	const [displayedValue, setDisplayedValue] = useState(
		_.round(_.get(fieldInfo, 'ok_max', 0), 2)
	);

  const okMaxValRef = useRef(displayedValue);
	const selectedFeatureRef = useRef(selectedFeature);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_range: {
								..._.get(
									selectedFeature,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_range`
								),
								ok_max: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.round(_.get(fieldInfo, 'ok_max', 0), 2));
    okMaxValRef.current = _.round(_.get(fieldInfo, 'ok_max', 0), 2);
		selectedFeatureRef.current = selectedFeature;
	}, [fieldInfo, selectedFeature]);

  useEffect(() => {
    return () => {
      if (okMaxValRef.current !== _.round(_.get(selectedFeatureRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_range.ok_max`, 0), 2)) {
        const payload = getPayload(
          okMaxValRef.current,
          selectedFeatureRef.current,
          lintItemName,
          agentParamName
        );
        submit(payload, lintItemName, agentParamName);
      }
    }
  }, []);

	return (
		<div className="flex flex-1 self-stretch">
			<div className='flex flex-1 self-stretch items-center gap-1'>
				<InputNumber
					disabled={!active}
					style={{ width: '100%', height: '26px' }}
					controls={false}
					min={_.get(fieldInfo, 'min', 0)}
					max={_.get(fieldInfo, 'max', 1)}
					value={active ? displayedValue : null}
					onChange={(value) => {
						setDisplayedValue(value);
						okMaxValRef.current = value;
					}}
					onBlur={(e) => {
						const val = Number(e.target.value);
						const payload = getPayload(
							val,
							selectedFeature,
							lintItemName,
							agentParamName
						);
						submit(payload, lintItemName, agentParamName);
					}}
					onPressEnter={(e) => {
						const val = Number(e.target.value);
						const payload = getPayload(
							val,
							selectedFeature,
							lintItemName,
							agentParamName
						);
						submit(payload, lintItemName, agentParamName);
					}}
					step={0.01}
				/>
			</div>
			<div className='flex items-center self-stretch flex-1 gap-2'>
			<Slider
				disabled={!active}
				step={0.01}
				styles={{
					track: { background: '#81F499' },
					rail: { background: '#F46D6D' },
					}}
				className="custom-slider"
				style={{
					marginBottom: '10px',
					width: '100%',
				}}
				// style={{ width: '100%' }}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          okMaxValRef.current = value;
				}}
				onChangeComplete={(value) => {
					const payload = getPayload(
						value,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
			</div>
		</div>
	);
};

const AgentParamInt = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		updateFeature,
		refetchAllFeatures,
		submit,
		active,
	} = props;

	const dispatch = useDispatch();

	const [displayedValue, setDisplayedValue] = useState(
		_.get(fieldInfo, 'value', 0)
	);

  const displayedValRef = useRef(displayedValue);
	const curSelectedFeature = useRef(selectedFeature);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_int: {
								..._.get(
									selectedFeature,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_int`
								),
								value: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.get(fieldInfo, 'value', 0));
    displayedValRef.current = _.get(fieldInfo, 'value', 0);
		curSelectedFeature.current = selectedFeature;
	}, [fieldInfo, selectedFeature]);

  useEffect(() => {
    return () => {
      // if (displayedValRef.current !== _.get(fieldInfo, 'value', 0)) {
			if (displayedValRef.current !== _.get(curSelectedFeature.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_int.value`, 0)) {
        const payload = getPayload(
          displayedValRef.current,
          curSelectedFeature.current,
          lintItemName,
          agentParamName
        );
        submit(payload, lintItemName, agentParamName);
      }
    }
  }, []);

	return (
		<div className="flex flex-col self-stretch">
			<InputNumber
				disabled={!active}
				step={1}
				style={{ width: '100%' }}
				controls={false}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          displayedValRef.current = value;
				}}
				onBlur={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
				onPressEnter={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

const AgentParamFloat = (props) => {
        const {
                fieldInfo,
                lintItemName,
                selectedFeature,
                agentParamName,
                // updateFeature,
                // refetchAllFeatures,
                submit,
                active,
        } = props;

        const dispatch = useDispatch();
        const { data: systemMetadata } = useSelector((state) =>
                systemApi.endpoints.getSystemMetadata.select()(state)
        );

        let decimal = _.get(
                systemMetadata,
                `default_line_items.${lintItemName}.params.${agentParamName}.param_float.decimal_length`,
                4
        );
        if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
                decimal = mmValueDecimal;
        } else if (
                _.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
        ) {
                decimal = angleValueDecimal;
        }

        const [displayedValue, setDisplayedValue] = useState(
                _.round(_.get(fieldInfo, 'value', 0), decimal)
        );
  const displayedValRef = useRef(displayedValue);
	const curSelectedFeatureRef = useRef(selectedFeature);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_float: {
								..._.get(
									selectedFeature,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_float`
								),
								value: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

        useEffect(() => {
                setDisplayedValue(_.round(_.get(fieldInfo, 'value', 0), decimal));
    displayedValRef.current = _.round(_.get(fieldInfo, 'value', 0), decimal);
                curSelectedFeatureRef.current = selectedFeature;
        }, [fieldInfo, selectedFeature]);

  useEffect(() => {
    return () => {
      // if (displayedValRef.current !== _.round(_.get(fieldInfo, 'value', 0), 4)) {
                        if (displayedValRef.current !== _.round(_.get(curSelectedFeatureRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_float.value`, 0), decimal)) {
        const payload = getPayload(
          displayedValRef.current,
          curSelectedFeatureRef.current,
          lintItemName,
          agentParamName
        );
        submit(payload, lintItemName, agentParamName);
      }
    }
  }, []);

	return (
		<div className="flex flex-col self-stretch">
                        <InputNumber
                                disabled={!active}
                                step={Math.pow(10, -decimal)}
                                precision={decimal}
                                style={{ width: '100%' }}
                                controls={false}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          displayedValRef.current = value;
				}}
				onBlur={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
				onPressEnter={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

const AgentParamBool = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		// updateFeature,
		// refetchAllFeatures,
		submit,
		active,
	} = props;

	const dispatch = useDispatch();

	const [displayedValue, setDisplayedValue] = useState(fieldInfo);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_bool: value,
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(fieldInfo);
	}, [fieldInfo]);

	return (
		<div className="flex flex-col self-stretch">
			<Switch
				disabled={!active}
				style={{ width: '32px' }}
				size="small"
				checked={displayedValue}
				onChange={(value) => {
					setDisplayedValue(value);
					const payload = getPayload(
						value,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

const AgentParamString = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		submit,
		active,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(fieldInfo);
  const displayedValueRef = useRef(fieldInfo);
  const selectedFeatureRef = useRef(null);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_string: value,
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(fieldInfo);
    displayedValueRef.current = fieldInfo;
		selectedFeatureRef.current = selectedFeature;
	}, [fieldInfo, selectedFeature]);

  useEffect(() => {
    return () => {
      // prevent user click out of the input and onBlur won't be triggered
      // if (displayedValueRef.current !== fieldInfo) {
			if (displayedValueRef.current !== _.get(selectedFeatureRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_string`, '')) {
        const payload = getPayload(
          displayedValueRef.current,
          selectedFeatureRef.current,
          lintItemName,
          agentParamName
        );
        submit(payload, lintItemName, agentParamName);
      }
    };
  }, []);

	return (
		<div className="flex flex-col self-stretch">
			<Input
				disabled={!active}
				style={{ width: '100%' }}
				value={active ? displayedValue : null}
				onChange={(e) => {
					setDisplayedValue(e.target.value);
          displayedValueRef.current = e.target.value;
				}}
				onBlur={(e) => {
					const val = e.target.value;
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
				onMouseEnter={(e) => {
					const val = e.target.value;
					const payload = getPayload(
						val,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

const AgentParamEnum = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedFeature,
		agentParamName,
		submit,
		active,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(
		_.get(fieldInfo, 'value', '')
	);

	const getPayload = (value, selectedFeature, lintItemName, agentParamName) => {
		const payload = {
			...selectedFeature,
			line_item_params: {
				..._.get(selectedFeature, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedFeature, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedFeature,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedFeature,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_enum: {
								..._.get(
									selectedFeature,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_enum`
								),
								value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.get(fieldInfo, 'value', ''));
	}, [fieldInfo]);

	return (
		<div className="flex flex-col self-stretch">
			<Select
				disabled={!active}
				style={{ width: '100%' }}
				options={_.map(_.get(fieldInfo, 'options', []), (o) => {
					return {
						label: (
							<span className="font-source text-[12px] font-normal leading-[150%]">
								{o}
							</span>
						),
						value: o,
					};
				})}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
					const payload = getPayload(
						value,
						selectedFeature,
						lintItemName,
						agentParamName
					);
					submit(payload, lintItemName, agentParamName);
				}}
			/>
		</div>
	);
};

const Lead2DColorRangeParams = (props) => {
	const {
		selectedFeature,
		lintItemName,
		submit,
	} = props;

	const okMinValRef = useRef(null);
	const okMaxValRef = useRef(null);

	const [isSolderColorRangeModalOpen, setIsSolderColorRangeModalOpen] = useState(false);
	const [isPadColorRangeModalOpen, setIsPadColorRangeModalOpen] = useState(false);
	const [isTipColorRangeModalOpen, setIsTipColorRangeModalOpen] = useState(false);

	const [displayedOkMinValue, setDisplayedOkMinValue] = useState(
		_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_min`, 0)
	);
	const [displayedOkMaxValue, setDisplayedOkMaxValue] = useState(
		_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_max`, 1)
	);

	const [updateFeature] = useUpdateFeatureMutation();

	useEffect(() => {

	}, [selectedFeature]);

	return (
		<Fragment>
			<HSVColorRangePicker
				isOpened={isSolderColorRangeModalOpen}
				setIsOpened={setIsSolderColorRangeModalOpen}
				sh={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorStartHue}.param_int.value`, 0)}
				ss={100}
				sv={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorStartVal}.param_int.value`, 0)}
				eh={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorEndHue}.param_int.value`, 0)}
				es={100}
				ev={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorEndVal}.param_int.value`, 0)}
				ch={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorCenterHue}.param_int.value`, 0)}
				cs={_.get(selectedFeature, `line_item_params.${lintItemName}.params.${solderColorCenterSat}.param_int.value`, 0)}
				cv={100}
				submit={(
					sh,
					ss,
					sv,
					eh,
					es,
					ev,
					ch,
					cs,
					cv,
				) => {
					const payload = {
						...selectedFeature,
						line_item_params: {
							..._.get(selectedFeature, 'line_item_params'),
							[lintItemName]: {
								..._.get(selectedFeature, `line_item_params.${lintItemName}`),
								params: {
									..._.get(
										selectedFeature,
										`line_item_params.${lintItemName}.params`
									),
									[solderColorStartHue]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorStartHue}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorStartHue}.param_int`
											),
											value: sh,
										},
									},
									[solderColorStartVal]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorStartVal}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorStartVal}.param_int`
											),
											value: sv,
										},
									},
									[solderColorEndHue]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorEndHue}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorEndHue}.param_int`
											),
											value: eh,
										},
									},
									[solderColorEndVal]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorEndVal}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorEndVal}.param_int`
											),
											value: ev,
										},
									},
									[solderColorCenterHue]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorCenterHue}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorCenterHue}.param_int`
											),
											value: ch,
										},
									},
									[solderColorCenterSat]: {
										..._.get(
											selectedFeature,
											`line_item_params.${lintItemName}.params.${solderColorCenterSat}`
										),
										param_int: {
											..._.get(
												selectedFeature,
												`line_item_params.${lintItemName}.params.${solderColorCenterSat}.param_int`
											),
											value: cs,
										},
									},
								},
							},
						},
					};

					const run = async (payload) => {
						const res = await updateFeature(payload);

						if (res.error) {
							aoiAlert(
								t('notification.error.updateFeature'),
								ALERT_TYPES.COMMON_ERROR
							);
							console.error('update feature failed', res.error.message);
							return;
						}

						await updateAllFeaturesState([payload.feature_id], 'update', [payload]);
					};

					run(payload);
				}}
			/>
			<div className="grid self-stretch items-center grid-cols-[20px_120px_1fr] gap-1">
				<div className='flex items-center self-stretch flex-1' />
				<div className="flex items-center self-stretch flex-1 gap-2">
					<Button
						style={{ width: '100%' }}
						onClick={() => {
							setIsSolderColorRangeModalOpen(true);
						}}
					>
						<span className='font-source text-[12px] font-normal leading-[150%]'>
							{t('common.viewUpdateColorRange')}
						</span>
					</Button>
				</div>
				<div className='flex items-center self-stretch flex-1'>
					<Slider
						range
						step={0.001}
						styles={{
							track: { background: '#81F499' },
							rail: { background: '#F46D6D' },
						}}
						style={{
							marginBottom: '10px',
						}}
						// style={{ width: '100%' }}
						min={_.get(fieldInfo, 'min', -15)}
						max={_.get(fieldInfo, 'max', 15)}
						value={[
							displayedOkMinValue,
							displayedOkMaxValue,
						]}
						onChange={(value) => {
							if (value[0] >= value[1]) {
								return;
							}
							setDisplayedOkMinValue(value[0]);
							setDisplayedOkMaxValue(value[1]);
							okMinValRef.current = value[0];
							okMaxValRef.current = value[1];
						}}
						onChangeComplete={(value) => {
							// const payload = getPayload(
							// 	value,
							// 	selectedFeature,
							// 	lintItemName,
							// 	agentParamName
							// );
							// submit(payload, lintItemName, agentParamName);
						}}
					/>
				</div>
			</div>
		</Fragment>
	);
};

export default AgentParams;
