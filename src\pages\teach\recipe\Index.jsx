import { Tabs } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import PCBDetail from './PCBDetail';
import ConveyorSetup from './ConveyorSetup';
import PCBDimension from './PCBDimension/PCBDimension';
import FullCapture from './fullCaputre/FullCapture';
import _ from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { useReleaseConveyorControlMutation } from '../../../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';
import { setCameraAccessToken, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsProgrammingUsingConveyor } from '../../../reducer/setting';
import ConveyorRelatedReminderInProductDefine from '../../../modal/ConveyorRelatedReminderInProductDefine';
import { useReleaseCameraControlMutation } from '../../../services/camera';
import { store } from '../../../store';


const Recipe = (props) => {
  const {
    setIsConveyorControllerOpened,
    curProduct,
    refetchCurProduct,
    productId,
    activeTab,
    setActiveTab,
    setTeachTab,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [isPCBDetailCompeleted, setIsPCBDetailCompeleted] = useState(false);
  const [isConveyorSetupCompeleted, setIsConveyorSetupCompeleted] = useState(false);
  const [isPCBDimensionCompeleted, setIsPCBDimensionCompeleted] = useState(false);
  const [isFullPCBCaptureCompeleted, setIsFullPCBCaptureCompeleted] = useState(false);
  const [conveyorReminderOpened, setConveyorReminderOpened] = useState(false);

  const [releaseConveyorControl] = useReleaseConveyorControlMutation();
  const [releaseCameraControl] = useReleaseCameraControlMutation();

  // const isProgrammingUsingConveyor = useSelector((state) => state.setting.isProgrammingUsingConveyor);
  // const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);
  // const currentControlledConveyorSlotId = useSelector((state) => state.setting.currentControlledConveyorSlotId);
  // const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);

  useEffect(() => {
    if (_.isObject(curProduct)) {
      setIsPCBDetailCompeleted(_.isEmpty(_.get(curProduct, 'product_name', '')));
      setIsConveyorSetupCompeleted(_.isInteger(_.get(curProduct, 'conveyor_width_mm', 0)) && _.isInteger(_.get(curProduct, 'conveyor_width_mm', 0)) > 0);
      setIsPCBDimensionCompeleted(_.isInteger(_.get(curProduct, 'board_height_mm', 0)) && _.isInteger(_.get(curProduct, 'board_height_mm', 0)));
      setIsFullPCBCaptureCompeleted(!_.isEmpty(_.get(curProduct, 'inspectables', [])));
    } else {
      setIsPCBDetailCompeleted(false);
      setIsConveyorSetupCompeleted(false);
      setIsPCBDimensionCompeleted(false);
      setIsFullPCBCaptureCompeleted(false);
    }
  }, [curProduct]);

  useEffect(() => {
    return () => {
      const conveyorAccessToken = _.get(store.getState(), 'setting.conveyorAccessToken', '');
      const cameraAccessToken = _.get(store.getState(), 'setting.cameraAccessToken', '');
      const release = async (conveyorAccessToken, cameraAccessToken) => {
        if (!_.isEmpty(conveyorAccessToken)) {
          // auto release conveyor
          const res = await releaseConveyorControl(conveyorAccessToken);

          if (res.error) {
            aoiAlert(t('notification.error.releaseConveyorControl'), ALERT_TYPES.COMMON_ERROR);
            console.error('releaseConveyorControl error:', _.get(res, 'error.message', ''));
            return;
          }

          dispatch(setConveyorAccessToken(''));
          aoiAlert(t('notification.success.autoReleaseConveyorControl'), ALERT_TYPES.COMMON_INFO);
        }

        if (!_.isEmpty(cameraAccessToken)) {
          // auto release camera
          const cameraRes = await releaseCameraControl(cameraAccessToken);

          if (cameraRes.error) {
            aoiAlert(t('notification.error.releaseCameraControl'), ALERT_TYPES.COMMON_ERROR);
            console.error('releaseCameraControl error:', _.get(cameraRes, 'error.message', ''));
            return;
          }

          dispatch(setCameraAccessToken(''));
        }
        
        dispatch(setIsProgrammingUsingConveyor(false));
        dispatch(setCurrentControlledConveyorSlotId(null));
      };

      release(conveyorAccessToken, cameraAccessToken);
    };
  }, []);

  return (
    <Fragment>
      <ConveyorRelatedReminderInProductDefine
        isOpened={conveyorReminderOpened}
        setIsOpened={setConveyorReminderOpened}
      />
      <div className='flex items-center self-stretch h-[62px] border-b-[#4F4F4F] border-b-[1px]'>
        <Tabs
          style={{ width: '100%' }}
          activeKey={activeTab}
          onChange={(key) => {
            setActiveTab(key)
            setIsConveyorControllerOpened(key !== 'PCBDetail');
          }}
          type='card'
          items={[
            {
              label: <div className='flex py-2 gap-2 items-center justify-center'>
                <span className={`font-source text-[14px] font-${activeTab === 'PCBDetail' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.PCBDetail')}
                </span>
                {isPCBDetailCompeleted && (
                  <img src='/icn/checkFilledCircle_blue.svg' alt='checkFilledCircle' className='w-[14px] h-[14px]' />
                )}
              </div>,
              key: 'PCBDetail',
            },
            {
              label: <div className='flex py-2 gap-2 items-center justify-center'>
                <span className={`font-source text-[14px] font-${activeTab === 'conveyorSetup' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.conveyorSetup')}
                </span>
                {isConveyorSetupCompeleted && (
                  <img src='/icn/checkFilledCircle_blue.svg' alt='checkFilledCircle' className='w-[14px] h-[14px]' />
                )}
              </div>,
              key: 'conveyorSetup',
              // disabled: !_.isString(productId) || _.isEmpty(productId) || !_.isObject(curProduct),
              disabled: activeTab !== 'conveyorSetup',
            },
            {
              label: <div className='flex py-2 gap-2 items-center justify-center'>
                <span className={`font-source text-[14px] font-${activeTab === 'PCBDimension' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.PCBDimension')}
                </span>
                {isPCBDimensionCompeleted && (
                  <img src='/icn/checkFilledCircle_blue.svg' alt='checkFilledCircle' className='w-[14px] h-[14px]' />
                )}
              </div>,
              key: 'PCBDimension',
              // disabled: !_.isString(productId) || _.isEmpty(productId) || !_.isObject(curProduct),
              disabled: activeTab !== 'PCBDimension',
            },
            {
              label: <div className='flex py-2 gap-2 items-center justify-center'>
                <span className={`font-source text-[14px] font-${activeTab === 'fullPCBCapture' ? 'semibold' : 'normal'} leading-[normal]`}>
                  {t('productDefine.fullPCBCapture')}
                </span>
                {isFullPCBCaptureCompeleted && (
                  <img src='/icn/checkFilledCircle_blue.svg' alt='checkFilledCircle' className='w-[14px] h-[14px]' />
                )}
              </div>,
              key: 'fullPCBCapture',
              // disabled: !_.isString(productId) || _.isEmpty(productId) || !_.isObject(curProduct),
              disabled: activeTab !== 'fullPCBCapture',
            }
          ]}
        />
      </div>
      <div className='flex flex-1 px-0.2 self-stretch rounded-[6px]'>
        {activeTab === 'PCBDetail' && (
          <PCBDetail
            setActiveTab={setActiveTab}
            setIsConveyorControllerOpened={setIsConveyorControllerOpened}
            curProduct={curProduct}
            refetchCurProduct={refetchCurProduct}
            productId={productId}
            setConveyorReminderOpened={setConveyorReminderOpened}
          />
        )}
        {activeTab === 'conveyorSetup' && (
          <ConveyorSetup
            // setConveyorAccessToken={setConveyorAccessToken}
            // conveyorAccessToken={conveyorAccessToken}
            refetchCurProduct={refetchCurProduct}
            curProduct={curProduct}
            productId={productId}
            setActiveTab={setActiveTab}
          />
        )}
        {activeTab === 'PCBDimension' && (
          <PCBDimension
            curProduct={curProduct}
            refetchCurProduct={refetchCurProduct}
            productId={productId}
            setActiveTab={setActiveTab}
          />
        )}
        {activeTab === 'fullPCBCapture' && (
          <FullCapture
            curProduct={curProduct}
            refetchCurProduct={refetchCurProduct}
            setTeachTab={setTeachTab}
          />
        )}
      </div>
    </Fragment>
  );
};

export default Recipe;