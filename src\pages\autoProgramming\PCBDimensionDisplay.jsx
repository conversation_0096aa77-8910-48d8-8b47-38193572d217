import React, { useRef } from 'react';
import PCBDimensionViewer from '../../viewer/PCBDimensionViewer';


const PCBDimensionDisplay = (props) => {
  const {
    current2DUri,
    currentDepthImgUri,
    currentThumbnailUri,
    selectedTool,
    setSelectedTool,
    curProduct,
    refetchCurProduct,
    setCurBottemLeftPos,
    setCurTopRightPos,
    cameraPosition,
    handlePickPointUpdateCameraPos,
  } = props;

  const viewerContainerRef = useRef(null);

  return (
    <div className='relative w-full h-full bg-[#000]'>
      {/* scene starts */}
      <div
        className='absoulte w-full h-full top-0 left-0 '
        ref={viewerContainerRef}
      >
        <PCBDimensionViewer
          imageUri={current2DUri}
          thumbnailUri={currentThumbnailUri}
          depthImgUri={currentDepthImgUri}
          viewerContainerRef={viewerContainerRef}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          setCurTopRightPos={setCurTopRightPos}
          setCurBottemLeftPos={setCurBottemLeftPos}
          handlePickPointUpdateCameraPos={handlePickPointUpdateCameraPos}
          cameraPosition={cameraPosition}
        />
      </div>
      {/* scene ends */}
    </div>
  );
};

export default PCBDimensionDisplay;