import ThreeDBaseViewer from './ThreeDBaseViewer';
import * as THREE from 'three';
import { getObjectFromScene, loadAndDecodePoints } from './util';
import _ from 'lodash';


export default class HeightDiffViewer extends ThreeDBaseViewer {
  constructor(canvasRef, elementHeight, elementWidth) {
    super(canvasRef, elementHeight, elementWidth);
    this.scene = super.getScene();
    this.trackball = super.getTrackballControls();
    this.camera = super.getCamera();
    this.renderer = super.getRenderer();
    this.goldenCloudId = null;
    this.ipcCloudId = null;
    this.pcBoundingBoxMaxMin = {};
    this.pcCenter = null;
  };

  loadCroppedGoldenAndIpcCloud = async ({
    goldenCloudUri,
    ipcCloudUri,
  }) => {
    const material = new THREE.ShaderMaterial({
      uniforms: {
        size: { value: 0.3 },
      },
      vertexShader: `
        uniform float size;
        varying vec3 vColor;
        
        void main() {
            vColor = color.rgb;
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * (450.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
  
        void main() {
          gl_FragColor = vec4(vColor, 1.0);
        }
      `,
      transparent: true,
      vertexColors: true,
    });

    this.clearScene();

    const center = new THREE.Vector3();

    if (!_.isEmpty(goldenCloudUri)) {
      // golden
      try {
        const {
          positions: goldenPos,
          // colors: goldenCol,
        } = await loadAndDecodePoints(goldenCloudUri);
        // use purple for all golden's points
        const purpleColors = new Uint8Array(goldenPos.length); // Same length as positions
        for (let i = 0; i < goldenPos.length / 3; i++) {
          purpleColors[i * 3] = 187;     // Red channel
          purpleColors[i * 3 + 1] = 107; // Green channel
          purpleColors[i * 3 + 2] = 217; // Blue channel
          // rgb(187, 107, 217)
        }
        const goldenGeometry = new THREE.BufferGeometry();
        goldenGeometry.setAttribute('position', new THREE.Float32BufferAttribute(goldenPos, 3));
        goldenGeometry.setAttribute('color', new THREE.Uint8BufferAttribute(purpleColors, 3));
        goldenGeometry.attributes.color.normalized = true;

        const cloud = new THREE.Points(goldenGeometry, material);
        this.scene.add(cloud);
        this.goldenCloudId = cloud.id;

        goldenGeometry.computeBoundingBox();
        goldenGeometry.boundingBox.getCenter(center);
        this.pcBoundingBoxMaxMin = {
          max: goldenGeometry.boundingBox.max,
          min: goldenGeometry.boundingBox.min,
        };
        this.pcCenter = center;
      } catch (err) {
        console.error(err);
      }
    }

    if (!_.isEmpty(ipcCloudUri)) {
      // ipc
      try {
        const {
          positions: ipcPos,
          // colors: ipcCol,
        } = await loadAndDecodePoints(ipcCloudUri);
        const ipcGeometry = new THREE.BufferGeometry();
        // use yellow for all ipc's points
        const yellowColors = new Uint8Array(ipcPos.length); // Same length as positions
        for (let i = 0; i < ipcPos.length / 3; i++) {
          yellowColors[i * 3] = 242;     // Red channel
          yellowColors[i * 3 + 1] = 201; // Green channel
          yellowColors[i * 3 + 2] = 76; // Blue channel
          // rgb(242, 201, 76)
        }
        ipcGeometry.setAttribute('position', new THREE.Float32BufferAttribute(ipcPos, 3));
        ipcGeometry.setAttribute('color', new THREE.Uint8BufferAttribute(yellowColors, 3));
        ipcGeometry.attributes.color.normalized = true;

        const cloud = new THREE.Points(ipcGeometry, material);
        this.scene.add(cloud);
        this.ipcCloudId = cloud.id;
      } catch (err) {
        console.error(err);
      }
    }


    this.getCamera().position.set(this.pcCenter.x, this.pcCenter.y, this.pcBoundingBoxMaxMin.min.z - 20,);
    this.trackball.target.set(center.x, center.y, center.z);
    this.getCamera().lookAt(center.x, center.y, center.z);
  };

  updateCameraInZPlane = (desiredObjView) => {
    if (_.isEmpty(this.pcBoundingBoxMaxMin) || _.isEmpty(this.pcCenter)) return;
    // desiredObjView: front, back, left, right, top, bottom
    // camera's position z should be the same as the cloud's center z
    // and should be facing the cloud's center
    // place camera at the front, back, left, right of the pc bounding box
    // also ensure the pc is facing up in the camera view
    const camera = this.getCamera();
    // console.log(this.pcBoundingBoxMaxMin.max.y + 200, this.pcBoundingBoxMaxMin.min.y - 200);
    switch (desiredObjView) {
      case 'front':
        camera.position.set(
          this.pcCenter.x,
          this.pcBoundingBoxMaxMin.max.y + 20,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'back':
        camera.position.set(
          this.pcCenter.x,
          this.pcBoundingBoxMaxMin.min.y - 20,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'left':
        camera.position.set(
          this.pcBoundingBoxMaxMin.min.x - 20,
          this.pcCenter.y,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'right':
        camera.position.set(
          this.pcBoundingBoxMaxMin.max.x + 20,
          this.pcCenter.y,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'top':
        camera.position.set(
          this.pcCenter.x,
          this.pcCenter.y,
          this.pcBoundingBoxMaxMin.min.z - 20,
        );
        camera.up.set(0, -1, 0);
        break;
      default:
        return;
    }
    camera.lookAt(this.pcCenter.x, this.pcCenter.y, this.pcCenter.z);
    this.trackball.target.set(this.pcCenter.x, this.pcCenter.y, this.pcCenter.z);
    this.trackball.update();
    this.renderer.render(this.scene, camera);
  };

  updatePointCloudsVis = (isGoldenCloudVisible, isIpcCloudVisible) => {
    if (_.isInteger(this.goldenCloudId)) {
      getObjectFromScene(this.scene, this.goldenCloudId).visible = isGoldenCloudVisible;
    }
    if (_.isInteger(this.ipcCloudId)) {
      getObjectFromScene(this.scene, this.ipcCloudId).visible = isIpcCloudVisible;
    }
  };

  clearScene = () => {
    if (this.goldenCloudId) {
      this.scene.remove(getObjectFromScene(this.scene, this.goldenCloudId));
      this.goldenCloudId = null;
    }
    if (this.ipcCloudId) {
      this.scene.remove(getObjectFromScene(this.scene, this.ipcCloudId));
      this.ipcCloudId = null;
    }
  };
};