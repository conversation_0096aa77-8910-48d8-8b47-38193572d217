import { Button, Input } from 'antd';
import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { fieldConstraints, localStorageKeys, serverHost } from '../common/const';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useNavigate } from 'react-router-dom';
import _ from 'lodash';
import { decode } from 'jsonwebtoken';
import UpdateHost from '../modal/UpdateHost';


const Login = (props) => {
  const navigate = useNavigate();

  const { t } = useTranslation();

  const [username, setUsername] = useState('');
  const [pwd, setPwd] = useState('');
  const [isUpdateHostOpened, setIsUpdateHostOpened] = useState(false);

  const handleLogin = async (username, pwd) => {
    if (_.isEmpty(username) || _.isEmpty(pwd)) {
      aoiAlert(t('notification.error.pleaseFillInAllRequiredFields'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await fetch(`${serverHost}/account/authenticate`, {
      method: 'PUT',
      body: JSON.stringify({
        name: username,
        password: pwd,
      }),
    });

    if (!res.ok) {
      aoiAlert(t('notification.error.loginFailed'), ALERT_TYPES.COMMON_ERROR);
      console.error('Login failed');
      return;
    }

    const token = res.headers.get('Authorization');

    localStorage.setItem(localStorageKeys.username, username);
    localStorage.setItem(localStorageKeys.accessToken, token);
    const decoded = decode(_.split(token, ' ')[1]);
    localStorage.setItem(localStorageKeys.userRole, decoded.role);
    localStorage.setItem(localStorageKeys.tokenExp, decoded.exp);
    localStorage.setItem(localStorageKeys.accId, decoded.jti);

    // navigate('/home');
    window.location.href = '/home'; // force reload to apply new token
  };

  return (
    <Fragment>
      <UpdateHost
        isOpened={isUpdateHostOpened}
        setIsOpened={setIsUpdateHostOpened}
      />
      <div className='flex p-4 justify-center items-center gap-8 flex-1 self-stretch'>
        <div
          className='flex items-center justify-center self-stretch'
          style={{
            width: 'calc(50vw - 16px)',
          }}
        >
          <img
            style={{
              height: 'calc(100vh - 32px)',
              width: 'calc(50vw - 16px)',
            }}
            src='/img/landingBg_color.png'
            alt='landingBg'
            className='object-contain rounded-[10px]'
          />
        </div>
        <div className='flex items-center justify-center flex-1 self-stretch flex-col py-[48px] px-[140px] gap-8'>
          <div className='flex items-center self-stretch'>
            <div className='flex w-[54px] h-[54px] items-center justify-center rounded-[4px] bg-gray-1'>
              <img
                src='/icn/logo_white.svg'
                alt='logo'
              />
            </div>
          </div>
          <div className='flex flex-col items-center gap-8 self-stretch'>
            <div className='flex flex-col gap-2 self-stretch'>
              <span className='font-source text-[24px] font-semibold'>
                {t('common.login')}
              </span>
            </div>
            <div className='flex flex-col gap-2 self-stretch items-center'>
              <Input
                maxLength={fieldConstraints.login.username.maxLength}
                minLength={fieldConstraints.login.username.minLength}
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder={t('login.username')}
                onPressEnter={() => handleLogin(username, pwd)}
              />
              <Input.Password
                maxLength={fieldConstraints.login.password.maxLength}
                minLength={fieldConstraints.login.password.minLength}
                value={pwd}
                onChange={(e) => setPwd(e.target.value)}
                placeholder={t('login.password')}
                onPressEnter={() => handleLogin(username, pwd)}
              />
            </div>
            <div className='flex flex-col gap-2 self-stretch items-center'>
              <Button
                style={{ width: '100%' }}
                type='primary'
                onClick={() => {
                  handleLogin(username, pwd);
                }}
              >
                <span className='font-source text-[12px] font-semibold'>
                  {t('common.login')}
                </span>
              </Button>
              <Button
                style={{ width: '100%' }}
                onClick={() => {
                  setIsUpdateHostOpened(true);
                }}
              >
                <span className='font-source text-[12px] font-normal'>
                  {t('login.changeHostAddress')}
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default Login;