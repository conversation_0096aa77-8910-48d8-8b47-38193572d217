import React, { Fragment, useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Collapse, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useDeleteComponentTemplateMutation, useGetAllCategoriesQuery, useGetComponentTemplateDetailByIdQuery, useGetComponentTemplateQuery, useLazyGetComponentTemplateQuery } from '../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import CommonTable from '../components/CommonTable';
import { backendTimestampToDisplayString, convertBackendTimestampToMoment, getDimensionFromTemplateInfo } from '../common/util';
import PreviewComponentTemplateViewer from '../viewer/PreviewComponentTemplateViewer';
import DeletePrivateTemplateConfirmation from './DeletePrivateTemplateConfirmation';


const AddFromLibrary = (props) => {
  const {
    isOpened,
    setIsOpened,
    curProduct,
    setCurrentNewTemplateFromLibrary,
  } = props;
  
  const { t } = useTranslation();

  const [userInputQuery, setUserInputQuery] = useState('');
  const [displayedUserInput, setDisplayedUserInput] = useState('');
  const [activeKey, setActiveKey] = useState(null);
  const [displayedTemplates, setDisplayedTemplates] = useState([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isDeleteTemplateOpened, setIsDeleteTemplateOpened] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState(null);

  const { data: allCategories, refetch: refetchAllCategories } = useGetAllCategoriesQuery();
  const { data: allPrivateTemplates, refetch: refetchAllPrivateTemplates } = useGetComponentTemplateQuery({ builtin: false }); // for render all private templates' package no
  const [lazyGetComponentTemplate] = useLazyGetComponentTemplateQuery();
  const { data: currentTemplate } = useGetComponentTemplateDetailByIdQuery(selectedTemplateId); // NOTE: the coord in this info is in physical coord not in pixel coord
  const [deletePrivateTempalte] = useDeleteComponentTemplateMutation();

  const refetchDisplayedTemplates = async (userInputQuery, activeKey, category) => {
    const payload = {};

    // if (!_.isEmpty(userInputQuery) && activeKey !== '1') payload.term = userInputQuery;
    // if (!_.isEmpty(userInputQuery) && activeKey === '1') payload.category = userInputQuery;
    if (!_.isEmpty(category)) payload.category = category;

    if (!_.isEmpty(userInputQuery)) payload.term = userInputQuery;
    if (activeKey === '1') payload.builtin = true;
    if (activeKey === '2') payload.builtin = false;

    const res = await lazyGetComponentTemplate(payload);

    if (res.error) {
      aoiAlert(t('notification.error.getComponentTemplate'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    setDisplayedTemplates(res.data);
  };

  const handleDeleteTemplate = async (
    tempalteId,
    displayedUserInput,
    activeKey,
    selectedCategory,
  ) => {
    const res = await deletePrivateTempalte(tempalteId).unwrap();
    await refetchAllPrivateTemplates();
    await refetchDisplayedTemplates(displayedUserInput, activeKey, selectedCategory);
  };

  useEffect(() => {
    if (!isOpened) return;

    refetchAllCategories();
    refetchAllPrivateTemplates();
  }, [isOpened]);

  return (
    <Fragment>
    <CustomModal
      width={958}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('addFromLibrary.findTemplate')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch h-[643px]'>
        <div className='flex items-center flex-1 self-stretch border-b-[1px] border-b-gray-2'>
          <div className='flex w-[268px] flex-col self-stretch bg-[#ffffff08]'>
            <div className='flex p-4 flex-col gap-2 self-stretch border-b-[1px] border-b-gray-2'>
              <div className='flex p-1.5 items-center self-stretch'>
                <span className='font-source text-[16px] font-normal leading-[150%]'>
                  {t('addFromLibrary.componentLibrary')}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <Input
                  allowClear
                  style={{ width: '180px' }}
                  addonBefore={<SearchOutlined />}
                  value={displayedUserInput}
                  onChange={(e) => {
                    setDisplayedUserInput(e.target.value);
                  }}
                  // onBlur={() => {
                  //   setUserInputQuery(displayedUserInput);
                  //   refetchDisplayedTemplates(displayedUserInput);
                  // }}
                  onPressEnter={() => {
                    setUserInputQuery(displayedUserInput);
                    refetchDisplayedTemplates(displayedUserInput, activeKey, selectedCategory);
                  }}
                />
                <Button
                  style={{ height: '32px' }}
                  onClick={() => {
                    setUserInputQuery(displayedUserInput);
                    refetchDisplayedTemplates(displayedUserInput, activeKey, selectedCategory);
                  }}
                >
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {/* {activeKey === '1' ? t('productDefine.searchCategory') : t('common.search')} */}
                    {t('common.search')}
                  </span>
                </Button>
              </div>
            </div>
            <div className='flex flex-col self-stretch flex-1 overflow-auto'>
              <Collapse
                style={{ width: '100%' }}
                activeKey={activeKey}
                onChange={(key) => {
                  if (key.length === 0) {
                    setActiveKey(null);
                  } else if (key.length === 1) {
                    setActiveKey(key[0]);
                  } else {
                    setActiveKey(key[1]);
                  }
                }}
                items={[
                  {
                    key: '1',
                    label: <div className='flex items-center self-stretch gap-2'>
                      <img src='/icn/global_white.svg' alt='global' className='w-[14px] h-[14px]' />
                      <span className='font-source text-[14px] font-normal leading-[150%] pt-0.5'>
                        {t('addFromLibrary.public')}
                      </span>
                    </div>,
                    children: <div
                      className='flex flex-col self-stretchm overflow-auto'
                      style={{ height: 'calc(100vh - 264px - 62px - 40px - 112px - 32px - 32px - 32px)' }}
                    >
                      {_.map(allCategories, (c, idx) => (
                        <div
                          className={`flex h-[36px] py-2 px-4 gap-2 items-center self-stretch 
                          cursor-pointer ${c === selectedCategory ? 'bg-[#ffffff1a]' : ''}`}
                          key={idx}
                          onClick={() => {
                            // setUserInputQuery(c);
                            setSelectedCategory(c);
                            refetchDisplayedTemplates(displayedUserInput, activeKey, c);
                            // setDisplayedUserInput(c);
                          }}
                        >
                          <span className='font-source text-[14px] font-normal leading-[150%]'>
                            {c}
                          </span>
                        </div>
                      ))}
                    </div>,
                  },
                  {
                    key: '2',
                    label: <div className='flex items-center self-stretch gap-2'>
                      <img src='/icn/download_white.svg' alt='global' className='w-[14px] h-[14px]' />
                      <span className='font-source text-[14px] font-normal leading-[150%] pt-0.5'>
                        {t('addFromLibrary.private')}
                      </span>
                    </div>,
                    children: <div
                      className='flex flex-col self-stretch overflow-auto'
                      style={{ height: 'calc(100vh - 264px - 62px - 40px - 112px - 32px - 32px - 32px)' }}
                    >
                      {_.map(_.uniq(_.map(allPrivateTemplates, t => _.get(t, 'part_no'))), (pno, idx) => (
                        <div
                          className={`flex h-[36px] py-2 px-4 gap-2 items-center self-stretch 
                          cursor-pointer`}
                          key={idx}
                          onClick={() => {
                            setUserInputQuery(pno);
                            refetchDisplayedTemplates(pno, activeKey, selectedCategory);
                            setDisplayedUserInput(pno);
                          }}
                        >
                          <span className='font-source text-[14px] font-normal leading-[150%]'>
                            {pno}
                          </span>
                        </div>
                      ))}
                    </div>,
                  },
                ]}
              />
            </div>
          </div>
          <div className='flex flex-1 self-stretch'>
            <div className='flex p-3 flex-1 self-stretch'>
              <div className='flex py-2 flex-col gap-2 self-stretch flex-1'>
                <span className='font-source text-[14px] font-semibold leading-[normal]'>
                  {t('common.search')}: {userInputQuery}
                </span>
                <span className='font-source text-[12px] font-normal leading-[normal] text-gray-4'>
                  {displayedTemplates.length} {t('addFromLibrary.templatesFound')}
                </span>
                <div className='flex flex-col h-[487px] overflow-auto self-stretch'>
                  <CommonTable
                    cols={[
                      // {
                      //   title: <span className='font-source text-[10px] font-semibold leading-[150%] text-gray-4'>{t('allUpperCases.partNumber')}</span>,
                      //   render: (text, record) => (
                      //     <span className='font-source text-[12px] font-normal leading-[150%]'>
                      //       {_.get(record, 'part_no', '')}
                      //     </span>
                      //   ),
                      //   sorter: (a, b) => _.get(a, 'part_no', '').localeCompare(_.get(b, 'part_no', '')),
                      // },
                      {
                        title: <span className='font-source text-[10px] font-semibold leading-[150%] text-gray-4'>{t('allUpperCases.packageNo')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {_.get(record, 'package_no', '')}
                          </span>
                        ),
                      },
                      {
                        title: <span className='font-source text-[10px] font-semibold leading-[150%] text-gray-4'>{t('allUpperCases.createdAt')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {backendTimestampToDisplayString(_.get(record, 'created_at', ''))}
                          </span>
                        ),
                        sorter: (a, b) => convertBackendTimestampToMoment(b.created_at) - convertBackendTimestampToMoment(a.created_at),
                      },
                      {
                        title: <span className='font-source text-[10px] font-semibold leading-[150%] text-gray-4'>{t('common.action')}</span>,
                        render: (text, record) => {
                          if (!record.builtin) {
                            return <Button
                              type='text'
                              onClick={() => {
                                setTemplateToDelete(record);
                                setIsDeleteTemplateOpened(true);
                              }}
                            >
                              <span
                                className='font-source text-[12px] font-normal leading-[150%]'
                              >
                                {t('common.delete')}
                              </span>
                            </Button>
                          } return <></>;
                        },
                      }
                    ]}
                    onRow={(record, idx) => {
                      return {
                        onClick: (e) => {
                          setSelectedTemplateId(record.id);
                        },
                      };
                    }}
                    data={displayedTemplates}
                    total={displayedTemplates?displayedTemplates.length:0}
                    rowHoverable={true}
                  />
                </div>
              </div>
            </div>
            <div className='flex p-3 flex-col gap-8 self-stretch bg-[#ffffff08] w-[344px]'>
              { !_.isUndefined(currentTemplate) &&
                <Fragment>
                  <div className='flex flex-col self-stretch'>
                    <div className='flex py-0.5 items-center self-stretch'>
                      <span className='font-source text-[14px] font-semibold leading-[normal]'>
                        {t('addFromLibrary.componentInfo')}
                      </span>
                    </div>
                    <div className='flex py-2 flex-col gap-2 self-stretch'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('addFromLibrary.package')}: {_.get(currentTemplate, 'summary.package_no', '')}
                      </span>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('addFromLibrary.partNumber')}: {_.get(currentTemplate, 'summary.part_no', '')}
                      </span>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('addFromLibrary.description')}: {_.get(currentTemplate, 'summary.demo', '')}
                      </span>
                    </div>
                    <div className='flex py-2 flex-col gap-2 self-stretch'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('addFromLibrary.componentWidth')}: {_.round(_.get(getDimensionFromTemplateInfo(currentTemplate), 'width', 0), 2)} mm
                      </span>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('addFromLibrary.componentLength')}: {_.round(_.get(getDimensionFromTemplateInfo(currentTemplate), 'height', 0), 2)} mm
                      </span>
                    </div>
                  </div>
                  <div className='flex w-[320px] h-[240px] bg-[#000]'>
                    <PreviewComponentTemplateViewer
                      curProduct={curProduct}
                      templateInfo={currentTemplate}
                    />
                  </div>
                </Fragment>
              }
            </div>
          </div>
        </div>
        <div className='flex p-4 items-center gap-2 items-center justify-end self-stretch flex-1'>
          <Button
            type='primary'
            onClick={() => {
              if (_.isUndefined(currentTemplate)) {
                aoiAlert(t('notification.error.selectATemplateFirst'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              setCurrentNewTemplateFromLibrary(currentTemplate);
            }}
          >
            <span className='font-source text-[12px] font-semibold text-gray-1 leading-[150%]'>
              {t('addFromLibrary.useThisTemplate')}
            </span>
          </Button>
          <Button
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
    <DeletePrivateTemplateConfirmation
      isOpened={isDeleteTemplateOpened}
      setIsOpened={setIsDeleteTemplateOpened}
      templateName={templateToDelete?.package_no}
      onConfirm={() => {
        if (!templateToDelete) return;
        handleDeleteTemplate(
          templateToDelete.id,
          displayedUserInput,
          activeKey,
          selectedCategory,
        );
        setTemplateToDelete(null);
        setIsDeleteTemplateOpened(false);
      }}
    />
    </Fragment>
  );
};


export default AddFromLibrary;
