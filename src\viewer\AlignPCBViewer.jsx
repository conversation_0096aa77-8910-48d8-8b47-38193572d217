import React, { useCallback, useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { generalPan<PERSON><PERSON><PERSON>ouse<PERSON><PERSON><PERSON><PERSON><PERSON>, generalPanZoom<PERSON>ouse<PERSON>oveHandler, generalPanZoom<PERSON>ouse<PERSON>p<PERSON>andler, generalPanZoomMouseWheelHandler, getTwoDRectPminPmax, loadHighResolScene, loadInitFullSizeThumbnail, middlePanZoomMouseDownHandler, zoomPanToObject } from './util';
import { useGetImageMetaDataQuery } from '../services/camera';
import _ from 'lodash';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useTranslation } from 'react-i18next';
import { useAddFeatureMutation, useAddFeatureRoiMutation, useUpdateFeatureMutation, useUpdateFeatureRoiMutation } from '../services/product';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { highResoluRefreshInterval, newRectStrokeWidth } from '../common/const';


const AlignPCBViewer = (props) => {
  const {
    selectedTool,
    setSelectedTool,
    imageUri,
    depthUri,
    curProduct,
    refetchMarkers,
    selectedLocateFid,
    setSelectedLocateFid,
    selectedEditFid,
    setSelectedEditFid,
    markerFeatures,
    shapeChangeUpdate,
    featureIdsToDisplayCenterDot,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const containerRef = useRef(null);
  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const curSelectMarkerRectRef = useRef();
  const curRectInitMousePosRef = useRef();
  const curMarkerRectsRef = useRef([]);
  const isPanning = useRef(false);
  const displayedHighResolSceneRef = useRef();
  const thumbnailBgSceneRef = useRef();
  const templateMarkerCenterDotRef = useRef([]);

  const [curSelectMarkerInfo, setCurSelectMarkerInfo] = useState(null);

  const [addFeature] = useAddFeatureMutation();
  const [addFeatureRoi] = useAddFeatureRoiMutation();
  const [updateFeature] = useUpdateFeatureMutation();
  const [updateFeatureRoi] = useUpdateFeatureRoiMutation();
  const { data: curImageMetaData } = useGetImageMetaDataQuery({ uri: imageUri });

  const handleDrawBoxMouseDown = (opt) => {
    const zoom = fcanvasRef.current.getZoom();
    const newStrokeWidth = Math.min(newRectStrokeWidth, newRectStrokeWidth / zoom);
    const pointer = fcanvasRef.current.getPointer(opt.e);
    const rect = new fabric.Rect({
      left: pointer.x,
      top: pointer.y,
      width: 0,
      height: 0,
      fill: 'transparent',
      stroke: 'red',
      strokeWidth: newStrokeWidth,
      selectable: true,
      strokeUniform: true, // Ensure stroke width remains consistent when scaling
      evented: true,
    });

    curRectInitMousePosRef.current = {
      x: pointer.x,
      y: pointer.y,
    };
    curSelectMarkerRectRef.current = rect;

    fcanvasRef.current.add(curSelectMarkerRectRef.current);

    const mousePos = fcanvasRef.current.getPointer(opt.e, true);

    setCurSelectMarkerInfo({
      curBoxWidth: 0,
      curBoxHeight: 0,
      curMouseTop: mousePos.y,
      curMouseLeft: mousePos.x,
    });

    updateZIndex();
  };

  const handleDrawBoxMouseMove = (opt) => {
    if (!curSelectMarkerRectRef.current || !curRectInitMousePosRef.current) return;

    const pointer = fcanvasRef.current.getPointer(opt.e);
    const mousePos = fcanvasRef.current.getPointer(opt.e, true);

    // NOTE: keep rect's width and height positive when drawing and adjust the top left
    // ow the rect's left and right will be hidden for some reason 2024/10/22
    if (_.get(curRectInitMousePosRef.current, 'x') > pointer.x) {
      curSelectMarkerRectRef.current.set({
        left: pointer.x,
        width: curRectInitMousePosRef.current.x - pointer.x,
      })
    } else {
      curSelectMarkerRectRef.current.set({
        width: pointer.x - curRectInitMousePosRef.current.x,
        left: curRectInitMousePosRef.current.x,
      });
    }

    if (_.get(curRectInitMousePosRef.current, 'y') > pointer.y) {
      curSelectMarkerRectRef.current.set({
        top: pointer.y,
        height: curRectInitMousePosRef.current.y - pointer.y,
      });
    } else {
      curSelectMarkerRectRef.current.set({
        height: pointer.y - curRectInitMousePosRef.current.y,
        top: curRectInitMousePosRef.current.y,
      });
    }

    updateZIndex();

    setCurSelectMarkerInfo({
      curBoxWidth: curSelectMarkerRectRef.current.width,
      curBoxHeight: curSelectMarkerRectRef.current.height,
      curMouseTop: mousePos.y,
      curMouseLeft: mousePos.x,
    });
  };

  const handleDrawBoxMouseUp = () => {
    if (!curSelectMarkerRectRef.current) return;

    curSelectMarkerRectRef.current.setCoords();

    if (curSelectMarkerRectRef.current.width < 0) {
      curSelectMarkerRectRef.current.left += curSelectMarkerRectRef.current.width;
      curSelectMarkerRectRef.current *= -1;
    }
    if (curSelectMarkerRectRef.current.height < 0) {
      curSelectMarkerRectRef.current.top += curSelectMarkerRectRef.current.height;
      curSelectMarkerRectRef.current *= -1;
    }

    // get pmin pmax
    const { pMax, pMin } = getTwoDRectPminPmax(curSelectMarkerRectRef.current, curSelectMarkerRectRef.current.strokeWidth);

    // clear the rect
    fcanvasRef.current.remove(curSelectMarkerRectRef.current);
    curSelectMarkerRectRef.current = null;
    setCurSelectMarkerInfo(null);
    setSelectedTool('transform');
    updateZIndex();

    // submit
    const submit = async (pMin, pMax, curProduct) => {
      const res = await addFeatureRoi({
        body: {
          product_id: Number(_.get(curProduct, 'product_id', 0)),
          step: 0,
          // variant: _.get(_.first(_.get(curProduct, 'inspectables', [])), 'variant', ''),
          roi: {
            type: 'obb',
            points: [
              pMin,
              pMax,
            ],
            angle: 0,
            center: null
          },
          feature_type: 'marker',
          feature_scope: 'global',
          line_item_params: {
            aligner_2d: {
              enabled: true,
              name: "aligner_2d",
              agent_name: "aligner_2d",
              params: {
                marker_type: {
                  param_enum: {
                    value: "CIRCLE",
                    options: [
                        "CIRCLE",
                          "RECTANGLE",
                          "TEMPLATE"
                    ]
                  }
                }
                }
            }
          },
        },
      });

      if (res.error) {
        aoiAlert(t('notification.error.addMarker'), ALERT_TYPES.COMMON_ERROR);
        console.error('addFeature error:', _.get(res, 'error.message', ''));
        return;
      }

      const refetchRes = await refetchMarkers();

      if (refetchRes.error) {
        aoiAlert(t('notification.error.refetchMarkers'), ALERT_TYPES.COMMON_ERROR);
        console.error('refetchMarkers error:', _.get(refetchRes, 'error.message', ''));
        return;
      }
    };

    submit(pMin, pMax, curProduct);
  };

  const updateMarkerRectsStrokeWidthBasedOnZoom = (zoom) => {
    if (!fcanvasRef.current) return;

    const newStrokeWidth = Math.min(newRectStrokeWidth, newRectStrokeWidth / zoom);

    _.forEach(curMarkerRectsRef.current, (rect) => {
      rect.set('strokeWidth', newStrokeWidth);
    });

    updateZIndex();
  };

  const updateZIndex = () => {
    // bigger z-index called later
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    if (curSelectMarkerRectRef.current) curSelectMarkerRectRef.current.moveTo(3);
    if (templateMarkerCenterDotRef.current.length > 0) {
      for (const dot of templateMarkerCenterDotRef.current) {
        dot.moveTo(4);
      }
    }
    if (curMarkerRectsRef.current.length > 0) {
      _.forEach(curMarkerRectsRef.current, (rect) => {
        rect.moveTo(5);
      });
    }
    fcanvasRef.current.renderAll();
  };

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetaData]);

  const handleSwitchMode = (mode) => {
    if (!fcanvasRef.current) return;

    fcanvasRef.current.off('mouse:down');
    fcanvasRef.current.off('mouse:move');
    fcanvasRef.current.off('mouse:up');
    fcanvasRef.current.off('mouse:wheel');

    fcanvasRef.current.defaultCursor = mode === 'transform' ? 'default' : 'crosshair';

    fcanvasRef.current.on('mouse:wheel', (opt) => {
      generalPanZoomMouseWheelHandler(opt, fcanvasRef);
      updateMarkerRectsStrokeWidthBasedOnZoom(fcanvasRef.current.getZoom());
      if (_.isString(imageUri) && _.isString(depthUri)) delayLoadHighSoluScene({
        fcanvasRef,
        rawImageW: _.get(curImageMetaData, 'width'),
        rawImageH: _.get(curImageMetaData, 'height'),
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
      });
    });

    switch (mode) {
      case 'transform':
        fcanvasRef.current.on('mouse:down', (opt) => {
          generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanning);
        });
        fcanvasRef.current.on('mouse:move', (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning));
        fcanvasRef.current.on('mouse:up', () => {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanning);
          if (_.isString(imageUri) && _.isString(depthUri)) delayLoadHighSoluScene({
            fcanvasRef,
            rawImageW: _.get(curImageMetaData, 'width'),
            rawImageH: _.get(curImageMetaData, 'height'),
            displayedHighResolSceneRef,
            imageUri,
            depthUri,
          });
        });
        break;
      case 'select3DArea':
        fcanvasRef.current.on('mouse:down', (opt) => {
          if (opt.e && opt.e.button === 0) handleDrawBoxMouseDown(opt);
          if (opt.e && opt.e.button === 2) setSelectedTool('transform');
          if (opt.e && opt.e.button === 1) middlePanZoomMouseDownHandler(fcanvasRef, isPanning);
        });
        fcanvasRef.current.on('mouse:move', (opt) => {
          if (!isPanning.current) {
            handleDrawBoxMouseMove(opt);
          } else {
            generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning);
          }
        });
        fcanvasRef.current.on('mouse:up', (opt) => {
          if (!isPanning.current) handleDrawBoxMouseUp(opt);
          else {
            generalPanZoomMouseUpHandler(fcanvasRef, isPanning);
            if (_.isString(imageUri) && _.isString(depthUri)) delayLoadHighSoluScene({
              fcanvasRef,
              rawImageW: _.get(curImageMetaData, 'width'),
              rawImageH: _.get(curImageMetaData, 'height'),
              displayedHighResolSceneRef,
              imageUri,
              depthUri,
            });
          }
        });
        break;
      default:
        break;
    }

    fcanvasRef.current.renderAll();
  };

  const panZoomToFeature = (featureId) => {
    if (!fcanvasRef.current || curMarkerRectsRef.current.length === 0) return;
    const rect = curMarkerRectsRef.current.find((r) => String(_.get(r.get('featureObj'), 'feature_id')) === String(featureId));
    if (!rect) return;

    const rectCenter = new fabric.Point(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2
    );

    const canvasWidth = fcanvasRef.current.getWidth();
    const canvasHeight = fcanvasRef.current.getHeight();

    let zoom = Math.min(
      canvasWidth/ rect.width,
      canvasHeight / rect.height,
    );
    zoom -= zoom * 0.1;

    fcanvasRef.current.zoomToPoint(rectCenter, zoom);

    const newRectCenter = fabric.util.transformPoint(rectCenter, fcanvasRef.current.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + fcanvasRef.current.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + fcanvasRef.current.viewportTransform[5];

    // Apply the pan adjustment
    fcanvasRef.current.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    // const newWidth = Math.min(newRectStrokeWidth, newRectStrokeWidth / zoom);
    // const strokeWidthDelta = newWidth - curMarkerRectsRef.current[0].strokeWidth;
    // update the rect stroke width
    curMarkerRectsRef.current.forEach((rect) => {
      rect.set({
        // strokeWidth: newWidth,
        // left: rect.left - strokeWidthDelta,
        // top: rect.top - strokeWidthDelta,
        // width: rect.width + strokeWidthDelta,
        // height: rect.height + strokeWidthDelta,
        strokeWidth: newRectStrokeWidth,
        left: rect.left,
        top: rect.top,
        width: rect.width,
        height: rect.height,
      });
      rect.setCoords();
    });

    updateZIndex();
    delayLoadHighSoluScene({
      fcanvasRef,
      rawImageW: _.get(curImageMetaData, 'width'),
      rawImageH: _.get(curImageMetaData, 'height'),
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    });
  };

  const currentMarkerRects = () => {
    if (!fcanvasRef.current) return;

    fcanvasRef.current.discardActiveObject().renderAll();

    const existingMarkers = new Map();
    curMarkerRectsRef.current.forEach(obj => {
      if (obj.get('featureObj')) {
        const featureId = obj.get('featureObj').feature_id;
        if (!existingMarkers.has(featureId)) {
          existingMarkers.set(featureId, { rect: null, circle: null, polygon: null });
        }
        if (obj.type === 'rect') {
          existingMarkers.get(featureId).rect = obj;
        } else if (obj.type === 'circle') {
          existingMarkers.get(featureId).circle = obj;
        } else if (obj.type === 'polygon') {
          existingMarkers.get(featureId).polygon = obj;
        }
      }
    });

    curMarkerRectsRef.current = [];

    for (const m of markerFeatures) {
      const featureId = m.feature_id;
      const pMin = _.get(m, 'roi.points[0]');
      const pMax = _.get(m, 'roi.points[1]');
      const centerX = _.get(m, 'roi.center.x', null);
      const centerY = _.get(m, 'roi.center.y', null);
      const radius = _.get(m, 'roi.radius', newRectStrokeWidth * 2);

      let rect = existingMarkers.get(featureId)?.rect;
      let circle = existingMarkers.get(featureId)?.circle;
      let polygonShape = existingMarkers.get(featureId)?.polygon;
      let polygon = _.get(m, 'roi.polygon', null);


      if (!rect ||
          Math.abs(rect.left - (pMin.x - newRectStrokeWidth)) > 1 ||
          Math.abs(rect.top - (pMin.y - newRectStrokeWidth)) > 1 ||
          Math.abs(rect.width - (pMax.x - pMin.x + newRectStrokeWidth + 1)) > 1 ||
          Math.abs(rect.height - (pMax.y - pMin.y + newRectStrokeWidth + 1)) > 1) {

        if (rect) {
          fcanvasRef.current.remove(rect);
        }

        let newLeft = pMin.x;
        let newTop = pMin.y;
        let newWidth = pMax.x - pMin.x;
        let newHeight = pMax.y - pMin.y;

        newLeft -= newRectStrokeWidth;
        newTop -= newRectStrokeWidth;
        newWidth += newRectStrokeWidth + 1;
        newHeight += newRectStrokeWidth + 1;

        rect = new fabric.Rect({
          left: newLeft,
          top: newTop,
          width: newWidth,
          height: newHeight,
          fill: 'transparent',
          stroke: 'red',
          strokeWidth: newRectStrokeWidth,
          selectable: true,
          strokeUniform: true,
          evented: true,
        });

        rect.set('featureObj', m);

        rect.on('modified', () => {

          const rectFeatureId = rect.get('featureObj').feature_id;
          const circleToRemove = curMarkerRectsRef.current.find(obj =>
            obj.type === 'circle' && obj.get('featureObj') &&
            obj.get('featureObj').feature_id === rectFeatureId
          );

          if (circleToRemove) {
            fcanvasRef.current.remove(circleToRemove);
            const circleIndex = curMarkerRectsRef.current.indexOf(circleToRemove);
            if (circleIndex > -1) {
              curMarkerRectsRef.current.splice(circleIndex, 1);
            }
          }

          // update marker feature
          const { pMax, pMin } = getTwoDRectPminPmax(rect, rect.strokeWidth);

          const submit = async (pMin, pMax, featureObj) => {
            const res = await updateFeatureRoi({
              body: {
                ...featureObj,
                roi: {
                  type: 'obb',
                  points: [
                    pMin,
                    pMax,
                  ],
                  angle: 0,
                },
              },
              params: { allComponents: false },
            });

            if (res.error) {
              aoiAlert(t('notification.error.updateMarker'), ALERT_TYPES.COMMON_ERROR);
              console.error('updateFeature error:', _.get(res, 'error.message', ''));
              return;
            }

            await refetchMarkers();
          };

          submit(pMin, pMax, rect.get('featureObj'));
        });

        fcanvasRef.current.add(rect);
      }

      // Handle polygon drawing
      if (polygon && polygon.length > 0) {

        // Check if polygon needs to be recreated
        const needRecreatePolygon = !polygonShape ||
          !polygonShape.points ||
          polygonShape.points.length !== polygon.length ||
          polygon.some((point, index) =>
            !polygonShape.points[index] ||
            Math.abs(polygonShape.points[index].x - point.x) > 1 ||
            Math.abs(polygonShape.points[index].y - point.y) > 1
          );

        if (needRecreatePolygon) {
          if (polygonShape) {
            fcanvasRef.current.remove(polygonShape);
          }

          polygonShape = new fabric.Polygon(polygon, {
            fill: 'rgba(255,0,0)',
            stroke: 'red',
            strokeWidth: newRectStrokeWidth,
            selectable: false,
            strokeUniform: true,
            evented: false,
          });

          polygonShape.set('featureObj', m);

          // polygonShape.on('modified', () => {
          //   // Remove associated circle when polygon is modified
          //   const polygonFeatureId = polygonShape.get('featureObj').feature_id;
          //   const circleToRemove = curMarkerRectsRef.current.find(obj =>
          //     obj.type === 'circle' && obj.get('featureObj') &&
          //     obj.get('featureObj').feature_id === polygonFeatureId
          //   );

          //   if (circleToRemove) {
          //     fcanvasRef.current.remove(circleToRemove);
          //     const circleIndex = curMarkerRectsRef.current.indexOf(circleToRemove);
          //     if (circleIndex > -1) {
          //       curMarkerRectsRef.current.splice(circleIndex, 1);
          //     }
          //   }

          //   // Update polygon feature
          //   const submit = async (featureObj, newPoints) => {
          //     const res = await updateFeatureRoi({
          //       body: {
          //         ...featureObj,
          //         roi: {
          //           ...featureObj.roi,
          //           polygon: newPoints.map(p => ({ x: Math.round(p.x), y: Math.round(p.y) })),
          //         },
          //       },
          //       params: { allComponents: false },
          //     });

          //     if (res.error) {
          //       aoiAlert(t('notification.error.updateMarker'), ALERT_TYPES.COMMON_ERROR);
          //       console.error('updateFeature error:', _.get(res, 'error.message', ''));
          //       return;
          //     }

          //     await refetchMarkers();
          //   };

          //   submit(polygonShape.get('featureObj'), polygonShape.points);
          // });

          fcanvasRef.current.add(polygonShape);
        }
      }


      if (centerX && centerY) {
        if (!circle ||
            Math.abs(circle.left - centerX) > 1 ||
            Math.abs(circle.top - centerY) > 1 ||
            Math.abs(circle.radius - radius) > 1) {

          if (circle) {
            fcanvasRef.current.remove(circle);
          }

          circle = new fabric.Circle({
            left: centerX,
            top: centerY,
            originX: 'center',
            originY: 'center',
            radius: radius,
            fill: 'rgba(255,0,0)',
            stroke: 'red',
            strokeWidth: newRectStrokeWidth,
            selectable: false,
            evented: false,
            strokeUniform: true,
          });

          // 为圆圈添加featureId标识和featureObj
          circle.set('featureId', featureId);
          circle.set('featureObj', m);

          fcanvasRef.current.add(circle);
        }

        // Add all existing shapes to the array
        const shapesToAdd = [];
        if (rect) shapesToAdd.push(rect);
        if (polygonShape) shapesToAdd.push(polygonShape);
        if (circle) shapesToAdd.push(circle);
        curMarkerRectsRef.current.push(...shapesToAdd);
      } else {
        if (circle) {
          fcanvasRef.current.remove(circle);
        }

        // Add all existing shapes to the array
        const shapesToAdd = [];
        if (rect) shapesToAdd.push(rect);
        if (polygonShape) shapesToAdd.push(polygonShape);
        curMarkerRectsRef.current.push(...shapesToAdd);
      }
    }

    existingMarkers.forEach((objects, featureId) => {
      const stillExists = markerFeatures.some(m => m.feature_id === featureId);
      if (!stillExists) {
        if (objects.rect) fcanvasRef.current.remove(objects.rect);
        if (objects.circle) fcanvasRef.current.remove(objects.circle);
        if (objects.polygon) fcanvasRef.current.remove(objects.polygon);
      }
    });

    updateZIndex();
  };

  const loadTemplateMarkerCenterDot = (featureIds) => {
    templateMarkerCenterDotRef.current.forEach(obj => {
      fcanvasRef.current.remove(obj);
    });

    templateMarkerCenterDotRef.current = [];
    if (!featureIds) return;

    if (!fcanvasRef.current || _.isEmpty(curMarkerRectsRef.current)) return;

    for (const r of curMarkerRectsRef.current) {
      if (r.get('featureObj') && featureIds.includes(r.get('featureObj').feature_id)) {
        const center = r.getCenterPoint();
        console.log('center', center);
        const circle = new fabric.Circle({
          left: center.x,
          top: center.y,
          originX: 'center',
          originY: 'center',
          radius: 5,
          fill: 'red',
          stroke: 'red',
          strokeWidth: 1,
          selectable: false,
          evented: false,
          strokeUniform: true,
        });

        fcanvasRef.current.add(circle);
        templateMarkerCenterDotRef.current.push(circle);
      }
    }

    updateZIndex();
  };

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;

    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

  useEffect(() => {
    if (_.isNull(selectedLocateFid) || _.isEmpty(curMarkerRectsRef.current)) return;
    panZoomToFeature(selectedLocateFid);
    setSelectedLocateFid(null);
  }, [selectedLocateFid]);

  useEffect(() => {
    if (_.isNull(selectedEditFid) || _.isEmpty(curMarkerRectsRef.current) || !fcanvasRef.current) return;
    panZoomToFeature(selectedEditFid);
    // set active obj
    const rect = curMarkerRectsRef.current.find((r) => String(_.get(r.get('featureObj'), 'feature_id')) === String(selectedEditFid));
    if (rect) {
      fcanvasRef.current.setActiveObject(rect);
      fcanvasRef.current.renderAll();
    }
    setSelectedEditFid(null);
  }, [selectedEditFid]);

  // 监听形状变化更新
  useEffect(() => {
    if (!shapeChangeUpdate || !fcanvasRef.current) return;

    const { featureId, updatedMarker } = shapeChangeUpdate;

    // 更新curMarkerRectsRef中对应矩形的featureObj
    curMarkerRectsRef.current.forEach(obj => {
      if (obj.get && obj.get('featureObj') &&
          String(obj.get('featureObj').feature_id) === String(featureId)) {
        obj.set('featureObj', updatedMarker);
      }
    });
  }, [shapeChangeUpdate]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    loadTemplateMarkerCenterDot(featureIdsToDisplayCenterDot);
  }, [featureIdsToDisplayCenterDot]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    handleSwitchMode(selectedTool);
  }, [selectedTool]);

  useEffect(() => {
    currentMarkerRects();
  }, [markerFeatures]);

  useEffect(() => {
    if (!fcanvasRef.current) return;

    handleSwitchMode(selectedTool);
  }, [selectedTool]);

  useEffect(() => {
    if (!containerRef.current || !canvasElRef.current || _.isUndefined(curImageMetaData)) return;
    if (!_.isString(imageUri) || !_.isString(depthUri)) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        fireRightClick: true,
        stopContextMenu: true,
        fireMiddleClick: true,
      });
    } else {
      fcanvasRef.current.clear();
    }

    const fcanvas = fcanvasRef.current;

    fcanvas.setWidth(containerRef.current.offsetWidth);
    fcanvas.setHeight(containerRef.current.offsetHeight);

    handleSwitchMode('transform');

    const initLoadScene = async (imageUri, depthUri, curImageMetaData) => {
      await loadInitFullSizeThumbnail({
        fcanvas: fcanvasRef.current,
        rawWidth: _.get(curImageMetaData, 'width'),
        rawHeight: _.get(curImageMetaData, 'height'),
        thumbnailBgSceneRef,
        imageUri,
        depthUri,
        type: 'image',
      });
      await loadHighResolScene({
        fcanvasRef,
        rawImageW: _.get(curImageMetaData, 'width'),
        rawImageH: _.get(curImageMetaData, 'height'),
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
      });

      if (!_.isEmpty(markerFeatures)) {
        currentMarkerRects();
      }

      updateZIndex();

      resetView();
    };

    initLoadScene(imageUri, depthUri, curImageMetaData);
  }, [curImageMetaData]);

  return (
    <div
      className='w-full h-full'
      ref={containerRef}
    >
      <canvas ref={canvasElRef} />
    </div>
  );
};

export default AlignPCBViewer;