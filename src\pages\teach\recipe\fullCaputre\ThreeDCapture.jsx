import { But<PERSON>, Checkbox, InputNumber, Select, Slider } from 'antd';
import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { cameraModel } from '../../../../common/const';


const ThreeDCapture = (props) => {
  const {
    frame,
    setFrame,
    algorithmParams,
    setAlgorithmParams,
  } = props;

  const { t } = useTranslation();

  const connectCameraModel = 'aq';

  const [selectedPointcloudFilterTab , setSelectedPointcloudFilterTab] = useState('outlier');

  return (
    <div
      className='flex flex-col self-stretch overflow-auto gap-0.5 bg-[#ffffff0d]'
      style={{
        height: 'calc(100vh - 330px)',
      }}
    >
      {/* frame setting starts */}
      <div className='flex py-2 flex-col self-stretch border-b-[1px] border-b-[#ffffff1a]'>
        <div className='flex px-4 gap-1 flex-col self-stretch'>
          <div className='flex h-[34px] py-2 px-0.5 items-center self-stretch'>
            <span className='font-source text-[12px] font-semibold leading-[150%] tracking-[0.36px]'>
              {t('productDefine.frameSetting')}
            </span>
          </div>
          <div className='flex flex-col gap-1 self-stretch'>
            <div className='grid grid-cols-3 gap-1 items-center self-stretch grid-cols-[90px_1fr_60px]'>
              <div className="flex py-1 items-center flex-1 self-stretch">
                <span className="text-xs whitespace-nowrap">
                  {t('productDefine.exposureTime')}
                </span>
              </div>
              <div className='flex items-center gap-0.5 flex-1 self-stretch'>
                <Slider
                  style={{ width: '100%' }}
                  min={-1}
                  max={4}
                  step={1}
                  value={_.get(
                    frame,
                    'exposure_stop'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.exposure_stop = value;
                    setFrame(newFrame);
                  }}
                />
              </div>
              <div className="flex items-center gap-0.5 flex-1 self-stretch">
                <InputNumber
                  controls={false}
                  size="small"
                  value={_.get(
                    frame,
                    'exposure_stop'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.exposure_stop = value;
                    setFrame(newFrame);
                  }}
                  min={-1}
                  max={4}
                  step={1}
                />
              </div>
            </div>
            <div className='grid grid-cols-3 gap-1 items-center self-stretch grid-cols-[90px_1fr_60px]'>
              <div className="flex py-1 items-center flex-1 self-stretch">
                <span className="text-xs whitespace-nowrap">
                  {t('productDefine.brightness')}
                </span>
              </div>
              <div className='flex items-center gap-0.5 flex-1 self-stretch'>
                <Slider
                  style={{ width: '100%' }}
                  min={1}
                  max={3}
                  step={1}
                  value={_.get(
                    frame,
                    'brightness'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.brightness = value;
                    setFrame(newFrame);
                  }}
                />
              </div>
              <div className="flex items-center gap-0.5 flex-1 self-stretch">
                <InputNumber
                  controls={false}
                  size="small"
                  value={_.get(
                    frame,
                    'brightness'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.brightness = value;
                    setFrame(newFrame);
                  }}
                  min={1}
                  max={3}
                  step={1}
                />
              </div>
            </div>
            <div className='grid grid-cols-3 gap-1 items-center self-stretch grid-cols-[90px_1fr_60px]'>
              <div className="flex py-1 items-center flex-1 self-stretch">
                <span className="text-xs whitespace-nowrap">
                  {t('productDefine.gain')}
                </span>
              </div>
              <div className='flex items-center gap-0.5 flex-1 self-stretch'>
                <Slider
                  style={{ width: '100%' }}
                  min={0}
                  max={2}
                  step={1}
                  value={_.get(
                    frame,
                    'gain'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.gain = value;
                    setFrame(newFrame);
                  }}
                />
              </div>
              <div className="flex items-center gap-0.5 flex-1 self-stretch">
                <InputNumber
                  controls={false}
                  size="small"
                  value={_.get(
                    frame,
                    'gain'
                  )}
                  onChange={(value) => {
                    let newFrame = _.cloneDeep(frame);
                    newFrame.gain = value;
                    setFrame(newFrame);
                  }}
                  min={0}
                  max={2}
                  step={1}
                />
              </div>
            </div>
          </div>
          {/* <div className='flex py-3 items-center self-stretch'>
            <Button
              style={{ width: '100%' }}
            >
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.resetToDefault')}
              </span>
            </Button>
          </div> */}
        </div>
      </div>
      {/* frame setting ends */}
      {/* point cloud filter starts */}
      <div className='flex flex-col py-2 self-stretch px-4 gap-1'>
        <div className='flex h-[34px] py-2 px-0.5 items-center self-stretch'>
          <span className='font-source text-[12px] font-semibold leading-[150%] tracking-[0.36px]'>
            {t('productDefine.pointCloudFilter')}
          </span>
        </div>
        
        <div
          className='flex py-1 flex-col items-start gap-4 self-stretch'
          style={{
            // height: `${(window.innerHeight - 48 - 16) / 2 - 165 - 8 - 32}px`,
            // overflowY: 'auto',
            // width: '100%',
          }}
        >
          <div className='flex flex-col items-start gap-2 self-stretch'>
            <Select
              style={{ width: '100%' }}
              options={[
                {
                  label: <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.outlier')}
                  </span>,
                  value: 'outlier',
                },
                {
                  label: <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.smoothing')}
                  </span>,
                  value: 'smoothing',
                },
                {
                  label: <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.contrast')}
                  </span>,
                  value: 'contrast',
                },
                {
                  label: <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.correction')}
                  </span>,
                  value: 'correction',
                },
              ]}
              value={selectedPointcloudFilterTab}
              onChange={(value) => setSelectedPointcloudFilterTab(value)}
            />
          </div>
          <hr className='w-full border-[#ffffff1a]' />
          {/* outlier starts */}
          { selectedPointcloudFilterTab === 'outlier' &&
            <Fragment>
              {/* <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex items-center gap-4 self-stretch'>
                  <div className='flex items-center px-0.5 flex-1 gap-1'>
                    <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                      {t('productDefine.outlierStrength')}
                    </span>
                  </div>
                  <Select
                    style={{ width: '100%' }}
                    value={curOutlierStrength}
                    onChange={(value) => {
                      setCurOutlierStrength(value);
                      setAlgorithmParams({
                        ...algorithmParams,
                        ...getOutlierStrengthAlgorithmParams(value),
                      });
                    }}
                    options={[
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.off')}
                        </span>,
                        value: 'off',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.weak')}
                        </span>,
                        value: 'weak',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.normal')}
                        </span>,
                        value: 'normal',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.strong')}
                        </span>,
                        value: 'strong',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.customized')}
                        </span>,
                        value: 'customized',
                      },
                    ]}
                  />
                </div>
              </div> */}
              {/* <hr className='w-full border-[#ffffff1a]' /> */}
              <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex items-center gap-2 self-stretch'>
                  <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.outlierRemoval')}
                  </span>
                </div>
                <div className='flex flex-col items-start gap-1 self-stretch'>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_outlier', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_outlier = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curOutlierStrength !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal pt-0.5'>
                          {t('productDefine.outlierThresholdLevel')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={0}
                            max={10}
                            step={0.1}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_outlier') === false}
                            disabled={_.get(algorithmParams, 'enable_outlier') === false}
                            value={_.get(algorithmParams, 'outlier')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.outlier = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.outlier = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={10}
                            step={0.1}
                            value={_.get(algorithmParams, 'outlier')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.outlier = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_outlier') === false}
                            disabled={_.get(algorithmParams, 'enable_outlier') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_face_normal', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_face_normal = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curOutlierStrength !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal pt-0.5'>
                          {t('productDefine.faceNormalFilter')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={0}
                            max={40}
                            step={1}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_face_normal') === false}
                            disabled={_.get(algorithmParams, 'enable_face_normal') === false}
                            value={_.get(algorithmParams, 'face_normal_angle')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.face_normal_angle = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.face_normal_angle = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={40}
                            step={1}
                            value={_.get(algorithmParams, 'face_normal_angle')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.face_normal_angle = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_face_normal') === false}
                            disabled={_.get(algorithmParams, 'enable_face_normal') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_cluster_filter', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_cluster_filter = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curOutlierStrength !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal pt-0.5'>
                          {t('productDefine.clusterFilter')}
                        </span>
                      </div>
                      <div className='flex h-[26px] py-1 pl-8 items-center gap-2 self-stretch'>
                        <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.strength')}
                        </span>
                      </div>
                      <div className='flex h-[26px] pl-8 items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={0}
                            max={50}
                            step={1}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                            disabled={_.get(algorithmParams, 'enable_cluster_filter') === false}
                            value={_.get(algorithmParams, 'cluster_filter_strength')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_filter_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_filter_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={50}
                            step={1}
                            value={_.get(algorithmParams, 'cluster_filter_strength')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_filter_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                            disabled={_.get(algorithmParams, 'enable_cluster_filter') === false}
                          />
                        </div>
                      </div>
                      <div className='flex h-[26px] py-1 pl-8 items-center gap-2 self-stretch'>
                        <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.neighborDistance')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch pl-8'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                            step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                            max={connectCameraModel === cameraModel.ad ? 100 : 10}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                            disabled={_.get(algorithmParams, 'enable_cluster_filter') === false}
                            value={_.get(algorithmParams, 'cluster_neighbor_distance')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_neighbor_distance = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_neighbor_distance = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                            step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                            max={connectCameraModel === cameraModel.ad ? 100 : 10}
                            value={_.get(algorithmParams, 'cluster_neighbor_distance')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.cluster_neighbor_distance = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                            disabled={_.get(algorithmParams, 'enable_cluster_filter') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Fragment>
          }
          {/* outlier ends */}
          {/* smoothing starts */}
          { selectedPointcloudFilterTab === 'smoothing' &&
            <Fragment>
              {/* <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex items-center gap-4 self-stretch'>
                  <div className='flex items-center px-0.5 flex-1 gap-1'>
                    <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                      {t('productDefine.smoothingStrength')}
                    </span>

                  </div>
                  <Select
                    style={{ width: '100%' }}
                    value={curSmoothingStrength}
                    onChange={(value) => {
                      setCurSmoothingStrength(value);
                      setAlgorithmParams({
                        ...algorithmParams,
                        ...getSmoothingStrengthAlgorithmParams(value)
                      });
                    }}
                    options={[
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.off')}
                        </span>,
                        value: 'off',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.weak')}
                        </span>,
                        value: 'weak',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.normal')}
                        </span>,
                        value: 'normal',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.strong')}
                        </span>,
                        value: 'strong',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.customized')}
                        </span>,
                        value: 'customized',
                      },
                    ]}
                  />
                </div>
              </div>
              <hr className='w-full border-[#ffffff1a]' /> */}
              <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
              <div className='flex items-center gap-2 self-stretch'>
                  <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.smoothingSettings')}
                  </span>
                </div>
                <div className='flex flex-col items-start gap-1 self-stretch'>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_gaussian', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_gaussian = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curSmoothingStrength !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal pt-0.5'>
                          {t('productDefine.gaussianFilter')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={0}
                            max={40}
                            step={1}
                            // disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                            disabled={_.get(algorithmParams, 'enable_gaussian') === false}
                            value={_.get(algorithmParams, 'gaussian_strength')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.gaussian_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.gaussian_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={40}
                            step={1}
                            value={_.get(algorithmParams, 'gaussian_strength')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.gaussian_strength = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                            disabled={_.get(algorithmParams, 'enable_gaussian') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_median', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_median = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curSmoothingStrength !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal pt-0.5'>
                          {t('productDefine.medianFilter')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={3}
                            max={5}
                            step={2}
                            // disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_median') === false}
                            disabled={_.get(algorithmParams, 'enable_median') === false}
                            value={_.get(algorithmParams, 'median_kernel_size')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.median_kernel_size = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.median_kernel_size = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={3}
                            max={5}
                            step={2}
                            value={_.get(algorithmParams, 'median_kernel_size')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.median_kernel_size = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_median') === false}
                            disabled={_.get(algorithmParams, 'enable_median') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                        <div className='flex items-center gap-2 self-stretch'>
                          <Checkbox
                            size='small'
                            checked={_.get(algorithmParams, 'enable_smooth', false)}
                            onChange={(e) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.enable_smooth = e.target.checked;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curSmoothingStrength !== 'customized'}
                          />
                          <span className='font-source text-[12px] font-normal pt-0.5'>
                            {t('productDefine.smoothFilter')}
                          </span>
                        </div>
                        <Select
                          style={{ width: '78px', height: '26px' }}
                          // disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_smooth') === false}
                          disabled={_.get(algorithmParams, 'enable_smooth') === false}
                          popupMatchSelectWidth={false}
                          options={[
                            {
                              value: 'OneHundredth',
                              label: <span className='font-source text-[12px] font-normal'>0.01</span>,
                            },
                            {
                              value: 'FiveHundredths',
                              label: <span className='font-source text-[12px] font-normal'>0.05</span>,
                            },
                            {
                              value: 'OneTenth',
                              label: <span className='font-source text-[12px] font-normal'>0.1</span>,
                            },
                            {
                              value: 'Half',
                              label: <span className='font-source text-[12px] font-normal'>0.5</span>,
                            },
                            {
                              value: 'One',
                              label: <span className='font-source text-[12px] font-normal'>1.0</span>,
                            },
                            {
                              value: 'Two',
                              label: <span className='font-source text-[12px] font-normal'>2.0</span>,
                            },
                          ]}
                          size="small"
                          value={_.get(algorithmParams, 'smooth_granularity')}
                          onChange={(value) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.smooth_granularity = value;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Fragment>
          }
          {/* smoothing ends */}
          {/* contrast starts */}
          { selectedPointcloudFilterTab === 'contrast' &&
            <Fragment>
              {/* <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex items-center gap-4 self-stretch'>
                  <div className='flex items-center px-0.5 flex-1 gap-1'>
                    <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                      {t('productDefine.contrast')}
                    </span>

                  </div>
                  <Select
                    style={{ width: '100%' }}
                    value={curContrast}
                    onChange={(value) => {
                      setCurContrast(value);
                      setAlgorithmParams({
                        ...algorithmParams,
                        ...getContrastAlgorithmParams(value)
                      });
                    }}
                    options={[
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.off')}
                        </span>,
                        value: 'off',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.weak')}
                        </span>,
                        value: 'weak',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.normal')}
                        </span>,
                        value: 'normal',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.strong')}
                        </span>,
                        value: 'strong',
                      },
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.customized')}
                        </span>,
                        value: 'customized',
                      },
                    ]}
                  />
                </div>
              </div>
              <hr className='w-full border-[#ffffff1a]' /> */}
              <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex items-center gap-2 self-stretch'>
                  <span className='font-source text-[12px] font-normal'>
                    {t('productDefine.contrastSettings')}
                  </span>
                </div>
                <div className='flex flex-col items-start gap-1 self-stretch'>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      { _.get(algorithmParams, 'projector_pattern') === 'PhasePattern' &&
                        <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                          <Checkbox
                            size='small'
                            checked={_.get(algorithmParams, 'saturation', false)}
                            onChange={(e) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.saturation = e.target.checked;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curContrast !== 'customized'}
                          />
                          <span className='font-source text-[12px] font-normal'>
                            {t('productDefine.removeOverexposeRegion')}
                          </span>
                        </div>
                      }
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_intensity', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_intensity = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                          // disabled={curContrast !== 'customized'}
                        />
                        <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.intensityThreshold')}
                        </span>
                      </div>
                      <div className='flex h-[26px] items-center gap-2 self-stretch'>
                        <div className='flex flex-col flex-1'>
                          <Slider
                            min={0}
                            max={50}
                            step={1}
                            // disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_intensity') === false}
                            disabled={_.get(algorithmParams, 'enable_intensity') === false}
                            value={_.get(algorithmParams, 'intensity')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.intensity = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            onChangeComplete={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.intensity = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '42px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={50}
                            step={1}
                            value={_.get(algorithmParams, 'intensity')}
                            onChange={(v) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.intensity = Number(v);
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                            disabled={_.get(algorithmParams, 'enable_intensity') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                        <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                          <Checkbox
                            size='small'
                            checked={_.get(algorithmParams, 'enable_phase_quality', false)}
                            onChange={(e) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.enable_phase_quality = e.target.checked;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curContrast !== 'customized'}
                          />
                          <span className='font-source text-[12px] font-normal'>
                            {t('productDefine.removeLowQualityRegion')}
                          </span>
                        </div>
                        <div className='flex items-start gap-2 self-stretch'>
                          <InputNumber
                            style={{ width: '50px' }}
                            size='small'
                            controls={false}
                            min={0}
                            max={0.5}
                            step={0.01}
                            value={_.get(algorithmParams, 'phase_quality_threshold')}
                            onChange={(v) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.phase_quality_threshold = Number(v);
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                            // disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_phase_quality') === false}
                            disabled={_.get(algorithmParams, 'enable_phase_quality') === false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Fragment>
          }
          {/* contrast ends */}
          {/* correction starts */}
          { selectedPointcloudFilterTab === 'correction' &&
            <Fragment>
              <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                <div className='flex flex-col items-start gap-1 self-stretch'>
                  <div className='flex flex-col items-start gap-1 self-stretch'>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                        <Checkbox
                          size='small'
                          checked={_.get(algorithmParams, 'enable_fill_hole', false)}
                          onChange={(e) => {
                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                            newAlgorithmParams.enable_fill_hole = e.target.checked;
                            setAlgorithmParams(newAlgorithmParams);
                          }}
                        />
                        <span className='font-source text-[12px] font-normal'>
                          {t('productDefine.fillGaps')}
                        </span>
                      </div>
                      { _.get(algorithmParams, 'enable_fill_hole') &&
                        <div className='flex pl-8 items-center gap-2 self-stretch flex-col'>
                          <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {t('productDefine.holeSize')}
                            </span>
                          </div>
                          <div className='flex h-[26px] items-center gap-2 self-stretch'>
                            <div className='flex flex-col flex-1'>
                              <Slider
                                min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                max={connectCameraModel === cameraModel.ad ? 100 : 10}
                                step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                value={_.get(algorithmParams, 'hole_size', 1)}
                                onChange={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.hole_size = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                onChangeComplete={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.hole_size = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                              />
                            </div>
                            <div className='flex items-start gap-2 self-stretch'>
                              <InputNumber
                                style={{ width: '42px' }}
                                size='small'
                                controls={false}
                                min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                max={connectCameraModel === cameraModel.ad ? 100 : 10}
                                step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                value={_.get(algorithmParams, 'hole_size', 0)}
                                onChange={(v) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.hole_size = Number(v);
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                              />
                            </div>
                          </div>
                          <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {t('productDefine.depthDifference')}
                            </span>
                          </div>
                          <div className='flex h-[26px] items-center gap-2 self-stretch'>
                            <div className='flex flex-col flex-1'>
                              <Slider
                                min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                max={connectCameraModel === cameraModel.ad ? 100 : 10}
                                step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                value={_.get(algorithmParams, 'depth_diff', 1)}
                                onChange={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.depth_diff = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                onChangeComplete={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.depth_diff = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                              />
                            </div>
                            <div className='flex items-start gap-2 self-stretch'>
                              <InputNumber
                                style={{ width: '42px' }}
                                size='small'
                                controls={false}
                                min={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                max={connectCameraModel === cameraModel.ad ? 100 : 10}
                                step={connectCameraModel === cameraModel.ad ? 1 : 0.1}
                                value={_.get(algorithmParams, 'depth_diff', 1)}
                                onChange={(v) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.depth_diff = Number(v);
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                              />
                            </div>
                          </div>
                        </div>
                      }
                    </div>
                    <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-center'>
                      <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                        <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                          <Checkbox
                            size='small'
                            checked={_.get(algorithmParams, 'enable_contrast_distortion', false)}
                            onChange={(e) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.enable_contrast_distortion = e.target.checked;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                          <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                            {t('productDefine.contrastDistortion')}
                          </span>
                        </div>
                        <div className='flex flex-col flex-1 items-center justify-center'>
                          <Select
                            style={{ width: '100%' }}
                            size='small'
                            options={[
                              {
                                value: 'Correct',
                                label: <span className='font-source text-[12px] font-normal'>
                                  {t('productDefine.correct')}
                                </span>,
                              },
                              {
                                value: 'Remove',
                                label: <span className='font-source text-[12px] font-normal'>
                                  {t('productDefine.remove')}
                                </span>
                              },
                            ]}
                            value={_.get(algorithmParams, 'contrast_distortion_treatment', 'Correct')}
                            onChange={(value) => {
                              const newAlgorithmParams = _.cloneDeep(algorithmParams);
                              newAlgorithmParams.contrast_distortion_treatment = value;
                              setAlgorithmParams(newAlgorithmParams);
                            }}
                          />
                        </div>
                      </div>
                      { _.get(algorithmParams, 'enable_contrast_distortion') &&
                        <div className='flex pl-8 items-center gap-2 self-stretch flex-col'>
                          <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(algorithmParams, 'contrast_distortion_treatment', 'Correct') === 'Correct' ?
                                t('productDefine.correctLevel') :
                                t('productDefine.removeLevel')
                              }
                            </span>
                          </div>
                          <div className='flex h-[26px] items-center gap-2 self-stretch'>
                            <div className='flex flex-col flex-1'>
                              <Slider
                                min={0}
                                max={0.15}
                                step={0.01}
                                disabled={_.get(algorithmParams, 'enable_contrast_distortion') === false}
                                value={_.get(algorithmParams, 'contrast_distortion_strength')}
                                onChange={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.contrast_distortion_strength = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                onChangeComplete={(value) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.contrast_distortion_strength = value;
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                              />
                            </div>
                            <div className='flex items-start gap-2 self-stretch'>
                              <InputNumber
                                style={{ width: '60px' }}
                                size='small'
                                controls={false}
                                min={0}
                                max={0.15}
                                step={0.01}
                                value={_.get(algorithmParams, 'contrast_distortion_strength')}
                                onChange={(v) => {
                                  const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                  newAlgorithmParams.contrast_distortion_strength = Number(v);
                                  setAlgorithmParams(newAlgorithmParams);
                                }}
                                disabled={_.get(algorithmParams, 'enable_contrast_distortion') === false}
                              />
                            </div>
                          </div>
                        </div>
                      }
                    </div>
                  </div>
                </div>
              </div>
            </Fragment>
          }
          {/* correction ends */}
        </div>

      </div>
      {/* point cloud filter ends */}
    </div>
  );
};

export default ThreeDCapture;