import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button } from 'antd';


const ConveyorInUse = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const { t } = useTranslation();

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<div className='flex items-center gap-2'>
        <img src='/icn/warnCircleFilled_red.svg' alt='warn' className='w-4 h-4' />
        <span className='font-source text-[14px] font-semibold leading-[150%]'>
          {t('conveyorInUse.conveyorInUse')}
        </span>
      </div>}
      footer={null}
    >
      <div className='flex py-6 px-4 flex-col self-stretch'>
        <span className='font-source text-[14px] font-normal leading-[150%]'>
          {t('conveyorInUse.theSelectedConveyor')}
        </span>
      </div>
      <div className='flex p-4 gap-2 items-center self-stretch'>
        <Button
          onClick={() => setIsOpened(false)}
        >
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('common.cancel')}
          </span>
        </Button>
        <Button>
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('conveyorInUse.stopTaskAnd')}
          </span>
        </Button>
      </div>
    </CustomModal>
  )
};

export default ConveyorInUse;