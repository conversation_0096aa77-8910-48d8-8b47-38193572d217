import React, { Fragment, useEffect, useState } from 'react';
import Display from './Display';
import { useTranslation } from 'react-i18next';
import { But<PERSON>, Collapse, InputNumber } from 'antd';
import { DarkBlueDefaultButtonConfigProvider, GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../../../common/styledComponent';
import { useDispatch, useSelector } from 'react-redux';
import { useLazyGetCameraCaptureFrameUriQuery, useMoveCameraMutation } from '../../../../services/camera';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';
import { useMeasureProductDimensionMutation, useUpdateProductMutation } from '../../../../services/product';
import _ from 'lodash';


const PCBDimension = (props) => {
  const {
    curProduct,
    refetchCurProduct,
    productId,
    setActiveTab,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [cameraPosition, setCameraPosition] = useState({
    x: 0,
    y: 0,
    z: 0,
  });
  const [current2DUri, setCurrent2DUri] = useState('');
  const [currentDepthImgUri, setCurrentDepthImgUri] = useState('');
  const [currentThumbnailUri, setCurrentThumbnailUri] = useState('');
  const [selectedTool, setSelectedTool] = useState('transform'); // transform, selectBottomLeftPixel, selectTopRightPixel
  const [cameraBLPos, setCameraBLPos] = useState(null);
  const [cameraTRPos, setCameraTRPos] = useState(null);
  const [curBottemLeftPos, setCurBottemLeftPos] = useState(null);
  const [curTopRightPos, setCurTopRightPos] = useState(null);
  const [isSelectingDimensionPoint, setIsSelectingDimensionPoint] = useState(true);
  const [currentDimension, setCurrentDimension] = useState(null);

  const [moveCamera] = useMoveCameraMutation();
  const [getCameraCaptureFrameUri] = useLazyGetCameraCaptureFrameUriQuery();
  const [updateProduct] = useUpdateProductMutation();
  const [measureProductDimension] = useMeasureProductDimensionMutation();

  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);

  const handlePickPointUpdateCameraPos = (type, cameraPosition) => {
    switch (type) {
      case 'bl':
        setCameraBLPos(cameraPosition);
        break;
      case 'tr':
        setCameraTRPos(cameraPosition);
        break;
      default:
        break;
    }
  };

  const handleMoveCameraSubmit = async (x, y, z) => {
    if (!_.isNumber(x) || !_.isNumber(y) || !_.isNumber(z) || _.isEmpty(cameraAccessToken)) {
      return;
    }

    const res = await moveCamera({
      x,
      y,
      z,
      camera_access_token: cameraAccessToken,
    });

    if (res.error) {
      aoiAlert(t('notification.error.moveCamera'), ALERT_TYPES.COMMON_ERROR);
      console.error('moveCamera error:', _.get(res, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.moveCamera'), ALERT_TYPES.COMMON_INFO);
    return;
  };

  const handleTwoDCapture = async (cameraAccessToken) => {
    if (_.isEmpty(cameraAccessToken)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.cameraCapturing'))); 
  
    const res = await getCameraCaptureFrameUri({
      // camera_access_token: cameraAccessToken,
      camera_id: 0,
    });

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    if (res.error) {
      aoiAlert(t('notification.error.failToCapturePleaseEnsureCameraHasMovedToDesignatedPosition'), ALERT_TYPES.COMMON_ERROR);
      // aoiAlert(t('notification.error.getCameraCaptureFrameUri'), ALERT_TYPES.COMMON_ERROR);
      console.error('getCameraCaptureFrameUri error:', _.get(res, 'error.message', ''));
      return;
    }

    setCurrentDepthImgUri(_.get(res, 'data.depth_image.data_uri', ''));
    setCurrentThumbnailUri(_.get(res, 'data.thumbnail_image.data_uri', ''));
    setCurrent2DUri(_.get(res, 'data.image.data_uri', ''));
  };

  const handleCalculateDimension = async (curBottemLeftPos, curTopRightPos, cameraBLPos, cameraTRPos) => {
    if (_.isEmpty(curBottemLeftPos) || _.isEmpty(curTopRightPos) || _.isEmpty(cameraBLPos) || _.isEmpty(cameraTRPos)) {
      aoiAlert(t('notification.error.selectPCBDimensionPixel'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    // check if the selected bottom left is on the left of the top right
    // if (curBottemLeftPos.x >= curTopRightPos.x || curBottemLeftPos.y <= curTopRightPos.y) {
    //   aoiAlert(t('notification.error.invalidPCBDimensionPoint'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    // calc dimension
    const res = await measureProductDimension({
      "bl_cam_pos": {
        "x": cameraBLPos.x,
        "y": cameraBLPos.y,
        "z": cameraBLPos.z
      },
      "bl_corner_pos": {
        "x": curBottemLeftPos.x,
        "y": curBottemLeftPos.y
      },
      "tr_cam_pos": {
        "x": cameraTRPos.x,
        "y": cameraTRPos.y,
        "z": cameraTRPos.z
      },
      "tr_corner_pos": {
        "x": curTopRightPos.x,
        "y": curTopRightPos.y
      }
    });

    if (res.error) {
      aoiAlert(t('notification.error.measureProductDimension'), ALERT_TYPES.COMMON_ERROR);
      console.error('measureProductDimension error:', _.get(res, 'error.message', ''));
      return;
    }

    setCurrentDimension({
      width: _.get(res, 'data.board_width_mm', 0),
      height: _.get(res, 'data.board_height_mm', 0),
    });
  };

  const handleDimensionSubmit = async (curBottemLeftPos, curTopRightPos, productId, refetchCurProduct, currentDimension) => {
    if (_.isEmpty(currentDimension)) {
      aoiAlert(t('notification.error.selectPCBDimensionPixel'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await updateProduct({
      product_id: Number(productId),
      board_width_mm: _.get(currentDimension, 'width', 0),
      board_height_mm: _.get(currentDimension, 'height', 0),
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      console.error('updateProduct error:', _.get(res, 'error.message', ''));
      return;
    }

    await refetchCurProduct();
    setActiveTab('fullPCBCapture');
  };

  useEffect(() => {
    setIsSelectingDimensionPoint(!_.isInteger(_.get(curProduct, 'board_width_mm')) || !_.isInteger(_.get(curProduct, 'board_height_mm')));
    if (_.isInteger(_.get(curProduct, 'board_width_mm')) && _.isInteger(_.get(curProduct, 'board_height_mm'))) {
      setCurrentDimension({
        width: _.get(curProduct, 'board_width_mm', 0),
        height: _.get(curProduct, 'board_height_mm', 0),
      });
    }
  }, [curProduct]);

  return (
    <div className='flex gap-0.5 flex-1 px-0.5 self-stretch rounded-[6px]'>
      <div className='flex w-[320px] flex-col self-stretch bg-[#ffffff0d]'>
        { _.isEmpty(currentDimension) ?
          <div className='flex p-4 flex-col gap-4 self-stretch justify-center'>
            <div className='flex gap-4 flex-col self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {t('productDefine.measurePCBDimension')}
              </span>
              {isSelectingDimensionPoint &&
                <Collapse
                  defaultActiveKey={['1']}
                  items={[
                    {
                      key: '1',
                      label: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-6'>
                        {`${t('productDefine.instructions')}:`}
                      </span>,
                      children: <div className='flex flex-col self-stretch'>
                        <div className='flex py-4 px-2'>
                          <img src='/img/PCBDimensionInstruction_color.svg' alt='PCBDimensionInstruction' className='w-[100%] h-[100%]' />
                        </div>
                        <div
                          className='flex p-2 flex-col self-stretch'
                        >
                          <h1 className='list-item font-source text-[12px] font-normal leading-[150%] text-gray-6 list-disc ml-[1rem]'>
                            {t('productDefine.instructionStep1')}
                          </h1>
                          <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                            {t('productDefine.instructionStep2')} 
                          </h1>
                          <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                            {t('productDefine.instructionStep3')}
                          </h1>
                          <h1 className='font-source text-[12px] font-normal leading-[150%] text-gray-6 list-item list-disc ml-[1rem]'>
                            {t('productDefine.instructionStep4')}
                          </h1>
                        </div>
                      </div>,
                    }
                  ]}
                />
              }
            </div>
            {isSelectingDimensionPoint &&
              <Fragment>
                <div className='flex gap-2 flex-col self-stretch'>
                  <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
                    {t('productDefine.inputCameraPosition')}
                  </span>
                  <div className='flex gap-4 items-center self-stretch'>
                    <div className='flex gap-2 items-center py-1 self-stretch flex-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        X:
                      </span>
                      <InputNumber
                        controls={false}
                        style={{ width: '100%' }}
                        value={cameraPosition.x}
                        onChange={(value) => setCameraPosition({ ...cameraPosition, x: value })}
                      />
                    </div>
                    <div className='flex gap-2 items-center py-1 self-stretch flex-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        Y:
                      </span>
                      <InputNumber
                        controls={false}
                        style={{ width: '100%' }}
                        value={cameraPosition.y}
                        onChange={(value) => setCameraPosition({ ...cameraPosition, y: value })}
                      />
                    </div>
                    <div className='flex gap-2 items-center py-1 self-stretch flex-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        Z:
                      </span>
                      <InputNumber
                        controls={false}
                        style={{ width: '100%' }}
                        value={cameraPosition.z}
                        onChange={(value) => setCameraPosition({ ...cameraPosition, z: value })}
                      />
                    </div>
                  </div>
                  <GreenDefaultButtonConfigProvider>
                    <Button
                      onClick={() => handleMoveCameraSubmit(cameraPosition.x, cameraPosition.y, cameraPosition.z)}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.moveCamera')}
                      </span>
                    </Button>
                  </GreenDefaultButtonConfigProvider>
                  <GreenPrimaryButtonConfigProvider>
                    <Button
                      type='primary'
                      onClick={() => {
                        handleTwoDCapture(cameraAccessToken);
                      }}
                    >
                      <span className='font-source text-[12px] font-semibold leading-[150%]'>
                        {t('productDefine.capture2DImage')}
                      </span>
                    </Button>
                  </GreenPrimaryButtonConfigProvider>
                </div>
                <div className='flex gap-2 flex-col self-stretch'>
                  <div className='flex items-center py-1 gap-1'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {`${t('productDefine.PCBDimension')}:`}
                    </span>
                    { _.isInteger(_.get(curProduct, 'board_width_mm')) && _.isInteger(_.get(curProduct, 'board_height_mm')) &&
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {`${t('common.lastSaved')}: ${_.get(curProduct, 'board_width_mm')}mm x ${_.get(curProduct, 'board_height_mm')}mm`}
                      </span>
                    }
                  </div>
                  <div className='flex flex-col self-stretch'>
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '90px 1fr 1fr',
                        gap: '8px',
                        padding: '8px 0',
                        alignItems: 'center',
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] flex-1 whitespace-nowrap'>
                        {t('productDefine.bottomLeftPixel')}:
                      </span>
                      <div className='py-1 px-2 h-[26px] flex items-center justify-center flex-1 bg-[#ffffff0d] rounded-[4px] self-stretch'>
                        <span className='font-source text-[12px] font-normal leading-[normal] whitespace-nowrap'>
                          {_.isEmpty(curBottemLeftPos) ? t('common.undefined') : `${curBottemLeftPos.x}, ${curBottemLeftPos.y}`}
                        </span>
                      </div>
                      <div className='flex items-center flex-1 self-stretch h-[26px]'>
                        {_.isEmpty(curBottemLeftPos) ?
                          <DarkBlueDefaultButtonConfigProvider>
                            <Button
                              style={{ width: '100%' }}
                              onClick={() => {
                                if (_.isEmpty(current2DUri)) {
                                  aoiAlert(t('notification.error.pleaseCapture2DImageFirst'), ALERT_TYPES.COMMON_ERROR);
                                  return;
                                }
                                setSelectedTool('selectBottomLeftPixel');
                              }}
                            >
                              <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                                <img src='/icn/locator_blue.svg' alt='locator' className='w-3 h-3' />
                                <span className='font-source text-[12px] font-normal text-AOI-blue pt-0.5'>
                                  {t('productDefine.selectPixel')}
                                </span>
                              </div>
                            </Button>
                          </DarkBlueDefaultButtonConfigProvider>
                        :
                          <DarkBlueDefaultButtonConfigProvider>
                            <Button
                              style={{ width: '100%' }}
                              onClick={() => {
                                setCurBottemLeftPos(null);
                              }}
                            >
                              <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                                <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                  {t('common.reset')}
                                </span>
                              </div>
                            </Button>
                          </DarkBlueDefaultButtonConfigProvider>
                        }
                      </div>
                    </div>
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns: '90px 1fr 1fr',
                        gap: '8px',
                        padding: '8px 0',
                        alignItems: 'center',
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] flex-1 whitespace-nowrap'>
                        {t('productDefine.topRightPixel')}:
                      </span>
                      <div className='py-1 px-2 h-[26px] flex items-center justify-center flex-1 bg-[#ffffff0d] rounded-[4px] self-stretch'>
                        <span className='font-source text-[12px] font-normal leading-[normal] whitespace-nowrap'>
                          {_.isEmpty(curTopRightPos) ? t('common.undefined') : `${curTopRightPos.x}, ${curTopRightPos.y}`}
                        </span>
                      </div>
                      <div className='flex items-center flex-1 self-stretch h-[26px]'>
                        {_.isEmpty(curTopRightPos) ?
                          <DarkBlueDefaultButtonConfigProvider>
                            <Button
                              style={{ width: '100%' }}
                              onClick={() => {
                                if (_.isEmpty(current2DUri)) {
                                  aoiAlert(t('notification.error.pleaseCapture2DImageFirst'), ALERT_TYPES.COMMON_ERROR);
                                  return;
                                }
                                setSelectedTool('selectTopRightPixel');
                              }}
                            >
                              <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                                <img src='/icn/locator_blue.svg' alt='locator' className='w-3 h-3' />
                                <span className='font-source text-[12px] font-normal text-AOI-blue pt-0.5'>
                                  {t('productDefine.selectPixel')}
                                </span>
                              </div>
                            </Button>
                          </DarkBlueDefaultButtonConfigProvider>
                          :
                          <DarkBlueDefaultButtonConfigProvider>
                            <Button
                              style={{ width: '100%' }}
                              onClick={() => {
                                setCurTopRightPos(null);
                              }}
                            >
                              <div className='flex items-center gap-1 justify-center self-stretch px-2'>
                                <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                  {t('common.reset')}
                                </span>
                              </div>
                            </Button>
                          </DarkBlueDefaultButtonConfigProvider>
                        }
                      </div>
                    </div>
                  </div>
                  <GreenPrimaryButtonConfigProvider>
                    <Button
                      type='primary'
                    onClick={() => {
                      handleCalculateDimension(curBottemLeftPos, curTopRightPos, cameraBLPos, cameraTRPos);
                    }}
                    disabled={_.isEmpty(curBottemLeftPos) || _.isEmpty(curTopRightPos)}
                    >
                      <span className='font-source text-[12px] font-semibold leading-[150%]'>
                        {t('productDefine.calcualateDimension')}
                      </span>
                    </Button>
                  </GreenPrimaryButtonConfigProvider>
                  { _.isInteger(_.get(curProduct, 'board_width_mm', 0)) && _.isInteger(_.get(curProduct, 'board_height_mm', 0)) &&
                    <GreenPrimaryButtonConfigProvider>
                      <Button
                        type='primary'
                        onClick={() => {
                          setActiveTab('fullPCBCapture');
                        }}
                      >
                        <span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {t('productDefine.editFullPCBCapture')}
                        </span>
                      </Button>
                    </GreenPrimaryButtonConfigProvider>
                  }
                </div>
              </Fragment>
            }
          </div>
        :
          <div className='flex gap-2 flex-col self-stretch'>
            <div className='flex p-4 gap-4 flex-col self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {t('productDefine.measurePCBDimension')}
              </span>
              <div className='flex gap-2 flex-col self-stretch'>
                <div className='flex items-center gap-2 self-stretch justify-between'>
                  <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
                    {t('productDefine.panelLength')}
                  </span>
                  <div className='py-1 px-4 flex rounded-[4px] bg-[#ffffff0d] justify-center items-center'>
                    {/* <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {_.get(currentDimension, 'height', 0)}
                    </span> */}
                    <InputNumber
                      controls={false}
                      style={{ width: '100%' }}
                      value={_.get(currentDimension, 'height', 0)}
                      min={1}
                      step={1}
                      onChange={(value) => {
                        setCurrentDimension({ ...currentDimension, height: value });
                      }}
                    />
                  </div>
                </div>
                <div className='flex items-center gap-2 self-stretch justify-between'>
                  <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
                    {t('productDefine.panelWidth')}
                  </span>
                  <div className='py-1 px-4 flex rounded-[4px] bg-[#ffffff0d] justify-center items-center'>
                    {/* <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {_.get(currentDimension, 'width', 0)}
                    </span> */}
                    <InputNumber
                      controls={false}
                      style={{ width: '100%' }}
                      value={_.get(currentDimension, 'width', 0)}
                      min={1}
                      step={1}
                      onChange={(value) => {
                        setCurrentDimension({ ...currentDimension, width: value });
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className='flex gap-4 py-8 px-2 flex-col self-stretch'>
              <GreenPrimaryButtonConfigProvider>
                <Button
                  type='primary'
                  onClick={() => {
                    handleDimensionSubmit(curBottemLeftPos, curTopRightPos, productId, refetchCurProduct, currentDimension);
                  }}
                >
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('common.saveAndContinue')}
                  </span>
                </Button>
              </GreenPrimaryButtonConfigProvider>
              <GreenDefaultButtonConfigProvider>
                <Button
                  onClick={() => {
                    setCurrentDimension(null);
                    setIsSelectingDimensionPoint(true);
                  }}
                >
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.resetDimension')}
                  </span>
                </Button>
              </GreenDefaultButtonConfigProvider>
            </div>
          </div>
        }
      </div>
      <Display
        current2DUri={current2DUri}
        currentDepthImgUri={currentDepthImgUri}
        currentThumbnailUri={currentThumbnailUri}
        selectedTool={selectedTool}
        setSelectedTool={setSelectedTool}
        curProduct={curProduct}
        refetchCurProduct={refetchCurProduct}
        setCurBottemLeftPos={setCurBottemLeftPos}
        setCurTopRightPos={setCurTopRightPos}
        cameraPosition={cameraPosition}
        handlePickPointUpdateCameraPos={handlePickPointUpdateCameraPos}
      />
    </div>
  );
};

export default PCBDimension;