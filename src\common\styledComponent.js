import { Collapse, ConfigProvider, Menu, Modal, Segmented, Slider } from 'antd';
import styled from 'styled-components';

// also includes custom configprovider antd component

export const CustomMenu = styled(Menu)`
	.ant-menu-item {
		padding-block: ${(props) =>
			props.paddingblock ? `${props.paddingblock}px` : '7px'};
	}
`;

export const CustomModal = styled(Modal)`
	.ant-modal-content {
		padding: 0 !important;
		background: var(--default-Gray-1, #333);
	}
	.ant-modal-header {
		padding: 16px !important;
		border-bottom: 1px solid #56ccf2;
		background: var(--default-Gray-1, #333);
		margin-bottom: 0 !important;
	}
`;

export const GreenPrimaryButtonConfigProvider = (props) => (
	<ConfigProvider
		theme={{
			components: {
				Button: {
					primaryColor: '#333',
					colorPrimary: '#57F2C4',
					colorPrimaryHover: '#35D5AD',
					colorPrimaryActive: '#35D5AD',
					controlHeight: 28,
				},
			},
		}}
	>
		{props.children}
	</ConfigProvider>
);

export const GreenDefaultButtonConfigProvider = (props) => (
	<ConfigProvider
		theme={{
			components: {
				Button: {
					primaryColor: '#333',
					colorPrimary: '#57F2C4',
					colorPrimaryHover: '#35D5AD',
					colorPrimaryActive: '#35D5AD',
					controlHeight: 28,
				},
			},
		}}
	>
		{props.children}
	</ConfigProvider>
);

export const DarkBlueDefaultButtonConfigProvider = (props) => (
	<ConfigProvider
		theme={{
			components: {
				Button: {
					primaryColor: '#56CCF2',
					colorPrimary: '#56CCF2',
					colorPrimaryHover: '#56CCF2',
					colorPrimaryActive: '#56CCF2',
					controlHeight: props.controlHeight || 28,
					colorPrimaryBg: '#00000099',
					paddingInline: 0,
				},
			},
		}}
	>
		{props.children}
	</ConfigProvider>
);

export const SmallTabsConfigProvider = (props) => (
	<ConfigProvider
		theme={{
			components: {
				Tabs: {
					cardPadding: '2px 8px',
					horizontalMargin: '4px 4px 0 4px',
					colorBgContainer: '#ffffff0d',
					colorBorder: '#4F4F4F',
					cardBg: '#1E1E1E',
					itemSelectedColor: '#fff',
					itemHoverColor: '#fff',
					itemActiveColor: '#fff',
					// lineWidth: 2,
				},
			}
		}}
	>
		{props.children}
	</ConfigProvider>
);

export const CustomSegmented = styled(Segmented)`
	.ant-segmented-item-label {
		padding: 0 !important;
		min-height: 6px;
		display: flex;
		align-items: center;
	}
	.ant-segmented-item-disabled {
		cursor: default !important;
	}
`;

export const CustomSlider = styled(Slider)`
	.ant-slider-horizontal.ant-slider-with-marks {
		margin-bottom: 0 !important;
	}
`;

export const CustomCollapse = styled(Collapse)`
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-expand-icon {
    height: 32px !important;
  }
  .ant-collapse-header {
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
    border-bottom: 1px solid #ffffff0f !important;
  }
  .ant-collapse-content {
    border-radius: 0 !important;
    background-color: transparent !important;
  }
`;

export const CustomFeatureCollapse = styled(Collapse)`
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-expand-icon {
    height: 32px !important;
  }
  .ant-collapse-header {
    padding-left: 16px !important;
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
  }
  .ant-collapse-content {
    padding-left: 24px !important;
    border-radius: 0 !important;
    background-color: transparent !important;
    border-bottom: 1px solid #ffffff0f !important;
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
  }
`;

export const ThreeDDisplayWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
`;

export const HiddenCanvasDimensionCalcDiv = styled.div`
  width: 100%;
  height: 100%;
  z-index: 1;
  position: absolute;
`;