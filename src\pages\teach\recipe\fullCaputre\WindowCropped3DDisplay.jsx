import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import { loadAndDecodePoints } from '../../../../viewer/util';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';
import { useTranslation } from 'react-i18next';
import { highResolyCropped3DDisplayMegaPixelCount, pointCloudColorMode, serverHost, threedViewerWindowDefaultDimension } from '../../../../common/const';
import CroppedPointCloudViewer from '../../../../viewer/CroppedPointCloudViewer';
import { Rnd } from 'react-rnd';
import { Button, ConfigProvider, InputNumber, Select, Tooltip } from 'antd';
import { RedoOutlined } from '@ant-design/icons';


const WindowedCroppedPointCloudDisplay = (props) => {
  const {
    selfUnmount,
    id,
  } = props;

  const { t } = useTranslation();

  const dispatch = useDispatch();
  
  const pointCloudInitialized = useRef(false);
  const canvasRef = useRef(null);
  const viewerRef = useRef(null);
  const viewerContainerRef = useRef(null);
  const twoDDimensionRef = useRef(null);

  const [mode, setMode] = useState(pointCloudColorMode.FULL_COLOR);
  const [zRoiMax, setZRoiMax] = useState(0);
  const [zRoiMin, setZRoiMin] = useState(0);
  const [zRoiMode, setZRoiMode] = useState('off');
  const [zMin, setZMin] = useState(0);
  const [zMax, setZMax] = useState(0);
  const [isViewerReady, setIsViewerReady] = useState(false);
  const [selectedDisplayType, setSelectedDisplayType] = useState('point_cloud');
  const [openTooltip, setOpenTooltip] = useState(false);
  
  const [cachedPoints, setCachedPoints] = useState(null);

  const current3dSelectRectInfo = useSelector((state) => state.display.current3dSelectRectInfo);

  const handleLoadScene = async (
    current3dSelectRectInfo,
    cachedPoints,
    mode,
    zRoiMode,
    zRoiMin,
    zRoiMax,
    displayType,
  ) => {
    if (!viewerRef.current) return;
    if (_.isEmpty(current3dSelectRectInfo) || _.isUndefined(current3dSelectRectInfo)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.loadingCroppedPointCloud')));

    let pointNColor;
    if (_.isEmpty(cachedPoints)) {
      try {
        let url = `${serverHost}/blob?type=${displayType}`;
        url += `&color_uri=${encodeURIComponent(_.get(current3dSelectRectInfo, 'curImageUri', ''))}`;
        url += `&depth_uri=${encodeURIComponent(_.get(current3dSelectRectInfo, 'curDepthImgUri', ''))}`;
        url += `&x_min=${_.get(current3dSelectRectInfo, 'pMin.x', 0)}`;
        url += `&y_min=${_.get(current3dSelectRectInfo, 'pMin.y', 0)}`;
        url += `&x_max=${_.get(current3dSelectRectInfo, 'pMax.x', 0)}`;
        url += `&y_max=${_.get(current3dSelectRectInfo, 'pMax.y', 0)}`;
        // url += `&max_megapixel=${highResolyCropped3DDisplayMegaPixelCount}`;
        if (_.isNumber(_.get(current3dSelectRectInfo, 'angle', null))) url += `&angle=${_.get(current3dSelectRectInfo, 'angle', 0)}`;
        url += `&max_megapixel=2`;
        url += `&t=${Date.now().valueOf()}`; // to prevent cache
        pointNColor= await loadAndDecodePoints(url);
      } catch (error) {
        console.error('Failed to load and decode points: ', error);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
      setCachedPoints(pointNColor);
      twoDDimensionRef.current = {
        width: _.get(current3dSelectRectInfo, 'pMax.x', 0) - _.get(current3dSelectRectInfo, 'pMin.x', 0),
        height: _.get(current3dSelectRectInfo, 'pMax.y', 0) - _.get(current3dSelectRectInfo, 'pMin.y', 0),
      };
    } else {
      pointNColor = cachedPoints;
    }

    viewerRef.current.clearScene();

    viewerRef.current.loadScene({
      pointNColor,
      mode: mode,
      zRoiMode: zRoiMode,
      zRoiMin: zRoiMin,
      zRoiMax: zRoiMax,
      twoDDimension: twoDDimensionRef.current,
    });

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const handleClose = () => {
    if (viewerRef.current) {
      viewerRef.current.clearScene();
      viewerRef.current.destroy();
    }

    // umount component
    selfUnmount(id);
  };

  useEffect(() => {
    if (pointCloudInitialized.current || !isViewerReady) return;

    handleLoadScene(
      current3dSelectRectInfo,
      cachedPoints,
      mode,
      zRoiMode,
      zRoiMin,
      zRoiMax,
      selectedDisplayType,
    );

    pointCloudInitialized.current = true;
  }, [
    mode,
    zRoiMode,
    zRoiMin,
    zRoiMax,
    isViewerReady,
    selectedDisplayType,
  ]);

  useEffect(() => {
    if (!viewerContainerRef.current || !canvasRef.current) return;

    // init three d scene
    viewerRef.current = new CroppedPointCloudViewer(
      canvasRef.current,
      viewerContainerRef.current.offsetHeight,
      viewerContainerRef.current.offsetWidth,
      () => ({
        height: viewerContainerRef.current.offsetHeight,
        width: viewerContainerRef.current.offsetWidth,
      }),
      (zMin, zMax) => {
        setZMin(zMin);
        setZMax(zMax);
      },
    );
    setIsViewerReady(true);
  }, []);

  return (
    <div
      className='fixed top-0 left-0 z-[30]'
    >
      <Rnd
        style={{ zIndex: 30 }}
        default={{
          width: threedViewerWindowDefaultDimension.width,
          height: threedViewerWindowDefaultDimension.height,
          x: (window.innerWidth - threedViewerWindowDefaultDimension.width) / 2,
          y: (window.innerHeight - threedViewerWindowDefaultDimension.height) / 2,
        }}
        onResizeStop={(e, direction, ref, delta, position) => {
          viewerRef.current.updateSceneSize(ref.offsetWidth - 16, ref.offsetHeight - 16 - 31);
        }}
        bounds={'window'}
        dragHandleClassName='rnd-drag-handle'
      >
        <div
          className='flex flex-col w-full h-full self-stretch rounded-[6px]'
          style={{
            boxShadow: '0px 4px 30px 15px rgba(255, 255, 255, 0.55)',
          }}
        >
          <div
            className={`rnd-drag-handle flex items-center justify-center w-full h-[42px] bg-gray-1 rounded-t-[6px] self-stretch border-b-[1px] border-b-AOI-blue py-2 px-3
              cursor-move`}
          >
            <div className='flex items-center gap-2'>
              <img className='w-[14px] h-[14px]' src='/icn/bordedBox_white.svg' alt='bordedBox' />
              <span className='font-source text-[12px] font-normal'>{t('common.threeDView')}</span>
            </div>
            <div
              className='flex w-5 h-5 absolute top-[12.5px] right-[12px] hover:bg-[#eb575729] cursor-pointer items-center justify-center'
              onClick={() => {
                handleClose();
              }}
            >
              <img src='/icn/cross_white.svg' alt='cross' className='h-4 w-4' />
            </div>
          </div>
          <div className='flex w-full h-full self-stretch p-1 bg-[#1E1E1E] rounded-b-[6px]'>
            <div
              className='relative rounded-b-[6px] bg-gray-1'
              style={{
                height: `calc(100%)`,
                width: '100%',
              }}
              ref={viewerContainerRef}
            >
              <div className='absolute top-0 left-0 w-full h-full'>
                <canvas ref={canvasRef} />
              </div>
              <div className='absolute top-[5px] right-[5px] z-[15]'>
                <div className='flex flex-col gap-2 w-[40px]'>
                  <Tooltip
                    trigger='click'
                    open={openTooltip}
                    getPopupContainer={() => viewerContainerRef.current} 
                    title={<Content
                      handleLoadScene={handleLoadScene}
                      mode={mode}
                      setMode={setMode}
                      zRoiMode={zRoiMode}
                      setZRoiMode={setZRoiMode}
                      zRoiMin={zRoiMin}
                      setZRoiMin={setZRoiMin}
                      zRoiMax={zRoiMax}
                      setZRoiMax={setZRoiMax}
                      zMin={zMin}
                      zMax={zMax}
                      current3dSelectRectInfo={current3dSelectRectInfo}
                      cachedPoints={cachedPoints}
                      t={t}
                      selectedDisplayType={selectedDisplayType}
                      setOpenTooltip={setOpenTooltip}
                    />}
                    placement='leftTop'
                  >
                    <div
                      className={`flex items-center justify-center w-[30px] h-[30px] cursor-pointer bg-gray-1 hover:bg-[#eb575729] rounded-[4px] transition-all duration-300`}
                      onClick={() => {
                        setOpenTooltip(!openTooltip);
                      }}
                    >
                      <img src='/icn/full_color.svg' className='w-[28px] h-[26px]' alt='full' />
                    </div>
                  </Tooltip>
                  <Tooltip
                    title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('common.resetView')}</span>}
                    placement='left'
                  >
                    <div
                      className='flex items-center justify-center w-[30px] h-[30px] cursor-pointer bg-gray-1 hover:bg-[#eb575729] rounded-[4px] transition-all duration-300'
                        onClick={() => {
                          if (!viewerRef.current) return;
                          viewerRef.current.resetCamera();
                        }}
                    >
                      {/* <img src='/icn/topView_white.svg' className='w-[28px] h-[26px]' alt='full' /> */}
                      <RedoOutlined
                      />
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Rnd>
    </div>
  );
};

const Content = (props) => {
  const {
    handleLoadScene,
    mode,
    setMode,
    zRoiMode,
    setZRoiMode,
    zRoiMin,
    setZRoiMin,
    zRoiMax,
    setZRoiMax,
    zMin,
    zMax,
    current3dSelectRectInfo,
    cachedPoints,
    t,
    selectedDisplayType,
    setOpenTooltip
  } = props;

  return (
    <div className="flex flex-col items-start self-stretch rounded-[4px]">
      <div className="flex items-center px-2 py-1 gap-2">
        <div
          className={`flex w-8 h-8 justify-center items-center gap-2.5 
            ${mode === pointCloudColorMode.FULL_COLOR && 'border-[2px] border-AOI-blue rounded-[4px]'}`}
          onClick={() => {
            setMode(pointCloudColorMode.FULL_COLOR);
          }}
        >
          <img src="/icn/full_color.svg" className='h-6 w-6' alt="" />
          {/* {mode === pointCloudColorMode.FULL_COLOR ? (
            <img
              src="/img/icn/full_color_active.svg"
              alt=""
              className='border-[2px] border-AOI-blue rounded-[4px]'
            />
          ) : (
            <img src="/img/icn/full_color.svg" alt="" />
          )} */}
        </div>
        <div
          className={`flex w-8 h-8 justify-center items-center gap-2.5 
            ${mode === pointCloudColorMode.GREEN_MODE && 'border-[2px] border-AOI-blue rounded-[4px]'}`}
          onClick={() => {
            setMode(pointCloudColorMode.GREEN_MODE);
          }}
        >
          <img src="/icn/green_mode.svg" alt="" />
          {/* {mode === pointCloudColorMode.GREEN_MODE ? (
            <img
              src="/img/icn/green_mode_active.svg" alt=""  className='border-[2px] border-AOI-blue rounded-[4px]' />
          ) : (
            <img src="/img/icn/green_mode.svg" alt="" />
          )} */}
        </div>
        <div
          className={`flex w-8 h-8 justify-center items-center gap-2.5
            ${mode === pointCloudColorMode.GRAY_SCALE && 'border-[2px] border-AOI-blue rounded-[4px]'}`}
          onClick={() => {
            setMode(pointCloudColorMode.GRAY_SCALE);
          }}
        >
          <img src="/icn/gray_scale.svg" alt="" />
          {/* {mode === pointCloudColorMode.GRAY_SCALE ? (
            <img src="/img/icn/gray_scale_active.svg" alt="" className='border-[2px] border-AOI-blue rounded-[4px]' />
          ) : (
            <img src="/img/icn/gray_scale.svg" alt="" />
          )} */}
        </div>
        <div
          className={`flex w-8 h-8 justify-center items-center gap-2.5 
            ${mode === pointCloudColorMode.DEPTH_MAP && 'border-[2px] border-AOI-blue rounded-[4px]'}`}
          onClick={() => {
            setMode(pointCloudColorMode.DEPTH_MAP);
          }}
        >
          <img src="/icn/depth_map.svg" alt="" />
          {/* {mode === pointCloudColorMode.DEPTH_MAP ? (
            <img src="/img/icn/depth_map_active.svg" alt="" />
          ) : (
            <img src="/img/icn/depth_map.svg" alt="" />
          )} */}
        </div>
      </div>
      <div className="flex px-3 py-2 flex-col items-start gap-1 self-stretch">
        <div className="flex items-center gap-2 self-stretch">
          <span className="text-[#fcfcfc] text-xxs uppercase">
            {t('common.depthRange')}
          </span>
        </div>
        <div className="w-full">
          <ConfigProvider
            theme={{
              token: {
                colorText: '#fff',
                colorIcon: '#fff',
              },
              components: {
                Select: {
                  selectorBg: '#4f4f4f',
                  optionSelectedBg: '#4f4f4f',
                  optionSelectedColor: '#FFF',
                },
              },
            }}
          >
            <Select
              dropdownStyle={{ backgroundColor: '#333' }}
              value={zRoiMode}
              style={{ width: '100%', borderRadius: '2px' }}
              options={[
                { value: 'off', label: t('common.off') },
                { value: 'automatic', label: t('common.automatic') },
                { value: 'manual', label: t('common.manual') },
              ]}
              size='small'
              onChange={(value) => {
                setZRoiMode(value);

                if (value === 'automatic') {
                  setZRoiMin(zMin);
                  setZRoiMax(zMax);
                }
              }}
            />
          </ConfigProvider>
        </div>
        <div className="flex items-center gap-2 self-stretch justify-between">
          <span className="text-[#fcfcfc] text-xs whitespace-nowrap flex-1">
            {t('common.minimum')}
          </span>
          <div className="w-full">
            <InputNumber
              size="small"
              suffix={<div className="pr-1">mm</div>}
              value={zRoiMin}
              disabled={zRoiMode === 'off' || zRoiMode === 'automatic'}
              onChange={(e) => setZRoiMin(e)}
              controls={false}
            />
          </div>
        </div>

        <div className="flex items-center gap-2 self-stretch justify-between">
          <span className="text-[#fcfcfc] text-xs whitespace-nowrap">{t('common.maximum')}</span>
          <div className="w-full">
            <InputNumber
              size="small"
              suffix={<div className="pr-1">mm</div>}
              value={zRoiMax}
              disabled={zRoiMode === 'off' || zRoiMode === 'automatic'}
              onChange={(e) => setZRoiMax(e)}
              controls={false}
            />
          </div>
        </div>
      </div>
      <div className='flex flex-row justify-between self-stretch'>
        <Button
        size='small'
        onClick={() => {
          setOpenTooltip(false);
        }}
        >
          <span className='font-source text-[12px] font-normal'>
            {t('common.cancel')}
          </span>
        </Button>
        <Button
          size='small'
          onClick={() => {
            handleLoadScene(
              current3dSelectRectInfo,
              cachedPoints,
              mode,
              zRoiMode,
              zRoiMin,
              zRoiMax,
              selectedDisplayType,
            );
          }}
        >
          <span className='font-source text-[12px] font-normal'>
            {t('common.apply')}
          </span>
        </Button>
      </div>
      
    </div>
  );
};

export default WindowedCroppedPointCloudDisplay;