import React, { useEffect, useState } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import CommonTable from '../../components/CommonTable';
import { Button, DatePicker, Input, Select, Table, Tooltip } from 'antd';
import i18n from '../../i18n';
import { customZhCNDatePickerLocale } from '../../common/const';
import enUS from 'antd/es/date-picker/locale/en_US';
import { useTranslation } from 'react-i18next';
import { backendTimestampToDisplayString, convertBackendTimestampToMoment, toLocalISOString } from '../../common/util';
import { useDeleteSessionMutation, useLazyGetAllSessionsQuery } from '../../services/inference';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import _ from 'lodash';

const { RangePicker } = DatePicker;

const SessionList = (props) => {
  const {
    setDisplayedContentType,
    allProducts,
    selectedGoldenProdId,
    setSelectedGoldenProdId,
    startTimeInSession,
    setStartTimeInSession,
    endTimeInSession,
    setEndTimeInSession,
    setIpcSessionId,
    setSelectedSession,
    pagination,
    setPagination,
    refreshToggle,
    setRefreshToggle,
  } = props;

  const { t } = useTranslation();

  const [displayedSessions, setDisplayedSessions] = useState([]);
  const [getSessionsQueryParam, setGetSessionsQueryParam] = useState({});

  const [getSessions ] = useLazyGetAllSessionsQuery();
  const [deleteSession] = useDeleteSessionMutation();

  const handleFilterUpdate = async (query, pagination) => {
    const res = await getSessions(query);

    if (res.error) {
      aoiAlert(t('notification.error.getSession'), ALERT_TYPES.COMMON_ERROR);
      console.error('get session failed', res.error.message);
      setDisplayedSessions([]);
      return;
    }

    setDisplayedSessions(_.get(res, 'data.data', []));
    setPagination({
      ...pagination,
      total: _.get(res, 'data.pageCount', 0) * pagination.pageSize,
    });
  };

  const refetchSessions = async () => {
    const res = await getSessions(getSessionsQueryParam);

    if (res.error) {
      aoiAlert(t('notification.error.getSession'), ALERT_TYPES.COMMON_ERROR);
      console.error('get session failed', res.error.message);
      setDisplayedSessions([]);
      return;
    }

    setDisplayedSessions(_.get(res, 'data.data', []));
    setPagination((prev) => ({
      ...prev,
      total: _.get(res, 'data.pageCount', 0) * prev.pageSize,
    }));
    setRefreshToggle((prev) => prev + 1);
  };



  useEffect(() => {
    if (pagination.pageSize === 0) return;

    const queryParam = {};

    if (_.isInteger(selectedGoldenProdId)) queryParam.golden_product_id = selectedGoldenProdId;
    if (startTimeInSession) {
      // dayjs in local timezone to UTC
      queryParam.start_datetime = toLocalISOString(new Date(startTimeInSession));
    }
    if (endTimeInSession) queryParam.end_datetime = toLocalISOString(new Date(endTimeInSession));
    queryParam.page = 0;
    queryParam.limit = pagination.pageSize;

    setGetSessionsQueryParam(queryParam);

    setPagination({
      ...pagination,
      current: 1,
    });

    handleFilterUpdate(queryParam, {
      ...pagination,
      current: 1,
    });
  }, [
    selectedGoldenProdId,
    startTimeInSession,
    endTimeInSession,
  ]);

  useEffect(() => {
    if (refreshToggle <= 0) return;
    handleFilterUpdate({
      ...getSessionsQueryParam,
      page: pagination.current - 1,
      limit: pagination.pageSize,
    }, pagination);
  }, [refreshToggle]);

  useEffect(() => {
    return () => {
      setRefreshToggle(0);
    };
  }, []);

  return (
    <div className='flex gap-4 flex-1 self-stretch flex-col'>
      <div className='flex py-2 justify-between items-center self-stretch'>
        <div className='flex gap-2 items-center self-stretch'>
          {/* <Input
            addonBefore={<SearchOutlined />}
          /> */}
          <Select
            showSearch
            options={
              _.isEmpty(allProducts) ? []
              : _.filter(allProducts, (product) => product.is_golden === true).map((product) => ({
                value: Number(product.product_id),
                label: <span className='font-source text-[12px] font-normal'>{product.product_name}</span>,
                searchKey: product.product_name,
              }))
            }
            value={selectedGoldenProdId}
            onChange={(value) => setSelectedGoldenProdId(value)}
            placeholder={<span className='font-source text-[12px] font-normal'>{t('worklist.filterByGoldenProduct')}</span>}
            style={{ width: '200px' }}
            popupMatchSelectWidth={false}
            allowClear
            filterOption={(input, option) => {
              return option.searchKey.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
          />
          <RangePicker
            locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
            showTime
            onCalendarChange={(value) => {
              setStartTimeInSession(_.get(value, '0', null));
              setEndTimeInSession(_.get(value, '1', null));
            }}
            value={[startTimeInSession, endTimeInSession]}
          />
        </div>
        {/* <div
          className='flex h-8 w-8 justify-center items-center cursor-pointer hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out'
          onClick={() => {}}
        >
          <img src='/icn/download_blue.svg' alt='download' className='w-[17px] h-[16px]' />
        </div> */}
      </div>
      <div className='flex flex-1 flex-col'>
        <CommonTable
          total={pagination.total}
          setPageSize={(pageSize) => {
            setPagination((prev) => ({
              ...prev,
              pageSize,
            }));
            handleFilterUpdate({
              ...getSessionsQueryParam,
              page: pagination.current - 1,
              limit: pageSize,
            }, {
              ...pagination,
              pageSize,
            });
          }}
          onPageChange={(page) => {
            setPagination((prev) => ({
              ...prev,
              current: page,
            }));
            handleFilterUpdate({
              ...getSessionsQueryParam,
              page: page - 1,
              limit: pagination.pageSize,
            }, {
              ...pagination,
              current: page,
            });
          }}
          cols={[
            {
              key: 'taskId',
              title: <div className='flex gap-1 items-center'>
                <img src='/icn/meter_white.svg' alt='meter' className='w-3 h-[9.5px]' />
                <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('worklist.taskId')}</span>
              </div>,
              render: (text, record) => (
                <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{_.get(record, 'ipc_session_id', '')}</span>
              )
            },
            {
              key: 'PCBType',
              title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('worklist.PCBType')}</span>,
              render: (text, record) => {
                return _.map(_.get(record, 'golden_product_ids', []), (pid, index) => {
                  return <span className={'font-source text-[14px] font-semibold leading-[150%]'}>
                    {`${_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name', 'unknwon product')}`}
                  </span>
                });
              },
            },
            {
              key: 'totalItems',
              title: <div className='flex gap-1 items-center'>
                <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('worklist.totalItems')}</span>
              </div>,
              render: (text, record) => (
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {record.total_product_count}
                </span>
              ),
            },
            {
              key: 'itemsNG',
              title: <div className='flex gap-1 items-center'>
                <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('worklist.itemsNG')}</span>
              </div>,
              render: (text, record) => (
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {record.defective_product_count}
                </span>
              ),
            },
            {
              key: 'pass_rate',
              title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.passRate')}</span>,
              render: (text, record) => (
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {`${
                    _.isInteger(record.total_product_count) && record.total_product_count > 0 ?
                    _.round((record.total_product_count-record.defective_product_count)/record.total_product_count * 100, 2)
                    : 0
                  }% ${t('common.pass')}`}
                </span>
              ),
              // sorter: (a, b) => {
              //   const aPassRate = _.isInteger(a.total_product_count) && a.total_product_count > 0 ?
              //     (a.total_product_count-a.defective_product_count)/a.total_product_count * 100
              //     : 0;
              //   const bPassRate = _.isInteger(b.total_product_count) && b.total_product_count > 0 ?
              //     (b.total_product_count-b.defective_product_count)/b.total_product_count * 100
              //     : 0;
              //   return bPassRate - aPassRate;
              // },
            },
            {
              key: 'startedAt',
              title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.startedAt')}</span>,
              render: (text, record) => (
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {backendTimestampToDisplayString(record.started_at)}
                </span>
              ),
              // sorter: (a, b) => convertBackendTimestampToMoment(b.started_at) - convertBackendTimestampToMoment(a.started_at),
            },
            {
              key: 'action',
              title: <></>,
              render: (text, record) => (
                <div className='flex gap-2 items-center'>
                  <span
                    className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                      italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                      px-2`}
                    onClick={() => {
                      if (!_.get(record, 'ipc_session_id', null) || _.isEmpty(_.get(record, 'golden_product_ids', []))) return;
                      for (let i = 0; i < _.get(record, 'golden_product_ids', []).length; i++) {
                        if (!_.find(allProducts, p => Number(p.product_id) === _.get(record, `golden_product_ids.${i}`))) return;
                      }
                      setIpcSessionId(_.get(record, 'ipc_session_id', null));
                      setSelectedSession(record);
                      setDisplayedContentType('inspection');
                    }}
                  >
                    {t('worklist.viewAllInspectionRecords')}
                  </span>
                  <span
                    className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                      italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                      px-2`}
                    onClick={async() => {
                      const res = await deleteSession({
                        ipc_session_id: _.get(record, 'ipc_session_id', null),
                      });

                      if (res.error) {
                        aoiAlert(t('notification.error.deleteSession'), ALERT_TYPES.COMMON_ERROR);
                        console.error('delete session failed', res.error.message);
                        return;
                      }

                      if (res) {
                        aoiAlert(t('notification.success.deleteSession'), ALERT_TYPES.COMMON_SUCCESS);
                        await refetchSessions();
                      }

                    }}
                  >
                    {t('worklist.deleteSession')}
                  </span>
                </div>

              ),
            }
          ]}
          data={displayedSessions}
        />
      </div>
    </div>
  )
};

export default SessionList;