import { Button, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { COMMON_HTTP_CODE, fieldConstraints } from '../../../common/const';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../../common/styledComponent';
import _ from 'lodash';
import { useAddProductMutation, useUpdateProductMutation } from '../../../services/product';
import { useDispatch, useSelector } from 'react-redux';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';
import { useNavigate } from 'react-router-dom';


const PCBDetail = (props) => {
  const {
    setActiveTab,
    setIsConveyorControllerOpened,
    curProduct,
    refetchCurProduct,
    productId,
    setConveyorReminderOpened,
  } = props;

  const navigate = useNavigate();

  const { t } = useTranslation();

  const [productName, setProductName] = useState('');

  const [addProduct] = useAddProductMutation();
  const [updateProduct] = useUpdateProductMutation();

  const ignoreConveyorReminderInProductDefine = useSelector((state) => state.setting.ignoreConveyorReminderInProductDefine);
  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);
  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);
  const isProgrammingUsingConveyor = useSelector((state) => state.setting.isProgrammingUsingConveyor);
  const currentControlledConveyorSlotId = useSelector((state) => state.setting.currentControlledConveyorSlotId);

  const handleSubmit = async (productName, productId, refetchCurProduct, curProduct) => {
    if (!_.isObject(curProduct) && !_.isString(productId)) {
      // create new product
    const res = await addProduct({product_name: productName, description: ''});
      if (res.error) {
        if (res.error.status === COMMON_HTTP_CODE.conflict) {
          console.error('product name exist');
          aoiAlert(t('notification.error.nameExists'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        console.error('addProduct error:', _.get(res, 'error.message', ''));
        aoiAlert(t('notification.error.addProduct'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      aoiAlert(t('notification.success.addProduct'), ALERT_TYPES.COMMON_INFO);
      navigate(`/teach?product-id=${_.get(res, 'data.product_id', '-1')}`);
      
      return;
    } else {
      const res = await updateProduct({
        product_id: Number(productId),
        product_name: productName,
      });

      if (res.error) {
        if (res.error.status === COMMON_HTTP_CODE.conflict) {
          console.error('product name exist');
          aoiAlert(t('notification.error.nameExists'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
        console.error('update product failed', res.error.message);
        return;
      }
    }
  };

  useEffect(() => {
    if (_.isObject(curProduct)) {
      setProductName(_.get(curProduct, 'product_name', ''));
    } else {
      setProductName('');
    }
  }, [curProduct]);

  return (
    <div className='flex p-[48px] justify-center flex-1 self-stretch'>
      <div className='flex w-[748px] p-[48px] flex-col gap-6 slef-stretch rounded-[10px] bg-[#ffffff08]'>
        <span className='font-source text-[20px] font-normal leading-[normal]'>
          {t('productDefine.PCBInformation')}
        </span>
        <div className='flex flex-col gap-6 self-stretch'>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex items-center gap-2 self-stretch'>
              <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.42px] whitespace-nowrap'>
                {t('productDefine.productName')}
              </span>
              <Input
                style={{ width: '100%' }}
                minLength={fieldConstraints.productDefine.productName.minLength}
                maxLength={fieldConstraints.productDefine.productName.maxLength}
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
              />
            </div>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <GreenDefaultButtonConfigProvider>
              <Button>
                <span className='font-source text-[12px] font-normal leading-[150%] '>
                  {t('common.cancel')}
                </span>
              </Button>
            </GreenDefaultButtonConfigProvider>
            <GreenPrimaryButtonConfigProvider>
              <Button
                type='primary'
                onClick={() => {
                  handleSubmit(productName, productId, refetchCurProduct, curProduct);
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('common.save')}
                </span>
              </Button>
            </GreenPrimaryButtonConfigProvider>
          </div>
        </div>
        <div className='flex flex-col gap-2 self-stretch'>
          <span className='font-source text-[16px] font-normal leading-[normal]'>
            {t('productDefine.specsForInspection')}
          </span>
          <div className='flex gap-4 py-3 px-4 items-center rounded-[4px] bg-[#ffffff08]'>
            <img src={_.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) ?
              '/icn/checkFilledCircle_blue.svg' :
              '/icn/checkFilledCircle_gray.svg'} alt='checkFilledCircle' className='w-[14px] h-[14px]' />
            <span className='font-source text-[12px] font-normal leading-[normal] w-[120px] pt-0.5'>
              {t('productDefine.conveyorWidth')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[normal] italic flex-1 pt-0.5'>
              {!_.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) ? `(${t('common.undefined')})` : `${_.get(curProduct, 'conveyor_width_mm', null)} mm`}
            </span>
            {(!_.isString(productId) || _.isEmpty(productId)) &&
              <GreenDefaultButtonConfigProvider>
                <Button disabled>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.set')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
            {_.isString(productId) && !_.isEmpty(productId) && !_.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) &&
              <GreenPrimaryButtonConfigProvider>
                <Button
                  type='primary'
                  onClick={() => {
                    setActiveTab('conveyorSetup');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                      {t('common.set')}
                    </span>
                    <img src='/icn/arrowRight_gray.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenPrimaryButtonConfigProvider>
            }
            {_.isString(productId) && !_.isEmpty(productId) && _.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) &&
              <GreenDefaultButtonConfigProvider>
                <Button
                  onClick={() => {
                    setActiveTab('conveyorSetup');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.set')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
          </div>
          {(_.isEmpty(conveyorAccessToken) || _.isEmpty(cameraAccessToken)) &&
            <div className='flex py-3 px-4 justify-center items-center self-stretch rounded-[4px] bg-[#56ccf21a]'>
              <span className='font-source text-[12px] font-normal leading-[normal]'>
                {t('productDefine.toFinishUpdateTheFollowing')}
              </span>
            </div>
          }
          <div className='flex gap-4 py-3 px-4 items-center rounded-[4px] bg-[#ffffff08]'>
            <img src={!(!_.isInteger(_.get(curProduct, 'board_width_mm', null)) || !_.isInteger(_.get(curProduct, 'board_height_mm', null))) ? '/icn/checkFilledCircle_blue.svg' : '/icn/checkFilledCircle_gray.svg'} alt='checkFilledCircle' className='w-[14px] h-[14px]' />
            <span className='font-source text-[12px] font-normal leading-[normal] w-[120px] pt-0.5'>
              {t('productDefine.PCBDimension')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[normal] italic flex-1 pt-0.5'>
              {
                (!_.isInteger(_.get(curProduct, 'board_width_mm', null)) || !_.isInteger(_.get(curProduct, 'board_height_mm', null))) ?
                `(${t('common.undefined')})` :
                `${_.get(curProduct, 'board_width_mm', null)} mm x ${_.get(curProduct, 'board_height_mm', null)} mm`
              }
            </span>
            {(_.isEmpty(conveyorAccessToken) || _.isEmpty(cameraAccessToken) || !_.isString(currentControlledConveyorSlotId) ||
              !_.isInteger(_.get(curProduct, 'conveyor_width_mm', null))) &&
              <GreenDefaultButtonConfigProvider>
                <Button
                  onClick={() => {
                    setActiveTab('PCBDimension');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                  disabled
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.measure')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
            {!_.isEmpty(conveyorAccessToken) && !_.isEmpty(cameraAccessToken) && _.isString(currentControlledConveyorSlotId) && _.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) &&
              !_.isInteger(_.get(curProduct, 'board_width_mm', null)) && !_.isInteger(_.get(curProduct, 'board_height_mm', null)) &&
              <GreenPrimaryButtonConfigProvider>
                <Button
                  type='primary'
                  onClick={() => {
                    setActiveTab('PCBDimension');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                      {t('common.measure')}
                    </span>
                    <img src='/icn/arrowRight_gray.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenPrimaryButtonConfigProvider>
            }
            {!_.isEmpty(conveyorAccessToken) && !_.isEmpty(cameraAccessToken) && _.isString(currentControlledConveyorSlotId) && _.isInteger(_.get(curProduct, 'conveyor_width_mm', null)) &&
              _.isInteger(_.get(curProduct, 'board_width_mm', null)) && _.isInteger(_.get(curProduct, 'board_height_mm', null)) &&
              <GreenDefaultButtonConfigProvider>
                <Button
                  onClick={() => {
                    setActiveTab('PCBDimension');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.measure')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
          </div>
          <div className='flex gap-4 py-3 px-4 items-center rounded-[4px] bg-[#ffffff08]'>
            <img src={!_.isEmpty(_.get(curProduct, 'inspectables', [])) ? '/icn/checkFilledCircle_blue.svg' : '/icn/checkFilledCircle_gray.svg'} alt='checkFilledCircle' className='w-[14px] h-[14px]' />
            <span className='font-source text-[12px] font-normal leading-[normal] w-[120px] pt-0.5'>
              {t('productDefine.fullPCBCapture')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[normal] italic flex-1 pt-0.5'>
              {
                _.isEmpty(_.get(curProduct, 'inspectables', [])) ?
                `(${t('common.undefined')})` :
                t('productDefine.captured2DAnd3DData')
              }
            </span>
            {(_.isEmpty(conveyorAccessToken) || _.isEmpty(cameraAccessToken) || !_.isString(currentControlledConveyorSlotId) ||
              !_.isInteger(_.get(curProduct, 'board_width_mm', null)) || !_.isInteger(_.get(curProduct, 'board_height_mm', null))) &&
              <GreenDefaultButtonConfigProvider>
                <Button disabled>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.capture')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
            {!_.isEmpty(conveyorAccessToken) && !_.isEmpty(cameraAccessToken) && _.isString(currentControlledConveyorSlotId) && _.isInteger(_.get(curProduct, 'board_width_mm', null)) && _.isInteger(_.get(curProduct, 'board_height_mm', null)) &&
              _.isEmpty(_.get(curProduct, 'inspectables', [])) &&
              <GreenPrimaryButtonConfigProvider>
                <Button
                  type='primary'
                  onClick={() => {
                    setActiveTab('fullPCBCapture');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                  disabled={
                    _.isEmpty(conveyorAccessToken) ||
                    _.isEmpty(cameraAccessToken) ||
                    !_.isString(currentControlledConveyorSlotId) ||
                    !_.isInteger(_.get(curProduct, 'board_width_mm', null)) ||
                    !_.isInteger(_.get(curProduct, 'board_height_mm', null))
                  }
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                      {t('common.capture')}
                    </span>
                    <img src='/icn/arrowRight_gray.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenPrimaryButtonConfigProvider>
            }
            {!_.isEmpty(conveyorAccessToken) && !_.isEmpty(cameraAccessToken) && _.isString(currentControlledConveyorSlotId) && _.isInteger(_.get(curProduct, 'board_width_mm', null)) && _.isInteger(_.get(curProduct, 'board_height_mm', null)) &&
              !_.isEmpty(_.get(curProduct, 'inspectables', [])) &&
              <GreenDefaultButtonConfigProvider>
                <Button
                  onClick={() => {
                    setActiveTab('fullPCBCapture');
                    setIsConveyorControllerOpened(true);
                    if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                  }}
                  disabled={
                    _.isEmpty(conveyorAccessToken) ||
                    _.isEmpty(cameraAccessToken) ||
                    !_.isString(currentControlledConveyorSlotId) ||
                    !_.isInteger(_.get(curProduct, 'board_width_mm', null)) ||
                    !_.isInteger(_.get(curProduct, 'board_height_mm', null))
                  }
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.capture')}
                    </span>
                    <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                  </div>
                </Button>
              </GreenDefaultButtonConfigProvider>
            }
            {/* <GreenDefaultButtonConfigProvider>
              <Button
                onClick={() => {
                  setActiveTab('fullPCBCapture');
                  setIsConveyorControllerOpened(true);
                  if (!ignoreConveyorReminderInProductDefine) setConveyorReminderOpened(true);
                }}
                disabled={
                  _.isEmpty(conveyorAccessToken) ||
                  _.isEmpty(cameraAccessToken) ||
                  !_.isString(currentControlledConveyorSlotId) ||
                  !_.isInteger(_.get(curProduct, 'board_width_mm', null)) ||
                  !_.isInteger(_.get(curProduct, 'board_height_mm', null))
                }
              >
                <div className='flex items-center gap-2'>
                  <span className='font-source text-[12px] font-weight leading-[150%] pt-0.5'>
                    {t('common.capture')}
                  </span>
                  <img src='/icn/arrowRight_white.svg' alt='arrowRight' className='w-[10px] h-[10px]' />
                </div>
              </Button>
            </GreenDefaultButtonConfigProvider> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PCBDetail;