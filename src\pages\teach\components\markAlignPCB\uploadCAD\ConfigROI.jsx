import { Button, Checkbox, Collapse, ConfigProvider, Select } from 'antd';
import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { allTrue } from '../../../../../common/util';
import _ from 'lodash';
import GenerateROIComfirmation from '../../../../../modal/GenerateROIComfirmation';


const ConfigROI = (props) => {
  const {
    parsedComponentInfo,
    setCurrentStep,
    parseRules,
    currentTranslation,
    currentRotation,
    curProduct,
    currentFileUri,
    horizontallyFlipped,
    verticallyFlipped,
  } = props;

  const { t } = useTranslation();

  const [selectedSourceType, setSelectedSourceType] = useState('bothPrivateAndPublic');
  const [selectedInspectionTypes, setSelectedInspectionTypes] = useState({
    body: {
      twoD: true,
      threeD: true,
    },
    ICLead: {
      twoD: true,
      threeD: true,
    },
    solder: {
      twoD: true,
      threeD: true,
    },
  });
  const [isExistingComponentsFoundComfirmationOpened, setIsExistingComponentsFoundComfirmationOpened] = useState(false);

  const UnusedCollapse = <ConfigProvider
theme={{
  components: {
    Collapse: {
      headerBg: '#131313',
      contentBg: '#131313',
      headerPadding: '4px 0px',
      contentPadding: '0 0',
    }
  }
}}
>
<Collapse
  defaultActiveKey={['all']}
  items={[
    {
      key: 'all',
      label: <div className='flex gap-2 items-center slef-stretch'>
        <Checkbox
          onClick={(e) => {
            e.stopPropagation();
            const newInspTypes = _.cloneDeep(selectedInspectionTypes);
            _.forEach(newInspTypes, (inspType) => {
              inspType.twoD = !allTrue(selectedInspectionTypes);
              inspType.threeD = !allTrue(selectedInspectionTypes);
            });
            setSelectedInspectionTypes(newInspTypes);
          }}
          checked={allTrue(selectedInspectionTypes)}
        />
        <span className='font-source text-[12px] font-normal leading-[150%]'>
          {t('common.all')}
        </span>
      </div>,
      children: <div className='flex flex-col self-stretch flex-1'>
        <div className='w-full h-[1px] bg-[#4F4F4F]' />
        {/* body starts */}
        <div className='flex self-stretch '>
          <CustomCollapse
            style={{ width: '100%' }}
            defaultActiveKey={['body']}
            items={[
              {
                key: 'body',
                label: <div className='flex gap-2 items-center slef-stretch '>
                  <Checkbox
                    onClick={(e) => {
                      e.stopPropagation();
                      const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                      newInspTypes.body.twoD = !allTrue(selectedInspectionTypes.body);
                      newInspTypes.body.threeD = !allTrue(selectedInspectionTypes.body);
                      setSelectedInspectionTypes(newInspTypes);
                    }}
                    checked={selectedInspectionTypes.body.twoD && selectedInspectionTypes.body.threeD}
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.body')}
                  </span>
                </div>,
                children: <div className='flex flex-col self-stretch'>
                  <div className='flex flex-col self-stretch'>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.body.twoD = !selectedInspectionTypes.body.twoD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.body.twoD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.twoD')}
                      </span>
                    </div>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.body.threeD = !selectedInspectionTypes.body.threeD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.body.threeD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.threeD')}
                      </span>
                    </div>
                  </div>
                </div>
              },
            ]}
          />
        </div>
        {/* body ends */}
        <div className='w-full h-[1px] bg-[#4F4F4F]' />
        {/* ic lead starts */}
        <div className='flex self-stretch '>
          <CustomCollapse
            style={{ width: '100%' }}
            defaultActiveKey={['ICLead']}
            items={[
              {
                key: 'ICLead',
                label: <div className='flex gap-2 items-center slef-stretch '>
                  <Checkbox
                    onClick={(e) => {
                      e.stopPropagation();
                      const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                      newInspTypes.ICLead.twoD = !allTrue(selectedInspectionTypes.ICLead);
                      newInspTypes.ICLead.threeD = !allTrue(selectedInspectionTypes.ICLead);
                      setSelectedInspectionTypes(newInspTypes);
                    }}
                    checked={selectedInspectionTypes.ICLead.twoD && selectedInspectionTypes.ICLead.threeD}
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.ICLead')}
                  </span>
                </div>,
                children: <div className='flex flex-col self-stretch'>
                  <div className='flex flex-col self-stretch'>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.ICLead.twoD = !selectedInspectionTypes.ICLead.twoD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.ICLead.twoD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.twoD')}
                      </span>
                    </div>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.ICLead.threeD = !selectedInspectionTypes.ICLead.threeD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.ICLead.threeD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.threeD')}
                      </span>
                    </div>
                  </div>
                </div>
              },
            ]}
          />
        </div>
        {/* ic lead ends */}
        {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
        {/* ocr 2d starts */}
        {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
          <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
          <div className='flex items-center gap-2 slef-stretch'>
            <Checkbox
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.OCR2D')}
            </span>
          </div>
        </div> */}
        {/* ocr 2d ends */}
        {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
        {/* qr 2d starts */}
        {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
          <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
          <div className='flex items-center gap-2 slef-stretch'>
            <Checkbox
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.QR2D')}
            </span>
          </div>
        </div> */}
        {/* qr 2d ends */}
        {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
        {/* text 2d starts */}
        {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
          <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
          <div className='flex items-center gap-2 slef-stretch'>
            <Checkbox
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.Text2D')}
            </span>
          </div>
        </div> */}
        {/* text 2d ends */}
        <div className='w-full h-[1px] bg-[#4F4F4F]' />
        {/* solder starts */}
        <div className='flex self-stretch '>
          <CustomCollapse
            style={{ width: '100%' }}
            defaultActiveKey={['solder']}
            items={[
              {
                key: 'solder',
                label: <div className='flex gap-2 items-center slef-stretch '>
                  <Checkbox
                    onClick={(e) => {
                      e.stopPropagation();
                      const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                      newInspTypes.solder.twoD = !allTrue(selectedInspectionTypes.solder);
                      newInspTypes.solder.threeD = !allTrue(selectedInspectionTypes.solder);
                      setSelectedInspectionTypes(newInspTypes);
                    }}
                    checked={selectedInspectionTypes.solder.twoD && selectedInspectionTypes.solder.threeD}
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.solder')}
                  </span>
                </div>,
                children: <div className='flex flex-col self-stretch'>
                  <div className='flex flex-col self-stretch'>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.solder.twoD = !selectedInspectionTypes.solder.twoD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.solder.twoD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.twoD')}
                      </span>
                    </div>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                      <Checkbox
                        onClick={(e) => {
                          e.stopPropagation();
                          const newInspTypes = _.cloneDeep(selectedInspectionTypes);
                          newInspTypes.solder.threeD = !selectedInspectionTypes.solder.threeD;
                          setSelectedInspectionTypes(newInspTypes);
                        }}
                        checked={selectedInspectionTypes.solder.threeD}
                      />
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.threeD')}
                      </span>
                    </div>
                  </div>
                </div>
              },
            ]}
          />
        </div>
        {/* solder ends */}
      </div>
    }
  ]}
/>
</ConfigProvider>;

  return (
    <Fragment>
      <GenerateROIComfirmation
        isOpened={isExistingComponentsFoundComfirmationOpened}
        setIsOpened={setIsExistingComponentsFoundComfirmationOpened}
        parsedComponentInfo={parsedComponentInfo}
        parseRules={parseRules}
        currentTranslation={currentTranslation}
        currentRotation={currentRotation}
        curProduct={curProduct}
        currentFileUri={currentFileUri}
        horizontallyFlipped={horizontallyFlipped}
        verticallyFlipped={verticallyFlipped}
      />
      <div className='flex flex-1 gap-4 px-8 justify-center self-stretch'>
        <div className='flex w-[680px] flex-col gap-2 self-stretch'>
          <div className='flex items-center py-1 gap-2'>
            <div
              className='flex h-4 w-4 items-center justify-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px] transition-all duration-300 ease-in-out'
              onClick={() => {
                setCurrentStep(1);
              }}
            >
              <img src='/icn/arrowLeft_white.svg' alt='arrowLeft' className='w-[6px] h-[10px]' />
            </div>
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.previous')}
            </span>
          </div>
          <div className='flex p-4 flex-col items-center gap-6 self-stretch bg-[#ffffff0d]'>
            <div className='flex flex-col gap-1 self-stretch'>
              <span className='font-source text-[16px] font-normal leading-[150%]'>
                {t('productDefine.configureROIs')}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('productDefine.configureROIMatchSourceAndDefaultEnabledInspection')}
              </span>
            </div>
            <div className='flex flex-col gap-2 self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('productDefine.findAndMatch')}
              </span>
              <Select
                style={{ width: '100%' }}
                options={[
                  {
                    label: <span className='font-source text-[14px] font-normal leading-[150%]'>
                      {t('productDefine.bothPrivateAndPublic')}
                    </span>,
                    value: 'bothPrivateAndPublic',
                  }
                ]}
                value={selectedSourceType}
                onChange={(value) => {
                  setSelectedSourceType(value);
                }}
                popupMatchSelectWidth={false}
              />
            </div>
            <div className='flex flex-col gap-2 self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('productDefine.selectInspectionTypes')}
              </span>
              <div className='flex px-2 flex-col self-stretch bg-[#131313] rounded-[2px]'>
                
              </div>
            </div>
            <Button
              style={{ width: '100%' }}
              type='primary'
              onClick={() => {
                setIsExistingComponentsFoundComfirmationOpened(true);
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('productDefine.finishAndGenerateROI')}
              </span>
            </Button>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

const CustomCollapse = styled(Collapse)`
  .ant-collapse-expand-icon {
    padding-left: 16px;
  }
`;

export default ConfigROI;