import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import _ from 'lodash';

export const systemApi = createApi({
	reducerPath: 'systemApi',
	baseQuery,
	tagTypes: ['System'],
	endpoints: (build) => ({
		getSystemMetadata: build.query({
			query: () => ({
				url: '/system',
				method: 'GET',
			}),
			providesTags: ['System'],
		}),
		getAllSystemConfigFiles: build.query({
			query: () => ({
				url: '/configFiles',
				method: 'GET',
			}),
		}),
		updateAllSystemConfigFiles: build.mutation({
			query: (body) => ({
				url: '/configFiles',
				method: 'POST',
				body,
			}),
		}),
		getInspectionExportPath: build.query({
			query: () => ({
				url: '/inspection/exportPath',
				method: 'GET',
			}),
		}),
	}),
});

export const {
	useGetSystemMetadataQuery,
	useGetAllSystemConfigFilesQuery,
	useLazyGetAllSystemConfigFilesQuery,
	useUpdateAllSystemConfigFilesMutation,
	useGetInspectionExportPathQuery,
} = systemApi;
