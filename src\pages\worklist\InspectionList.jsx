import { Button, DatePicker, Input, Select, Tooltip } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CommonTable from '../../components/CommonTable';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useDeleteInspectionRecordByProductIdMutation, useLazyGetAllInspectionsQuery } from '../../services/inference';
import _ from 'lodash';
import i18n from '../../i18n';
import enUS from 'antd/es/date-picker/locale/en_US';
import { backendAutoGenTimeToDisplayString, toLocalISOString } from '../../common/util';
import { customZhCNDatePickerLocale, localStorageKeys, serverHost } from '../../common/const';
import { useScanBarcodeMutation, useUpdateProductMutation } from '../../services/product';


const { RangePicker } = DatePicker;

const InspectionList = (props) => {
  const {
    setDisplayedContentType,
    selectedGoldenProdId,
    allProducts,
    startTimeInInspection,
    setStartTimeInInspection,
    endTimeInInspection,
    setEndTimeInInspection,
    onlyFeedbackProvided,
    setOnlyFeedbackProvided,
    onlyDefectiveItems,
    setOnlyDefectiveItems,
    setSelectedGoldenProdId,
    setSearchSerialNumber,
    searchSerialNumber,
    ipcSessionId,
    selectedSession,
    pagination,
    setPagination,
    setRefreshToggle,
    refreshToggle,
  } = props;

  const { t } = useTranslation();

  const [isComponentOkNgListOpened, setIsComponentOkNgListOpened] = useState(false);
  const [searchMode, setSearchMode] = useState('sn'); // goldenProduct, sn
  const [getInspectionsQueryParam, setGetInspectionsQueryParam] = useState({
    page: 0,
    limit: 0,
    ipc_session_id: ipcSessionId,
  });
  const [displaySearchSN, setDisplaySearchSN] = useState('');
  // const [pagination, setPagination] = useState({
  //   current: 1,
  //   pageSize: 0,
  //   total: 0,
  // });
  const [displayedIpc, setDisplayedIpc] = useState([]);

  const [getInspections] = useLazyGetAllInspectionsQuery();
  const [deleteInspectionRecord] = useDeleteInspectionRecordByProductIdMutation();
  const [scanBarcode] = useScanBarcodeMutation();
  const [updateProduct] = useUpdateProductMutation();

  const cols = [
    {
      title: t('worklist.serialNo'),
      key: 'product_serial_no',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'product_serial_no', '')}
          </span>
        );
      }
    },
    {
      title: t('worklist.date'),
      key: 'date',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'timestamp') ? backendAutoGenTimeToDisplayString(record.timestamp) : ''}
          </span>
        );
      },
    },
    {
      title: t('worklist.totalComponent'),
      key: 'total_component',
      // dataIndex: 'total_roi_count',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'total_roi_count', 0)}
          </span>
        );
      },
    },
    {
      title: t('worklist.okngCounts'),
      key: 'okng_counts',
      render: (text, record) => {
        return (
          <div className='flex flex-1 items-center'>
            <div className='flex w-[56px] items-center gap-1'>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/icn/checkCircle_green.svg' alt='checkCircle' />
              </div>
              <span className='font-source text-[12px] font-normal'>
                {_.get(record, 'total_roi_count', 0) - _.get(record, 'defective_roi_count', 0)}
              </span>
            </div>
            <div className='flex w-[56px] items-center gap-1'>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/icn/warning_red.svg' alt='warning' />
              </div>
              <span className='font-source text-[12px] font-normal'>
                {_.get(record, 'defective_roi_count', 0)}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: t('worklist.passFail'),
      key: 'pass_fail',
      render: (text, record) => (
        <div className='flex gap-2 items-center'>
          { _.get(record, 'defective_roi_count', 0) > 0 ?
            <Fragment>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/icn/warning_red.svg' alt='warning' />
              </div>
              <span className='font-source text-[12px] font-semibold text-[#EB5757]'>
                {t('worklist.fail')}
              </span>
            </Fragment>
          :
          <Fragment>
            <div className='flex flex-col items-center justify-center'>
              <img className='w-[12px] h-[12px]' src='/icn/checkCircle_green.svg' alt='check' />
            </div>
            <span className='font-source text-[12px] font-semibold text-[#27AE60]'>
              {t('worklist.pass')}
            </span>
          </Fragment>
          }
        </div>
      ),
    },
    {
      title: t('worklist.feedback'),
      key: 'feedback',
      render: (text, record) => (
        <>
          { record.feedback_provided ? <div className='flex items-center gap-2'>
              <img className='w-[16px] h-[16px]' src='/icn/check_green.svg' alt='check' />
              <span className='font-source text-[12px] font-normal'>
                {t('worklist.provided')}
              </span>
            </div> : <div className='flex items-center gap-2'>
              <span className='font-source text-[12px] font-normal'>
                {t('worklist.notProvided')}
              </span>
          </div> }
        </>
      ),
    },
    {
      title: t('worklist.actions'),
      key: 'actions',
      render: (text, record) => (
        <div className='flex items-center gap-2'>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={() => {
              let url = `/inspection/review`;
              url += `?ipc-product-id=${_.get(record, 'product_id')}`;
              url += `&ipc-session-id=${ipcSessionId}`;
              url += `&golden-product-id=${_.get(record, 'golden_product_id')}`;
              url += `&is-from-live=false`;
              const q = { ...getInspectionsQueryParam };
              delete q.page;
              delete q.limit;
              if (!_.isEmpty(q)) {
                url += `&query=${encodeURIComponent(JSON.stringify(q))}`;
              }
              window.location.href = url;
            }}
          >
            {t('worklist.review')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async() => {
              await handleRescan(_.get(record, 'product_id'));
            }}
          >
            {t('worklist.rescan')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async() => {
              await handleExportMESResult(_.get(record, 'product_id'));
            }}
          >
            {t('common.exportMESResult')}
          </span>
          <span
            className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
              px-2`}
            onClick={async () => {
              const res = await deleteInspectionRecord(_.get(record, 'product_id'));
              if (res.error) {
                aoiAlert(t('notification.error.deleteInspectionRecord'), ALERT_TYPES.COMMON_ERROR);
                console.error('deleteInspectionRecord error: ', res.error.message);
                return;
              }
              setRefreshToggle(refreshToggle + 1);
            }}
          >
            {t('common.delete')}
          </span>
        </div>
      ),
    },
  ];

  const handleFilterUpdate = async (query, pagination) => {
    const res = await getInspections(query);
    if (res.error) {
      aoiAlert(t('notification.error.getInspections'), ALERT_TYPES.COMMON_ERROR);
      console.error('getInspections error: ', res.error.message);
      setDisplayedIpc([]);
      return;
    }

    setDisplayedIpc(_.get(res, 'data.data', []));
    setPagination({
      ...pagination,
      total: Number(_.get(res, 'data.pageCount', 0)) * pagination.pageSize,
    });
  };

  const handleRescan = async (productId) => {
    // Ben said this is unknown for now, so we will not implement this for now
    // const res = await scanBarcode({ device_id:  });
    const res = await scanBarcode();

    if (res.error) {
      aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (res) {
      if(!res.data) {
        aoiAlert(t('notification.error.scanBarcode'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const updateRes = await updateProduct({
        product_id: Number(productId),
        product_serial: res?.data
      });


      if (updateRes.error) {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
        console.error('updateProduct error: ', updateRes.error.message);
        return;
      }

      const updateResData = _.get(updateRes, 'data.data', {});
      if (updateResData) {
        aoiAlert(t('notification.success.updateProduct'), ALERT_TYPES.COMMON_SUCCESS);
        setRefreshToggle(refreshToggle + 1);
      } else {
        aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      }
    }
  };

  const handleExportMESResult = async (productId) => {
    const res = await fetch(`${serverHost}/inspection/exportMesResult`, {
      method: 'POST',
      headers: {
        Authorization: localStorage.getItem(localStorageKeys.accessToken),
      },
      body: JSON.stringify(Number(productId)),
    });

    if (!res.ok) {
      aoiAlert(t('notification.error.exportMesResult'), ALERT_TYPES.COMMON_ERROR);
      console.error('exportMesResult error: ', res.statusText);
      return;
    }

    aoiAlert(t('notification.success.exportMesResult'), ALERT_TYPES.COMMON_INFO);
    return;
  };

  useEffect(() => {
    if (pagination.pageSize === 0) return;

    const newQueryParam = {ipc_session_id: ipcSessionId,};

    if (searchMode === 'goldenProduct' && _.isInteger(selectedGoldenProdId)) {
      newQueryParam.golden_product_id = selectedGoldenProdId;
    }
    if (searchMode === 'sn' && !_.isEmpty(searchSerialNumber)) {
      newQueryParam.serial_no = searchSerialNumber;
    }

    if (startTimeInInspection) {
      // newQueryParam.start_datetime = startTimeInInspection.valueOf();
      // convert to UTC in YYYY-MM-DDTHH:mm:ss.sssZ format
      newQueryParam.start_datetime = toLocalISOString(new Date(startTimeInInspection));
    }
    if (endTimeInInspection) {
      // newQueryParam.end_datetime = endTimeInInspection.valueOf();
      // convert to UTC in YYYY-MM-DDTHH:mm:ss.sssZ format
      newQueryParam.end_datetime = toLocalISOString(new Date(endTimeInInspection));
    }

    if (_.isBoolean(onlyFeedbackProvided) && onlyFeedbackProvided) {
      newQueryParam.feedback = true;
    }

    if (_.isBoolean(onlyDefectiveItems) && onlyDefectiveItems) {
      newQueryParam.defect = true;
    }

    // newQueryParam.page = pagination.current - 1;
    // newQueryParam.limit = pagination.pageSize;

    newQueryParam.page = 0;
    newQueryParam.limit = pagination.pageSize;

    setPagination({
      ...pagination,
      current: 1,
    });

    setGetInspectionsQueryParam(newQueryParam);
    handleFilterUpdate(newQueryParam, {
      ...pagination,
      current: 1,
    });
  }, [
    searchMode,
    searchSerialNumber,
    startTimeInInspection,
    endTimeInInspection,
    onlyFeedbackProvided,
    onlyDefectiveItems,
    selectedGoldenProdId,
  ]);

  useEffect(() => {
    if (refreshToggle <= 0) return;
    handleFilterUpdate({
      ...getInspectionsQueryParam,
      page: pagination.current - 1,
      limit: pagination.pageSize,
    }, pagination);
  }, [refreshToggle]);

  return (
    <div className='flex flex-col gap-4 flex-1 self-stretch items-start'>
      <div className='flex gap-8 self-stretch items-center py-2'>
        <div className='flex items-baseline gap-6'>
          <div className='flex items-center gap-2'>
            <Button
              type='text'
              onClick={() => { setDisplayedContentType('session') }}
            >
              <span className='font-source text-[14px] font-normal text-AOI-blue'>
                {t('worklist.worklist')}
              </span>
            </Button>
            <span className='font-source text-[14px] font-normal'>
              /
            </span>
            <span className='font-source text-[14px] font-normal'>
              {`${t('home.sessionId')} ${ipcSessionId}`}
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {t('worklist.sessionGoldenProd')}
            </span>
            <div className='flex items-center gap-1 h-[32px] max-w-[400px] overflow-y-auto flex-wrap'>
              {/* {_.map([1066, 1062, 1061, 1054], (pid, index) => { */}
              {_.map(_.get(selectedSession, 'golden_product_ids', []), (pid, index) => {
                return <span className={`font-source text-[12px] font-${selectedGoldenProdId === pid ? 'semibold' : 'formal'} text-${selectedGoldenProdId === pid ? 'white' : '[#BDBDBD]'}`}>
                  {`${_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name')}`}
                </span>
              })}
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {t('worklist.totalProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold'>
              {_.get(selectedSession, 'total_product_count', 0)}
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {t('worklist.goodProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold text-[#57F2C4]'>
              {_.get(selectedSession, 'total_product_count', 0)-_.get(selectedSession, 'defective_product_count', 0)}
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {t('worklist.defectiveProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold text-[#EB5757]'>
              {_.get(selectedSession, 'defective_product_count', 0)}
            </span>
          </div>
        </div>
        {/* <Button
          type='text'
          onClick={() => setIsComponentOkNgListOpened(true)}
        >
          <span className='font-source text-[12px] font-normal'>
            {t('worklist.openComponentOkNgList')}
          </span>
        </Button> */}
      </div>
      <div className='flex py-2 gap-4 self-stretch items-center'>
        <div className='flex gap-1 items-center'>
          {/* { searchMode === 'goldenProduct' ?
            <Select
              showSearch
              options={
                _.isEmpty(allProducts) ? []
                : _.map(_.get(selectedSession, 'golden_product_ids'), pid => {
                  const product = _.find(allProducts, (prod) => Number(prod.product_id) === pid);
                  return {
                    value: Number(product.product_id),
                    label: <span className='font-source text-[12px] font-normal'>{product.product_name}</span>,
                  };
                })
              }
              value={selectedGoldenProdId}
              onChange={(value) => setSelectedGoldenProdId(value)}
              placeholder={t('worklist.filterByGoldenProduct')}
              style={{ width: '200px' }}
              popupMatchSelectWidth={false}
              allowClear
            />
          :
            <Input
              value={displaySearchSN}
              onChange={(e) => setDisplaySearchSN(e.target.value)}
              style={{ width: '200px' }}
              onBlur={(e) => setSearchSerialNumber(e.target.value)}
              onPressEnter={(e) => setSearchSerialNumber(e.target.value)}
              placeholder={t('worklist.searchBySerialNo')}
            />
          } */}
          <Input
            value={displaySearchSN}
            onChange={(e) => setDisplaySearchSN(e.target.value)}
            style={{ width: '200px' }}
            onBlur={(e) => setSearchSerialNumber(e.target.value)}
            onPressEnter={(e) => setSearchSerialNumber(e.target.value)}
            placeholder={t('worklist.searchBySerialNo')}
          />
          {/* <Select
            options={[
              {
                value: 'goldenProduct',
                label: <span className='font-source text-[12px] font-normal'>{t('worklist.filterByGoldenProduct')}</span>,
              },
              {
                value: 'sn',
                label: <span className='font-source text-[12px] font-normal'>{t('worklist.filterBySerialNumber')}</span>,
              },
            ]}
            value={searchMode}
            onChange={(value) => setSearchMode(value)}
            style={{ width: '200px' }}
            popupMatchSelectWidth={false}
          /> */}
        </div>
        <RangePicker
          locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
          showTime
          onCalendarChange={(value) => {
            setStartTimeInInspection(_.get(value, '0', null));
            setEndTimeInInspection(_.get(value, '1', null));
          }}
          value={[startTimeInInspection, endTimeInInspection]}
        />
        <Button
          type={onlyFeedbackProvided ? 'primary' : 'default'}
          onClick={() => setOnlyFeedbackProvided(!onlyFeedbackProvided)}
        >
          <span className={`font-source text-[12px] font-normal ${onlyFeedbackProvided ? 'text-[#333]' : 'text-white'}`}>
            {t('worklist.feedbackProvided')}
          </span>
        </Button>
        <Button
          type={onlyDefectiveItems ? 'primary' : 'default'}
          onClick={() => setOnlyDefectiveItems(!onlyDefectiveItems)}
        >
          <span className={`font-source text-[12px] font-normal ${onlyDefectiveItems ? 'text-[#333]' : 'text-white'}`}>
            {t('worklist.onlyDisplayDefectiveItems')}
          </span>
        </Button>
        <Tooltip
          title={
            <span className='font-source text-[12px] font-normal'>
              {t('worklist.clearFilter')}
            </span>
          }
        >
          <div className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer' onClick={() => {
            setStartTimeInInspection(null);
            setEndTimeInInspection(null);
            setOnlyFeedbackProvided(false);
            setOnlyDefectiveItems(false);
          }}>
            <img className='w-[16px] h-[16px]' src='/icn/cancelFilter_blue.svg' alt='cancelFilter' />
          </div>
        </Tooltip>
      </div>
      <CommonTable
        cols={cols}
        data={displayedIpc}
        setPageSize={(pageSize) => {
          setPagination((prev) => ({
            ...prev,
            pageSize,
          }));
          handleFilterUpdate({
            ...getInspectionsQueryParam,
            page: pagination.current - 1,
            limit: pageSize,
          }, {
            ...pagination,
            pageSize,
          });
        }}
        total={pagination.total}
        onPageChange={(page) => {
          setPagination((prev) => ({
            ...prev,
            current: page,
          }));
          handleFilterUpdate({
            ...getInspectionsQueryParam,
            page: page - 1,
            limit: pagination.pageSize,
          }, {
            ...pagination,
            current: page,
          });
        }}
      />
    </div>
  );
};

export default InspectionList;