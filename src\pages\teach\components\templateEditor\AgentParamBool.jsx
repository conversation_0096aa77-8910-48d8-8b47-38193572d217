import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { Switch } from 'antd';

const AgentParamBool = (props) => {
  const {
    fieldInfo,
    lintItemName,
    selectedGroupFeatureTypeAgentParams,
    agentParamName,
    submit,
    active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
  } = props;

  const [displayedValue, setDisplayedValue] = useState(fieldInfo);
  const displayedValRef = useRef(displayedValue);
  const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

  const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
    const payload = {
      ...selectedGroupFeatureTypeAgentParams,
      line_item_params: {
        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
        [lintItemName]: {
          ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
          params: {
            ..._.get(
              selectedGroupFeatureTypeAgentParams,
              `line_item_params.${lintItemName}.params`
            ),
            [agentParamName]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${agentParamName}`
              ),
              param_bool: value,
            },
          },
        },
      },
    };

    return payload;
  };

  useEffect(() => {
    setDisplayedValue(fieldInfo);
    displayedValRef.current = fieldInfo;
    selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
  }, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
      if (displayedValRef.current !== _.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_bool`, false)) {
        const payload = getPayload(
          displayedValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

  return (
    <div className="flex self-stretch">
      <Switch
        size="small"
        disabled={!active}
        checked={active ? displayedValue : false}
        onChange={(checked) => {
          setDisplayedValue(checked);
          displayedValRef.current = checked;
          const payload = getPayload(
            checked,
            selectedGroupFeatureTypeAgentParamsRef.current,
            lintItemName,
            agentParamName
          );
          submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
        }}
      />
    </div>
  );
};

export default AgentParamBool;
