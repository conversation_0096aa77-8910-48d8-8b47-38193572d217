import { createSlice } from '@reduxjs/toolkit';


const initialState = {
  current3dSelectRectInfo: null, // { pMin: { x: 0, y: 0 }, pMax: ..., twoDUri: ... }
};

const display = createSlice({
  name: 'display',
  initialState,
  reducers: {
    setCurrent3dSelectRectInfo: (state, action) => {
      state.current3dSelectRectInfo = action.payload;
    },
  },
});

export const {
  setCurrent3dSelectRectInfo,
} = display.actions;

export default display.reducer;