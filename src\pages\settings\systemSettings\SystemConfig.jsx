import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import {
	useGetAllSystemConfigFilesQuery,
	useGetInspectionExportPathQuery,
	useUpdateAllSystemConfigFilesMutation,
} from '../../../services/system';
import { useTranslation } from 'react-i18next';
import CustomExpand from '../../../components/CustomExpand';
import JSONEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.css';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';
import { Button, Input } from 'antd';
import { localStorageKeys, serverHost } from '../../../common/const';

function SystemConfig() {
	const { t } = useTranslation();
	const {
		data: jsonData,
		error: jsonDataError,
		refetch: jsonDataRefetch,
	} = useGetAllSystemConfigFilesQuery();

	const [updateAllSystemConfigFiles] = useUpdateAllSystemConfigFilesMutation();
	const { data: inspectionExportPath, refetch: refetchInspectionExportPath } = useGetInspectionExportPathQuery();

	const [json, setJson] = useState(null);
	const [displayedInspectionExportPath, setDisplayedInspectionExportPath] = useState('');

	useEffect(() => {
		if (!_.isEmpty(jsonData)) {
			setJson(jsonData);
		}
	}, [jsonData]);

	useEffect(() => {
		setDisplayedInspectionExportPath(inspectionExportPath || '');
	}, [inspectionExportPath]);

	const captureAgentContRef = useRef();
	const captureAgentEidtorRef = useRef();
	const sensorConfigContRef = useRef();
	const sensorConfigEditorRef = useRef();
	const systemConfigContRef = useRef();
	const systemConfigEditorRef = useRef();

	useEffect(() => {
		return () => {
			if (captureAgentEidtorRef.current)
				captureAgentEidtorRef.current.destroy();
			if (sensorConfigEditorRef.current)
				sensorConfigEditorRef.current.destroy();
			if (systemConfigEditorRef.current)
				systemConfigEditorRef.current.destroy();
		};
	}, []);

	if (jsonDataError) {
		aoiAlert(
			t('notification.error.failToGetSystemConfig'),
			ALERT_TYPES.COMMON_ERROR
		);
	}

	return (
		<div className="flex py-4 px-[140px] flex-1 self-stretch justify-center">
			<div className="flex w-[800px] flex-col gap-0.5 self-stretch">
				<div className="flex p-6 items-center self-stretch">
					<span className="font-source text-[20px] font-normal leading-[normal] tracking-[0.6px]">
						{t('editSystemConfig.editSystemConfig')}
					</span>
				</div>
				{/* add system language desc */}
				<div className="flex flex-col  items-start gap-2 px-4">
					<span className="font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]">
						{t('editSystemConfig.editSystemConfigDesc')}
					</span>
				</div>

				<div className="flex flex-col items-start gap-8 self-stretch px-4 py-6">
					<div className="flex gap-4 self-stretch flex-col">
						{/* capture agent starts */}
						<div className="flex gap-4 self-stretch flex-col">
							<span className="font-source text-[14px] font-semibold">
								{t('editSystemConfig.captureAgent')}
							</span>
							<CustomExpand
								title={
									<span className="font-source text-[12px] font-normal">
										{t('editSystemConfig.captureAgentConfig')}
									</span>
								}
								onExpand={() => {
									setTimeout(() => {
										if (
											_.isUndefined(captureAgentContRef.current) ||
											_.isEmpty(captureAgentContRef.current)
										)
											return;
										if (captureAgentEidtorRef.current)
											captureAgentEidtorRef.current.destroy();
										captureAgentEidtorRef.current = new JSONEditor(
											captureAgentContRef.current,
											{
												onChangeJSON: (v) => {
													setJson({
														...json,
														'capture_agent/capture_agent_config.json':
															JSON.stringify(v),
													});
												},
											}
										);
										try {
											const curJson = JSON.parse(
												_.get(
													json,
													'capture_agent/capture_agent_config.json',
													'{}'
												)
											);
											captureAgentEidtorRef.current.set(curJson);
										} catch (e) {
											aoiAlert(
												t('notification.error.failToParseThisJsonToObject'),
												ALERT_TYPES.COMMON_ERROR
											);
											captureAgentEidtorRef.current.set({});
										}
									}, 300);
								}}
							>
								<div
									className="w-full h-[400px] border border-[#28587B] rounded-[6px]"
									ref={captureAgentContRef}
								/>
							</CustomExpand>
							<CustomExpand
								title={
									<span className="font-source text-[12px] font-normal">
										{t('editSystemConfig.sensorConfig')}
									</span>
								}
								onExpand={() => {
									setTimeout(() => {
										if (
											_.isUndefined(sensorConfigContRef.current) ||
											_.isEmpty(sensorConfigContRef.current)
										)
											return;
										if (sensorConfigEditorRef.current)
											sensorConfigEditorRef.current.destroy();
										sensorConfigEditorRef.current = new JSONEditor(
											sensorConfigContRef.current,
											{
												onChangeJSON: (v) => {
													setJson({
														...json,
														'capture_agent/sensor_config.json':
															JSON.stringify(v),
													});
												},
											}
										);
										try {
											const curJson = JSON.parse(
												_.get(json, 'capture_agent/sensor_config.json', '{}')
											);
											sensorConfigEditorRef.current.set(curJson);
										} catch (e) {
											aoiAlert(
												t('notification.error.failToParseThisJsonToObject'),
												ALERT_TYPES.COMMON_ERROR
											);
											sensorConfigEditorRef.current.set({});
										}
									}, 300);
								}}
							>
								<div
									className="w-full h-[400px] border border-[#28587B] rounded-[6px]"
									ref={sensorConfigContRef}
								/>
							</CustomExpand>
						</div>
						{/* capture agent ends */}

						{/* system config starts */}
						<div className="flex gap-4 self-stretch flex-col">
							<span className="font-source text-[14px] font-semibold">
								{t('editSystemConfig.system')}
							</span>
							<CustomExpand
								title={
									<span className="font-source text-[12px] font-normal">
										{t('editSystemConfig.systemJsonConfig')}
									</span>
								}
								onExpand={() => {
									setTimeout(() => {
										if (
											_.isUndefined(systemConfigContRef.current) ||
											_.isEmpty(systemConfigContRef.current)
										)
											return;
										if (systemConfigEditorRef.current)
											systemConfigEditorRef.current.destroy();
										systemConfigEditorRef.current = new JSONEditor(
											systemConfigContRef.current,
											{
												onChangeJSON: (v) => {
													setJson({
														...json,
														'system_config.json': JSON.stringify(v),
													});
												},
												// onEditable: ({ path, field, value }) => {
												// 	return {
												// 		[path]: false,
												// 		[field]: false,
												// 		[value]: true,
												// 	};
												// },
											}
										);
										try {
											const curJson = JSON.parse(
												_.get(json, 'system_config.json', '{}')
											);
											systemConfigEditorRef.current.set(curJson);
										} catch (e) {
											aoiAlert(
												t('notification.error.failToParseThisJsonToObject'),
												ALERT_TYPES.COMMON_ERROR
											);
											systemConfigEditorRef.current.set({});
										}
									}, 300);
								}}
							>
								<div
									className="w-full h-[400px] border border-[#28587B] rounded-[6px]"
									ref={systemConfigContRef}
								/>
							</CustomExpand>
						</div>
						{/* system config ends */}

						{/* MES export starts */}
						<div className="flex gap-4 self-stretch flex-col">
							<span className="font-source text-[14px] font-semibold">
								{t('editSystemConfig.exportMESPath')}
							</span>
							<Input
								value={displayedInspectionExportPath}
								onChange={(e) => {
									setDisplayedInspectionExportPath(e.target.value);
								}}
								style={{ width: '100%' }}
							/>
						</div>
						{/* MES export ends */}
					</div>
				</div>

				<div className="flex items-center gap-2 py-2">
					<Button
						onClick={() => {
							const update = async (json, displayedInspectionExportPath) => {
								const res = await updateAllSystemConfigFiles(json);

								if (res.error) {
									aoiAlert(
										t('notification.error.failToUpdateAllSystemConfigFiles'),
										ALERT_TYPES.COMMON_ERROR
									);
									return;
								}

								const res1 = await fetch(`${serverHost}/inspection/exportPath`, {
									headers: {
										Authorization: localStorage.getItem(localStorageKeys.accessToken),
									},
									method: 'PUT',
									body: displayedInspectionExportPath,
								});

								if (!res1.ok) {
									aoiAlert(
										t('notification.error.failToUpdateInspectionExportPath'),
										ALERT_TYPES.COMMON_ERROR
									);
									return;
								}

								await refetchInspectionExportPath();

								aoiAlert(
									t('notification.success.systemConfigFilesUpdated'),
									ALERT_TYPES.COMMON_SUCCESS
								);
								return;
							};

							// validate json before update
							try {
								JSON.parse(JSON.stringify(json));
							} catch (e) {
								aoiAlert(
									t('notification.error.failToParseThisJsonToObject'),
									ALERT_TYPES.COMMON_ERROR
								);
								return;
							}
							update(json, displayedInspectionExportPath);
						}}
					>
						<span className="font-source text-[12px] font-normal">
							{t('common.save')}
						</span>
					</Button>
				</div>
			</div>
		</div>
	);
}

export default SystemConfig;
