import _ from 'lodash';
import React from 'react';
import { useSelector } from 'react-redux';


const ContainerTransparentLoader = () => {
  const isTransparentLoadingEnabled = useSelector((state) => state.setting.isTransparentLoadingEnabled);

  return (
    <div className={`absolute top-0 left-0 w-full h-full ${isTransparentLoadingEnabled ? 'z-[1002]' : 'hidden'}`}>
      <div className='flex flex-col items-center justify-center w-full h-full' />
    </div>
  );
};

export default ContainerTransparentLoader;