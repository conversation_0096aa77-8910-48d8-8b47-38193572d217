import React, { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import Display from './Display';
import { useTranslation } from 'react-i18next';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../../../common/styledComponent';
import { Button, Select } from 'antd';
import _ from 'lodash';
import { useDeleteFeatureMutation, useDetectMarkerCenterMutation, useGetAllFeaturesQuery, useUpdateFeatureRoiMutation } from '../../../../services/product';
import { markerFeatureType } from '../../../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';


const MarkAlignPCB = (props) => {
  const {
    curProduct,
    markerFeatures,
    refetchMarkers
  } = props;

  const { t } = useTranslation();

  const [selectedTool, setSelectedTool] = useState('transform');
  const [selectedLocateFid, setSelectedLocateFid] = useState(null);
  const [selectedEditFid, setSelectedEditFid] = useState(null);

  const [deleteFeature] = useDeleteFeatureMutation();
  const [detectMarkerCenter] = useDetectMarkerCenterMutation();
  const [updateFeatureRoi] = useUpdateFeatureRoiMutation();

  const [markerFeaturesCopy, setMarkerFeaturesCopy] = useState(null);
  const [shapeChangeUpdate, setShapeChangeUpdate] = useState(null); // 用于通知AlignPCBViewer更新featureObj
  const [featureIdsToDisplayCenterDot, setFeatureIdsToDisplayCenterDot] = useState([]);

  useEffect(() => {
    setMarkerFeaturesCopy(markerFeatures);
  }, [markerFeatures]);

  const handleAddMarker = useCallback(() => {
    setSelectedTool('select3DArea');
  }, []);

  const handleLocateMarker = useCallback((featureId) => {
    setSelectedLocateFid(featureId);
  }, []);

  const handleEditMarker = useCallback((featureId) => {
    setSelectedEditFid(featureId);
  }, []);

  const handleDeleteMarker = useCallback(async (marker) => {
    const res = await deleteFeature({
      product_id: _.get(marker, 'product_id'),
      step: _.get(marker, 'step'),
      feature_id: _.get(marker, 'feature_id'),
      variant: _.get(marker, 'variant'),
    });

    if (res.error) {
      aoiAlert(t('notification.error.deleteFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error('deleteFeature error:', _.get(res, 'error.message', ''));
      return;
    }
    
    await refetchMarkers();

    setFeatureIdsToDisplayCenterDot((prev) => prev.filter(id => id !== marker.feature_id));
  }, [deleteFeature, t, refetchMarkers]);

  const handleShapeChange = useCallback(async (value, marker) => {
    const baseLineItemParams = marker.line_item_params || {};
    const baseAligner2d = baseLineItemParams.aligner_2d || {};
    const baseParams = baseAligner2d.params || {};


    const updatedMarker = {
      ...marker,
      line_item_params: {
        ...baseLineItemParams,
        aligner_2d: {
          ...baseAligner2d,
          params: {
            ...baseParams,
            marker_type: {
              param_enum: {
                value: value,
                options: ["CIRCLE", "RECTANGLE", "TEMPLATE"]
              }
            }
          }
        }
      }
    };

    setMarkerFeaturesCopy((prev) => {
      const newMarkerFeatures = _.cloneDeep(prev);
      const markerIndex = _.findIndex(newMarkerFeatures, { feature_id: marker.feature_id });

      if (markerIndex !== -1) {
        newMarkerFeatures[markerIndex] = updatedMarker;
      }

      return newMarkerFeatures;
    });

    setShapeChangeUpdate({
      featureId: marker.feature_id,
      updatedMarker: updatedMarker,
      timestamp: Date.now()
    });

    const res = await updateFeatureRoi({
      body: updatedMarker,
      params: { allComponents: false },
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateMarker'), ALERT_TYPES.COMMON_ERROR);
      console.error('updateFeatureRoi error:', _.get(res, 'error.message', ''));

      // 如果更新失败，回滚本地状态
      setMarkerFeaturesCopy((prev) => {
        const newMarkerFeatures = _.cloneDeep(prev);
        const markerIndex = _.findIndex(newMarkerFeatures, { feature_id: marker.feature_id });

        if (markerIndex !== -1) {
          newMarkerFeatures[markerIndex] = marker; // 回滚到原始状态
        }

        return newMarkerFeatures;
      });
      return;
    }

    aoiAlert(t('notification.success.updateMarker'), ALERT_TYPES.COMMON_SUCCESS);
    refetchMarkers();
  }, [updateFeatureRoi, t, refetchMarkers]);

  const handleDetectMarkerCenter = useCallback(async (marker) => {
    const pid = _.get(marker, 'product_id');
    const featureId = _.get(marker, 'feature_id');
    const res = await detectMarkerCenter({
      product_id: pid,
      step: _.get(marker, 'step'),
      roi: {
        type: _.get(marker, 'roi.type'),
        angle: _.get(marker, 'roi.angle'),
        points: _.get(marker, 'roi.points'),
      },
      feature_type: _.get(marker, 'feature_type'),
      feature_scope: _.get(marker, 'feature_scope'),
      line_item_params: _.get(marker, 'line_item_params'),
    });

    if (res.error) {
      aoiAlert(t('notification.error.detectMarkerCenter'), ALERT_TYPES.COMMON_ERROR);
      console.error('detectMarkerCenter error:', _.get(res, 'error.message', ''));
      return;
    }

    if (res.data) {
      aoiAlert(t('notification.success.detectMarkerCenter'), ALERT_TYPES.COMMON_SUCCESS);

      setMarkerFeaturesCopy((prev) => {
        const newMarkerFeatures = _.cloneDeep(prev);
        const markerIndex = _.findIndex(newMarkerFeatures, { feature_id: featureId });

        if (markerIndex !== -1) {
          // console.log('res', res);


          const responseType = _.get(res, 'data.type');
          let updatedRoi = { ...newMarkerFeatures[markerIndex].roi };

          if (responseType === 'CIRCLE' && _.get(res, 'data.circle')) {
            updatedRoi.center = _.get(res, 'data.circle.center');
            updatedRoi.radius = _.get(res, 'data.circle.radius');
          } else if (responseType === 'POLYGON' && _.get(res, 'data.polygon')) {
            updatedRoi.polygon = _.get(res, 'data.polygon');
          }

          newMarkerFeatures[markerIndex] = {
            ...newMarkerFeatures[markerIndex],
            roi: updatedRoi
          };
        }
        return newMarkerFeatures;
      });

      if (_.get(marker, 'line_item_params.aligner_2d.params.marker_type.param_enum.value') === 'TEMPLATE') {
        // render a dot at center to prove detect center works
        setFeatureIdsToDisplayCenterDot((prev) => [...prev, featureId]);
      }
    }
  }, [detectMarkerCenter, t]);

  const markerItems = useMemo(() => {
    if (!markerFeaturesCopy) return [];

    return markerFeaturesCopy.map((marker, index) => ({
      id: _.get(marker, 'feature_id', index),
      marker,
      index,
      title: `${t('productDefine.marker')} ${index + 1}`,
    }));
  }, [markerFeaturesCopy, t]);

  const MarkerItem = useMemo(() => {
    return React.memo(({ markerData, onLocate, onEdit, onDelete, onDetectCenter, onShapeChange }) => {
      const { marker, index, title } = markerData;

      const currentShape = _.get(marker, 'line_item_params.aligner_2d.params.marker_type.param_enum.value', 'CIRCLE');

      return (
        <div className='flex flex-row gap-2 self-stretch bg-[#ffffff0d] py-4 px-1' key={index}>
          <div className='flex self-stretch gap-1 items-center'>
              <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px] flex-shrink-0 flex-1 w-12'>
                {title}
              </span>
          </div>
          <div className='flex flex-col items-center gap-2'>

            <div className="flex items-start justify-start flex-row">
              <div className="flex items-center gap-2 px-1">
                <GreenDefaultButtonConfigProvider>
                  <Button
                    style={{ width: '62px' }}
                    onClick={() => onLocate(_.get(marker, 'feature_id', null))}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] '>
                      {t('productDefine.locate')}
                    </span>
                  </Button>
                </GreenDefaultButtonConfigProvider>
                <GreenDefaultButtonConfigProvider>
                  <Button
                    style={{ width: '62px' }}
                    onClick={() => onEdit(_.get(marker, 'feature_id', null))}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] '>
                      {t('productDefine.refine')}
                    </span>
                  </Button>
                </GreenDefaultButtonConfigProvider>
                <GreenDefaultButtonConfigProvider>
                  <Button
                    style={{ width: '62px' }}
                    onClick={() => onDelete(marker)}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] '>
                      {t('common.delete')}
                    </span>
                  </Button>
                </GreenDefaultButtonConfigProvider>
                <GreenDefaultButtonConfigProvider>
                  <Button
                    style={{ width: '90px' }}
                    onClick={() => onDetectCenter(marker)}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] '>
                      {t('productDefine.predictCenter')}
                    </span>
                  </Button>
                </GreenDefaultButtonConfigProvider>
              </div>
            </div>
            <div className='flex items-start justify-start gap-2 px-1 flex-row flex-1 w-full'>
              <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px] text-gray-400 my-auto'>
                {t('productDefine.markShapeType')}:
              </span>
              <Select
                value={currentShape}
                onChange={(value) => onShapeChange(value, marker)}
                style={{ width: 120 }}
                size="small"
                options={[
                  { value: 'CIRCLE', label: t('productDefine.circleMarker') },
                  { value: 'RECTANGLE', label: t('productDefine.rectangleMarker') },
                  { value: 'TEMPLATE', label: t('productDefine.templateMarker') },
                ]}
              />
            </div>
          </div>
        </div>
      );
    });
  }, [t]);

  if (!markerFeaturesCopy) {
    return null;
  }

  return (
    <div className='flex gap-0.5 flex-1 self-stretch px-0.5'>
      <div className='flex w-[490px] bg-[#ffffff0d] self-stretch flex-col'>
        <div className='flex p-4 gap-4 flex-col justify-center self-stretch'>
          <div className='flex flex-col self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
              {t('productDefine.markAlignPCB')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
              {t('productDefine.defineTwoMakers')}
            </span>
          </div>
          <GreenPrimaryButtonConfigProvider>
            <Button
              type='primary'
              onClick={handleAddMarker}
              disabled={!_.isUndefined(markerFeaturesCopy) && markerFeaturesCopy.length === 2}
            >
              <div className='flex items-center justify-center gap-2'>
                <img src='/icn/plus_black.svg' alt='plus' className='w-2.5 h-2.5' />
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                  {t('productDefine.marker')}
                </span>
              </div>
            </Button>
          </GreenPrimaryButtonConfigProvider>
        </div>
        <div className='flex p-1 gap-0.5 flex-1 flex-col self-stretch'>
          <div className='w-full h-[1px] bg-[#ffffff1a]' />
          {markerItems.map((markerData) => (
            <MarkerItem
              key={markerData.id}
              markerData={markerData}
              onLocate={handleLocateMarker}
              onEdit={handleEditMarker}
              onDelete={handleDeleteMarker}
              onDetectCenter={handleDetectMarkerCenter}
              onShapeChange={handleShapeChange}
            />
          ))}
        </div>
      </div>
      <Display
        curProduct={curProduct}
        selectedTool={selectedTool}
        setSelectedTool={setSelectedTool}
        refetchMarkers={refetchMarkers}
        selectedLocateFid={selectedLocateFid}
        setSelectedLocateFid={setSelectedLocateFid}
        selectedEditFid={selectedEditFid}
        setSelectedEditFid={setSelectedEditFid}
        markerFeatures={markerFeaturesCopy}
        shapeChangeUpdate={shapeChangeUpdate}
        featureIdsToDisplayCenterDot={featureIdsToDisplayCenterDot}
      />
    </div>
  );
};

export default MarkAlignPCB;