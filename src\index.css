@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  color: #FFF; /* default text color */
  background-color: #131313; /* default background color */
}

html, body {
  cursor: url('/icn/crosshair_color.svg'), corsshair;
}

span {
  line-height: 22px;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #1e1e1e;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
  transition: all 0.3s ease-in-out;
}

::-webkit-scrollbar-thumb:hover {
  background: #555; 
}


@font-face {
  font-family: 'Source Sans Pro';
  src: url('assets/fonts/SourceSans3-VariableFont_wght.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  src: url('assets/fonts/Inter-VariableFont_opsz,wght.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto';
  src: url('assets/fonts/Rubik-VariableFont_wght.ttf') format('truetype');
}

@font-face {
  font-family: 'Sansation';
  src: url('assets/fonts/Sansation_Regular.ttf') format('truetype');
}