import { ConfigProvider, Tabs } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ArrayGroup from './ArrayGroup';
import SmartPCBArray from './SmartPCBArray';
import Display from './Display';
import { useGetArrayRegisterationQuery, useGoldenRegisterArrayMutation, useLazyGetProductComponentQuery } from '../../../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';


const PCBArray = (props) => {
  const {
    curProduct,
    refetchCurProduct,
    setComponentsActiveTab,
  } = props;

  const { t } = useTranslation();

  const reftFormColRef = useRef(null);

  const [activeTab, setActiveTab] = useState('arrayGroup');
  const [allComponents, setAllComponents] = useState([]);
  const [selectedTool, setSelectedTool] = useState('transform');
  const [leftColHeight, setLeftColHeight] = useState(0);
  const [curSelectedMarker, setCurSelectedMarker] = useState(null);
  const [curMarkers, setCurMarkers] = useState({});
  const [isSubBoardSelectionRoiDisplayed, setIsSubBoardSelectionRoiDisplayed] = useState(true);
  const [previewArrayTransforms, setPreviewArrayTransforms] = useState(null);
  const [previewComponents, setPreviewComponents] = useState(null);
  const [panZoomToArrayBoardSelectionIdx, setPanZoomToArrayBoardSelectionIdx] = useState(null);

  const { data: arrayRegisteration, refetch: refetchArrayRegisteration } = useGetArrayRegisterationQuery({ product_id: Number(_.get(curProduct, 'product_id', 0)), step: 0 });
  const [lazyGetAllComponents] = useLazyGetProductComponentQuery();
  const [registerGoldenArray] = useGoldenRegisterArrayMutation();

  const handleRefetchAllComponents = async (productId, step) => {
    const res = await lazyGetAllComponents({ definition_product_id: productId, definition_step: step }, false);

    if (res.error) {
      console.error('getProductComponent error:', _.get(res, 'error.message', ''));
      return;
    }

    setAllComponents(res.data);
  };

  const handleFinishDrawingSelectionRoi = async (filteredComponents, productId, step, arrayTransform, selectionRoi) => {
    const res = await registerGoldenArray({
      product_id: productId,
      step: step,
      array_transforms: arrayTransform,
      selection_roi: selectionRoi,
      component_ids: _.map(filteredComponents, c => c.region_group_id),
    });

    if (res.error) {
      console.error('registerGoldenArray error:', _.get(res, 'error.message', ''));
      aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(productId, step);
    setPreviewArrayTransforms(null);
    setPreviewComponents(null);
  };

  useEffect(() => {
    if (_.isUndefined(curProduct) || !curProduct) return;
    handleRefetchAllComponents(Number(_.get(curProduct, 'product_id', 0)), 0);
    refetchArrayRegisteration();
    setLeftColHeight(reftFormColRef.current.offsetHeight);
  }, []);

  useEffect(() => {
    if (!previewArrayTransforms) {
      setPreviewComponents(null);
      return;
    }

    const baseComponents = allComponents.filter(c => _.get(c, 'array_index', 0) === 0);
    if (baseComponents.length === 0) {
      setPreviewComponents(null);
      return;
    }

    const newComponents = [...allComponents];
    for (const transform of previewArrayTransforms) {
      if (transform.array_index === 0) continue;
      for (const comp of baseComponents) {
        const pMin = _.get(comp, 'shape.points[0]', { x: 0, y: 0 });
        const pMax = _.get(comp, 'shape.points[1]', { x: 0, y: 0 });
        const translatedPMin = {
          x: pMin.x + _.get(transform, 'translation.x', 0),
          y: pMin.y + _.get(transform, 'translation.y', 0),
        };
        const translatedPMax = {
          x: pMax.x + _.get(transform, 'translation.x', 0),
          y: pMax.y + _.get(transform, 'translation.y', 0),
        };

        newComponents.push({
          ..._.cloneDeep(comp),
          array_index: transform.array_index,
          shape: {
            ..._.get(comp, 'shape', {}),
            points: [translatedPMin, translatedPMax],
            angle: _.get(comp, 'shape.angle', 0) + _.get(transform, 'rotation.angle', 0),
          }
        });
      }
    }

    setPreviewComponents(newComponents);
  }, [previewArrayTransforms, allComponents]);

  const arrayRegisterationForDisplay = previewArrayTransforms ?
    { ...arrayRegisteration, array_transforms: previewArrayTransforms } :
    arrayRegisteration;

  const allComponentsForDisplay = previewComponents || allComponents;

  // useEffect(() => {
  //   if (activeTab === 'smartPCBArray') {
  //     setIsSubBoardSelectionRoiDisplayed(true);
  //   } else {
  //     setIsSubBoardSelectionRoiDisplayed(false);
  //   }
  // }, [activeTab]);

  return (
    <div className='flex flex-1 gap-0.5 self-stretch rounded-[6px]'>
      <div className='flex w-[348px] flex-col self-stretch'>
        <div className='flex pt-1 items-center self-stretch border-t-[1px] border-t-gray-2'>
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  cardPadding: '1px 12px',
                  horizontalMargin: '4px 4px 0 4px',
                },
              }
            }}
          >
            <Tabs
              activeKey={activeTab}
              style={{ width: '100%' }}
              onChange={(key) => {
                setActiveTab(key);
              }}
              type='card'
              items={[
                {
                  label: <div className='flex py-2 gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'arrayGroup' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.arrayGroup')}
                    </span>
                  </div>,
                  key: 'arrayGroup',
                },
                {
                  label: <div className='flex py-2 gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'smartPCBArray' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.smartPCBArray')}
                    </span>
                  </div>,
                  key: 'smartPCBArray',
                  disabled: _.get(arrayRegisteration, 'array_transforms', []).length === 0 || _.isEmpty(_.get(arrayRegisteration, 'selection_roi', {})),
                }
              ]}
            />
          </ConfigProvider>
        </div>
        <div
          className='flex flex-col flex-1 self-stretch px-2 gap-2'
          ref={reftFormColRef}
        >
          {activeTab === 'arrayGroup' &&
            // manual mode
            <ArrayGroup
              arrayRegisteration={arrayRegisteration}
              setSelectedTool={setSelectedTool}
              leftColHeight={leftColHeight}
              curProduct={curProduct}
              refetchArrayRegisteration={refetchArrayRegisteration}
              handleRefetchAllComponents={handleRefetchAllComponents}
              setActiveTab={setActiveTab}
              setPanZoomToArrayBoardSelectionIdx={setPanZoomToArrayBoardSelectionIdx}
            />
          }
          {activeTab === 'smartPCBArray' &&
            <SmartPCBArray
              arrayRegisteration={arrayRegisteration}
              setActiveTab={setActiveTab}
              curSelectedMarker={curSelectedMarker}
              setCurSelectedMarker={setCurSelectedMarker}
              curMarkers={curMarkers}
              setCurMarkers={setCurMarkers}
              setSelectedTool={setSelectedTool}
              curProduct={curProduct}
              refetchArrayRegisteration={refetchArrayRegisteration}
              handleRefetchAllComponents={handleRefetchAllComponents}
              isSubBoardSelectionRoiDisplayed={isSubBoardSelectionRoiDisplayed}
              setIsSubBoardSelectionRoiDisplayed={setIsSubBoardSelectionRoiDisplayed}
              setPreviewArrayTransforms={setPreviewArrayTransforms}
              setComponentsActiveTab={setComponentsActiveTab}
            />
          }
        </div>
      </div>
      <div className='flex flex-1 self-stretch bg-[#000000cc]'>
        <Display
          curProduct={curProduct}
          allComponents={allComponentsForDisplay}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          handleFinishDrawingSelectionRoi={handleFinishDrawingSelectionRoi}
          arrayRegisteration={arrayRegisterationForDisplay}
          handleRefetchAllComponents={handleRefetchAllComponents}
          refetchArrayRegisteration={refetchArrayRegisteration}
          setCurSelectedMarker={setCurSelectedMarker}
          curSelectedMarker={curSelectedMarker}
          curMarkers={curMarkers}
          isSubBoardSelectionRoiDisplayed={isSubBoardSelectionRoiDisplayed}
          panZoomToArrayBoardSelectionIdx={panZoomToArrayBoardSelectionIdx}
          setPanZoomToArrayBoardSelectionIdx={setPanZoomToArrayBoardSelectionIdx}
        />
      </div>
    </div>
  );
};

export default PCBArray;