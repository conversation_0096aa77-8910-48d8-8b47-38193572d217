import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { applyTemplateString } from '../common/util';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDeleteProductMutation } from '../services/product';


const RemovePCBProgram = (props) => {
  const {
    isOpened,
    setIsOpened,
    curProduct,
    refetchAllProducts,
  } = props;

  const { t } = useTranslation();

  const [deleteProduct] = useDeleteProductMutation();

  const handleDleteProduct = async (productId) => {
    const res = await deleteProduct(productId).unwrap();
    await refetchAllProducts();
    setIsOpened(false);
  }

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('common.removePCBA')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col gap-4 p-4'>
        <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
          {applyTemplateString(t('common.thisActionWillRemovePCBA'), { productName: curProduct?.product_name })}
        </span>
        <div className='flex gap-2 items-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => {
              // Logic to remove the PCB program goes here
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              if (!curProduct.product_id) {
                return;
              }
              // Logic to confirm the removal goes here
              handleDleteProduct(Number(curProduct?.product_id));
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.remove')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default RemovePCBProgram;