import { ConfigProvider, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import i18n from '../i18n';
import enLocale from 'antd/es/locale/en_US';
import cnLocale from 'antd/es/locale/zh_CN';
import styled from 'styled-components';


const CommonTable = (props) => {
  const {
    cols,
    data,
    onPageChange,
    total,
    isLoading,
    setPageSize,
    rowHoverable,
    onRow,
  } = props;

  const containerRef = useRef(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });

  useEffect(() => {
    setPagination((prev) => ({
      ...prev,
      total: total,
    }));
  }, [total]);

  useEffect(() => {
    if (!containerRef.current) return;

    const handleResize = () => {
      if (!containerRef.current) return;
      const tableTbodyHeight = containerRef.current.offsetHeight - 32 - 43; // 32 for pagination 43 for header row
      const pageSize = Math.max(Math.floor(tableTbodyHeight / 43), 1);
      setPagination((prev) => ({
        ...prev,
        pageSize,
      }));
      if (_.isFunction(setPageSize)) setPageSize(pageSize);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.current);

    if (_.isInteger(total) && total > 0) {
      setPagination((prev) => ({
        ...prev,
        total: total,
      }));
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className='relative w-full h-full'>
      <div className='absolute top-0 left-0 w-full h-full z-[10]' ref={containerRef} />
      <div className='absolute top-0 left-0 w-full h-full z-[11]'>
        <CustomTable
          bordered={false}
          columns={cols}
          dataSource={data}
          pagination={{
            pageSize: pagination.pageSize,
            total: pagination.total,
            current: pagination.current,
            hideOnSinglePage: true,
            showSizeChanger: false,
            showQuickJumper: true,
            locale: i18n.language === 'cn' ? cnLocale.Pagination : enLocale.Pagination,
          }}
          onChange={(pagination) => {
            if (_.isFunction(onPageChange)) onPageChange(pagination.current);
            setPagination((prev) => ({
              ...prev,
              current: pagination.current,
            }));
          }}
          rowHoverable={_.isUndefined(rowHoverable) ? false : rowHoverable}
          loading={_.isBoolean(isLoading) ? isLoading : false}
          locale={i18n.language === 'cn' ? cnLocale.Table : enLocale.Table}
          onRow={_.isFunction(onRow) ? onRow : () => ({})}
        />
      </div>
    </div>
  );
};

const CustomTable = styled(Table)`
  .ant-table-pagination {
    margin: 0 !important;
  }
`;

export default CommonTable;