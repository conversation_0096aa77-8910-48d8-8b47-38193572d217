import { Select } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import i18n from '../../../i18n';

function Language() {
	const { t } = useTranslation();

	return (
		<div className="flex py-4 px-[140px] flex-1 self-stretch justify-center">
			<div className="flex w-[800px] flex-col gap-0.5 self-stretch">
				<div className="flex p-6 items-center self-stretch">
					<span className="font-source text-[20px] font-normal leading-[normal] tracking-[0.6px]">
						{t('common.systemLanguage')}
					</span>
				</div>
				{/* add system language desc */}
				<div className="flex items-center gap-2 px-4">
					<img
						src="/icn/language_white.svg"
						alt="language"
						className="h-4 w-4"
					/>
					<span className="font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]">
						{t('common.systemLanguageDesc')}
					</span>
				</div>

				<div className="flex flex-col items-center gap-[48px] flex-1 self-stretch">
					<div className="flex flex-col items-start gap-8 self-stretch px-4 py-6">
						<div className="flex items-center gap-2  ">
							<span className="font-source text-[14px] font-normal flex-1">
								{t('common.language')}
							</span>
							<Select
								style={{ width: '120px' }}
								options={[
									{ label: t('common.english'), value: 'en' },
									{ label: t('common.chinese'), value: 'cn' },
								]}
								value={i18n.language}
								onChange={(value) => {
									i18n.changeLanguage(value);
									setIsOpened(false);
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}

export default Language;
