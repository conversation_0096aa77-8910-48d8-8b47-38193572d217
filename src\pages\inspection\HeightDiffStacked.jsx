import React, { useEffect, useRef } from 'react';
import { ThreeDDisplayWrapper, HiddenCanvasDimensionCalcDiv } from '../../common/styledComponent';
import _, { debounce } from 'lodash';
import { Button, Checkbox } from 'antd';
import { highResoluCroppedDisplayMegaPixelCount, serverHost } from '../../common/const';
import HeightDiffViewer from '../../viewer/HeightDiffViewer';


const HeightDiffStacked = (props) => {
  const {
    goldenComponentInfo,
    ipcComponentInfo,
    goldenFeatureInfo,
    ipcFeatureInfo,
    goldenProduct,
    isIpcCloudVisible,
    isGoldenCloudVisible,
    pointCloudDisplayedView,
  } = props;
  
  const canvasRef = useRef(null);
  const canvasDimensionCalcDivRef = useRef(null);
  const viewerRef = useRef(null);

  const loadScene = async (viewer, goldenFeatureInfo, ipcFeatureInfo, goldenComponentInfo) => {
    let ipcCloudUri = `${serverHost}/blob?type=point_cloud`;
    ipcCloudUri += `&color_uri=${encodeURIComponent(_.get(ipcFeatureInfo, 'component_color_map_uri', ''))}`;
    ipcCloudUri += `&depth_uri=${encodeURIComponent(_.get(ipcFeatureInfo, 'component_depth_map_uri', ''))}`;
    ipcCloudUri += `&x_min=${_.get(ipcFeatureInfo, 'roi.points[0].x', 0)}`;
    ipcCloudUri += `&y_min=${_.get(ipcFeatureInfo, 'roi.points[0].y', 0)}`;
    ipcCloudUri += `&x_max=${_.get(ipcFeatureInfo, 'roi.points[1].x', 0)}`;
    ipcCloudUri += `&y_max=${_.get(ipcFeatureInfo, 'roi.points[1].y', 0)}`;
    if (_.isNumber(_.get(ipcFeatureInfo, 'roi.angle', null))) ipcCloudUri += `&angle=${_.get(ipcFeatureInfo, 'roi.angle', 0)}`;
    ipcCloudUri += `&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
    ipcCloudUri += `&t=${Date.now().valueOf()}`; // to prevent cache

    let goldenCloudUri = `${serverHost}/blob?type=point_cloud`;
    goldenCloudUri += `&color_uri=${encodeURIComponent(_.get(goldenComponentInfo, 'color_map_uri', ''))}`;
    goldenCloudUri += `&depth_uri=${encodeURIComponent(_.get(goldenComponentInfo, 'depth_map_uri', ''))}`;
    goldenCloudUri += `&x_min=${_.get(goldenFeatureInfo, 'roi.points[0].x', 0) - _.get(goldenComponentInfo, 'shape.points[0].x', 0)}`;
    goldenCloudUri += `&y_min=${_.get(goldenFeatureInfo, 'roi.points[0].y', 0) - _.get(goldenComponentInfo, 'shape.points[0].y', 0)}`;
    goldenCloudUri += `&x_max=${_.get(goldenFeatureInfo, 'roi.points[1].x', 0) - _.get(goldenComponentInfo, 'shape.points[0].x', 0)}`;
    goldenCloudUri += `&y_max=${_.get(goldenFeatureInfo, 'roi.points[1].y', 0) - _.get(goldenComponentInfo, 'shape.points[0].y', 0)}`;
    if (_.isNumber(_.get(goldenFeatureInfo, 'roi.angle', null))) goldenCloudUri += `&angle=${_.get(goldenFeatureInfo, 'roi.angle', 0)}`;
    goldenCloudUri += `&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
    goldenCloudUri += `&t=${Date.now().valueOf()}`; // to prevent cache

    viewer.loadCroppedGoldenAndIpcCloud({
      goldenCloudUri,
      ipcCloudUri,
    });
  };

  useEffect(() => {
    if (!viewerRef.current) return;
    viewerRef.current.updatePointCloudsVis(isGoldenCloudVisible, isIpcCloudVisible);
  }, [
    isGoldenCloudVisible,
    isIpcCloudVisible,
  ]);

  useEffect(() => {
    if (!viewerRef.current) return;
    viewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
  }, [pointCloudDisplayedView]);

  useEffect(() => {
    if (!viewerRef.current) return;

    loadScene(viewerRef.current, goldenFeatureInfo, ipcFeatureInfo, goldenComponentInfo);
  }, [
    goldenFeatureInfo,
    ipcFeatureInfo,
    goldenComponentInfo,
  ]);

  useEffect(() => {
    if (!canvasRef.current || !canvasDimensionCalcDivRef.current) return;

    const viewer = new HeightDiffViewer(
      canvasRef.current,
      canvasDimensionCalcDivRef.current.offsetHeight,
      canvasDimensionCalcDivRef.current.offsetWidth,
    );

    viewerRef.current = viewer;

    const updateCanvasDimension = () => {
      if (!viewer || !canvasDimensionCalcDivRef.current) return;
      viewerRef.current.updateSceneSize(canvasDimensionCalcDivRef.current.offsetWidth, canvasDimensionCalcDivRef.current.offsetHeight);
    };

    const debounceUpdateCanvasDimension = debounce(updateCanvasDimension, 300);

    window.addEventListener('resize', debounceUpdateCanvasDimension);

    if (!_.isEmpty(goldenFeatureInfo) && !_.isEmpty(ipcFeatureInfo) && !_.isEmpty(goldenComponentInfo)) {
      loadScene(viewer, goldenFeatureInfo, ipcFeatureInfo, goldenComponentInfo);
      viewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    return () => {
      if (viewer) {
        viewer.clearScene();
        viewer.destroy();
        viewerRef.current = null;
      }
      
      window.removeEventListener('resize', debounceUpdateCanvasDimension);
    };
  }, []);

  return (
    <div className='flex flex-col w-full h-full gap-2 pt-2'>
      <div className='relative w-full h-full'>
        <ThreeDDisplayWrapper>
          <canvas ref={canvasRef} />
        </ThreeDDisplayWrapper>
        <HiddenCanvasDimensionCalcDiv ref={canvasDimensionCalcDivRef} />
      </div>
    </div>
  );
};

export default HeightDiffStacked;