import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import _ from 'lodash';


export const accountApi = createApi({
  reducerPath: 'accountApi',
  baseQuery,
  tagTypes: ['Account'],
  endpoints: (build) => ({
    getAccountList: build.query({
      query: () => ({
        url: '/account',
        method: 'GET',
      }),
    }),
    newAccount: build.mutation({
      query: (body) => ({
        url: '/account/create',
        method: 'POST',
        body,
      }),
    }),
    removeAccount: build.query({
      query: (id) => ({
        url: `/account`,
        method: 'DELETE',
        params: {
          account_id: id,
        }
      }),
    }),
  }),
});

export const {
  useGetAccountListQuery,
  useNewAccountMutation,
  useLazyRemoveAccountQuery,
} = accountApi;