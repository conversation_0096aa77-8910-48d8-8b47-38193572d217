import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import postprocessor from 'i18next-sprintf-postprocessor';
import { text } from './common/translation';


const transformToI18NextFormat = (obj) => {
  if (obj.en && obj.cn) {
    return { en: obj.en, cn: obj.cn };
  }

  const result = {};

  for (const key in obj) {
    const transformed = transformToI18NextFormat(obj[key]);

    if (transformed.en && transformed.cn) {
      if (!result.en) result.en = {};
      if (!result.cn) result.cn = {};

      result.en[key] = transformed.en;
      result.cn[key] = transformed.cn;
    } else {
      result[key] = transformed;
    }
  }

  return result;
}

const translation = transformToI18NextFormat(text);


i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .use(postprocessor) // i18next.t('key2', { postProcess: 'sprintf', sprintf: { users: 1000 } })
  .init({
    resources: {
      en: {
        translation: translation.en,
      },
      cn: {
        translation: translation.cn,
      },
    },
    fallbackLng: 'en',
    // debug: true,
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;