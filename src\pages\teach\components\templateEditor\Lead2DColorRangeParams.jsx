import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Button, Select, Slider, Tooltip } from 'antd';
import _ from 'lodash';
import { useUpdateAgentParamsMutation } from '../../../../services/product';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import HSVColorRangePicker from '../../../../modal/HSVColorRangePicker';
import './CustomSlider.css';
import {
  leadInspection2D,
  solderValidRatioRange,
  liftedLeadTipValidRatioRange,
  solderColorStartVal,
  solderColorEndVal,
  padColorStartVal,
  padColorEndVal,
  tipColorStartVal,
  tipColorEndVal,
  solderColorStartHue,
  solderColorEndHue,
  padColorStartHue,
  padColorEndHue,
  tipColorStartHue,
  tipColorEndHue,
  solderColorCenterHue,
  solderColorCenterSat,
  padColorCenterHue,
  padColorCenterSat,
  tipColorCenterHue,
  tipColorCenterSat,
} from '../../../../common/const';


const Lead2DColorRangeParams = (props) => {
	const {
    statsQuery,
    setStatsQuery,
		isAgentParamStatsOpen,
    setIsAgentParamStatsOpen,
    selectedGroupFeatureTypeAgentParams,
    lintItemName,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
    updateAllFeaturesState,
    setSelectedGroupFeatureTypeAgentParams,
	} = props;

  const { t } = useTranslation();

        const okMinValRef = useRef(null);
        const okMaxValRef = useRef(null);
        const startValRef = useRef(null);
        const endValRef = useRef(null);


	const [manualUpdateKey, setManualUpdateKey] = useState(0);
	const [selectedColorType, setSelectedColorType] = useState('solderColor');
	const [isSolderColorRangeModalOpen, setIsSolderColorRangeModalOpen] = useState(false);
	const [isPadColorRangeModalOpen, setIsPadColorRangeModalOpen] = useState(false);
	const [isTipColorRangeModalOpen, setIsTipColorRangeModalOpen] = useState(false);	const [displayedSolderOkMinValue, setDisplayedSolderOkMinValue] = useState(
		_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_min`, 0)
	);
	const [displayedSolderOkMaxValue, setDisplayedSolderOkMaxValue] = useState(
		_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_max`, 1)
	);
	const [displayedTipOkMinValue, setDisplayedTipOkMinValue] = useState(
		_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${liftedLeadTipValidRatioRange}.param_range.ok_min`, 0)
	);
        const [displayedTipOkMaxValue, setDisplayedTipOkMaxValue] = useState(
                _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${liftedLeadTipValidRatioRange}.param_range.ok_max`, 1)
        );

        const [displayedStartVal, setDisplayedStartVal] = useState(
                _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorStartVal}.param_int.value`, 0)
        );
        const [displayedEndVal, setDisplayedEndVal] = useState(
                _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorEndVal}.param_int.value`, 100)
        );

  const [updateFeatureParams] = useUpdateAgentParamsMutation();

	useEffect(() => {
    setDisplayedSolderOkMinValue(
			_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_min`, 0)
		);
		setDisplayedSolderOkMaxValue(
			_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderValidRatioRange}.param_range.ok_max`, 1)
		);
		setDisplayedTipOkMinValue(
			_.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${liftedLeadTipValidRatioRange}.param_range.ok_min`, 0)
		);
                setDisplayedTipOkMaxValue(
                        _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${liftedLeadTipValidRatioRange}.param_range.ok_max`, 1)
                );

                const startVal =
                        selectedColorType === 'solderColor'
                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorStartVal}.param_int.value`, 0)
                                : selectedColorType === 'padColor'
                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorStartVal}.param_int.value`, 0)
                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorStartVal}.param_int.value`, 0);
                const endVal =
                        selectedColorType === 'solderColor'
                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorEndVal}.param_int.value`, 100)
                                : selectedColorType === 'padColor'
                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorEndVal}.param_int.value`, 100)
                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorEndVal}.param_int.value`, 100);
                setDisplayedStartVal(startVal);
                setDisplayedEndVal(endVal);
                setManualUpdateKey(prevKey => prevKey + 1);
        }, [selectedGroupFeatureTypeAgentParams]);

        // Update manualUpdateKey when selectedColorType changes to force HSVColorRangePicker re-initialization
        useEffect(() => {
                const startVal =
                        selectedColorType === 'solderColor'
                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorStartVal}.param_int.value`, 0)
                                : selectedColorType === 'padColor'
                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorStartVal}.param_int.value`, 0)
                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorStartVal}.param_int.value`, 0);
                const endVal =
                        selectedColorType === 'solderColor'
                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorEndVal}.param_int.value`, 100)
                                : selectedColorType === 'padColor'
                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorEndVal}.param_int.value`, 100)
                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorEndVal}.param_int.value`, 100);
                setDisplayedStartVal(startVal);
                setDisplayedEndVal(endVal);
                setManualUpdateKey(prevKey => prevKey + 1);
        }, [selectedColorType]);

	return (
		<Fragment>
			{/* Color Type Selection */}
			<div className="flex items-center self-stretch flex-1 w-full gap-2">
				<div className='flex items-center'>
					<span
						className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap overflow-hidden text-ellipsis'
						title={t(`agentParamName.lead_inspection_2d_v2.colorRange`)}
					>
						{t(`agentParamName.lead_inspection_2d_v2.colorRange`) || 'Color Range'}
					</span>
				</div>
				<div className="flex items-center self-stretch gap-2">
					<Select
						style={{ width: '100%' }}
						value={selectedColorType}
						onChange={setSelectedColorType}
						options={[
							{
								label: t(`agentParamName.lead_inspection_2d_v2.solderColorRange`) || 'Solder Color Range',
								value: 'solderColor'
							},
							{
								label: t(`agentParamName.lead_inspection_2d_v2.padColorRange`) || 'Pad Color Range',
								value: 'padColor'
							},
							{
								label: t(`agentParamName.lead_inspection_2d_v2.tipColorRange`) || 'Tip Color Range',
								value: 'tipColor'
							}
						]}
					/>
				</div>

			</div>

			{/* Valid Ratio Range Slider - Only for solder and tip colors */}
			{(selectedColorType === 'solderColor' || selectedColorType === 'tipColor') && (
					<div className='flex items-center self-stretch flex-row gap-2 w-full'>
						<span
							// className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap overflow-hidden text-ellipsis'
							className="font-source text-[12px] font-normal leading-[150%] pt-0.5 w-full overflow-hidden whitespace-nowrap text-ellipsis"
							title={selectedColorType === 'solderColor'
								? t(`agentParamName.${leadInspection2D}.solder_valid_ratio_range`)
								: t(`agentParamName.${leadInspection2D}.lifted_lead_tip_valid_ratio_range`)}
						>
							{selectedColorType === 'solderColor'
								? t(`agentParamName.${leadInspection2D}.solder_valid_ratio_range`)
								: t(`agentParamName.${leadInspection2D}.lifted_lead_tip_valid_ratio_range`)}
						</span>
						<Slider
							range
							step={0.001}
							className='custom-slider'
							styles={{
								track: { background: '#81F499' },
								rail: { background: '#F46D6D' },
							}}
							style={{
								marginBottom: '10px',
								width: '100%',
							}}
							min={0}
							max={100}
							value={[
								selectedColorType === 'solderColor'
									? displayedSolderOkMinValue
									: displayedTipOkMinValue,
								selectedColorType === 'solderColor'
									? displayedSolderOkMaxValue
									: displayedTipOkMaxValue,
							]}
							onChange={(value) => {
								if (value[0] >= value[1]) {
									return;
								}
								if (selectedColorType === 'solderColor') {
									setDisplayedSolderOkMinValue(value[0]);
									setDisplayedSolderOkMaxValue(value[1]);
								} else if (selectedColorType === 'tipColor') {
									setDisplayedTipOkMinValue(value[0]);
									setDisplayedTipOkMaxValue(value[1]);
								}
								okMinValRef.current = value[0];
								okMaxValRef.current = value[1];
							}}
							onChangeComplete={(value) => {
								const rangeParam = selectedColorType === 'solderColor' ? solderValidRatioRange : liftedLeadTipValidRatioRange;

								const payload = {
									product_id: goldenProductId,
									step: 0,
									feature_type: selectedFeatureType,
									line_item_params: {
										..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
										[lintItemName]: {
											..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
											params: {
												..._.get(
													selectedGroupFeatureTypeAgentParams,
													`line_item_params.${lintItemName}.params`
												),
												[rangeParam]: {
													..._.get(
														selectedGroupFeatureTypeAgentParams,
														`line_item_params.${lintItemName}.params.${rangeParam}`
													),
													param_range: {
														..._.get(
															selectedGroupFeatureTypeAgentParams,
															`line_item_params.${lintItemName}.params.${rangeParam}.param_range`
														),
														ok_min: value[0],
														ok_max: value[1],
													},
												},
											},
										},
									},
								};

								if (_.isInteger(selectedCid) && selectedScope === 'component') {
									payload.component_id = selectedCid;
								} else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
									payload.part_no = selectedPartNo;
								} else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
									payload.package_no = selectedPackageNo;
								}

								const run = async (payload, selectedGroupFeatureTypeAgentParams) => {
									const res = await updateFeatureParams(payload);

									if (res.error) {
										aoiAlert(
											t('notification.error.updateFeature'),
											ALERT_TYPES.COMMON_ERROR
										);
										console.error('update feature failed', res.error.message);
										return;
									}

									if (!_.isEmpty(res.data)) {
										await updateAllFeaturesState(_.get(res, 'data.feature_ids', []), 'updateGroupParam', _.get(res, 'data.line_item_params', {}));
									}

									setSelectedGroupFeatureTypeAgentParams(selectedGroupFeatureTypeAgentParams);
								};

								run(payload, {
									...selectedGroupFeatureTypeAgentParams,
									line_item_params: {
										..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params', {}),
										...payload.line_item_params
									},
								});
							}}
						/>
					</div>
				)}

			{/* HSV Color Range Picker - Always visible for selected color type */}
                        <HSVColorRangePicker
                                key={manualUpdateKey}
                                isOpened={true} // Always opened, no modal
                                setIsOpened={() => {}} // No-op since we don't use modal
                                sh={
                                        selectedColorType === 'solderColor'
                                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorStartHue}.param_int.value`, 0)
                                                : selectedColorType === 'padColor'
                                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorStartHue}.param_int.value`, 0)
                                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorStartHue}.param_int.value`, 0)
                                }
                                ss={100}
                                sv={100}
                                eh={
                                        selectedColorType === 'solderColor'
                                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorEndHue}.param_int.value`, 0)
                                                : selectedColorType === 'padColor'
                                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorEndHue}.param_int.value`, 0)
                                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorEndHue}.param_int.value`, 0)
                                }
                                es={100}
                                ev={100}
                                ch={
                                        selectedColorType === 'solderColor'
                                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorCenterHue}.param_int.value`, 0)
						: selectedColorType === 'padColor'
							? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorCenterHue}.param_int.value`, 0)
							: _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorCenterHue}.param_int.value`, 0)
				}
                                cs={
                                        selectedColorType === 'solderColor'
                                                ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${solderColorCenterSat}.param_int.value`, 0)
                                                : selectedColorType === 'padColor'
                                                        ? _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${padColorCenterSat}.param_int.value`, 0)
                                                        : _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${tipColorCenterSat}.param_int.value`, 0)
                                }
                                cv={100}
                                noValueSlider={true}
                                updateAllFeaturesState={updateAllFeaturesState}
                                submit={(
                                        sh,
                                        ss,
                                        _sv,
                                        eh,
                                        es,
                                        _ev,
                                        ch,
                                        cs,
                                        cv,
                                ) => {
					const payload = {
						product_id: goldenProductId,
						step: 0,
						feature_type: selectedFeatureType,
						line_item_params: {
							..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
							[lintItemName]: {
								..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
								params: {
									..._.get(
										selectedGroupFeatureTypeAgentParams,
										`line_item_params.${lintItemName}.params`
									),
									[selectedColorType === 'solderColor' && solderColorStartHue
									|| selectedColorType === 'padColor' && padColorStartHue
									|| selectedColorType === 'tipColor' && tipColorStartHue]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorStartHue
												|| selectedColorType === 'padColor' && padColorStartHue
												|| selectedColorType === 'tipColor' && tipColorStartHue
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorStartHue
													|| selectedColorType === 'padColor' && padColorStartHue
													|| selectedColorType === 'tipColor' && tipColorStartHue
												}.param_int`
											),
											value: sh,
										},
									},
									[selectedColorType === 'solderColor' && solderColorStartVal
									|| selectedColorType === 'padColor' && padColorStartVal
									|| selectedColorType === 'tipColor' && tipColorStartVal]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorStartVal
												|| selectedColorType === 'padColor' && padColorStartVal
												|| selectedColorType === 'tipColor' && tipColorStartVal
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorStartVal
													|| selectedColorType === 'padColor' && padColorStartVal
													|| selectedColorType === 'tipColor' && tipColorStartVal
												}.param_int`
											),
                                                                               value: displayedStartVal,
										},
									},
									[selectedColorType === 'solderColor' && solderColorEndHue
									|| selectedColorType === 'padColor' && padColorEndHue
									|| selectedColorType === 'tipColor' && tipColorEndHue]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorEndHue
												|| selectedColorType === 'padColor' && padColorEndHue
												|| selectedColorType === 'tipColor' && tipColorEndHue
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorEndHue
													|| selectedColorType === 'padColor' && padColorEndHue
													|| selectedColorType === 'tipColor' && tipColorEndHue
												}.param_int`
											),
											value: eh,
										},
									},
									[selectedColorType === 'solderColor' && solderColorEndVal
									|| selectedColorType === 'padColor' && padColorEndVal
									|| selectedColorType === 'tipColor' && tipColorEndVal]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorEndVal
												|| selectedColorType === 'padColor' && padColorEndVal
												|| selectedColorType === 'tipColor' && tipColorEndVal
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorEndVal
													|| selectedColorType === 'padColor' && padColorEndVal
													|| selectedColorType === 'tipColor' && tipColorEndVal
												}.param_int`
											),
                                                                               value: displayedEndVal,
										},
									},
									[selectedColorType === 'solderColor' && solderColorCenterHue
									|| selectedColorType === 'padColor' && padColorCenterHue
									|| selectedColorType === 'tipColor' && tipColorCenterHue]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorCenterHue
												|| selectedColorType === 'padColor' && padColorCenterHue
												|| selectedColorType === 'tipColor' && tipColorCenterHue
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorCenterHue
													|| selectedColorType === 'padColor' && padColorCenterHue
													|| selectedColorType === 'tipColor' && tipColorCenterHue
												}.param_int`
											),
											value: ch,
										},
									},
									[selectedColorType === 'solderColor' && solderColorCenterSat
									|| selectedColorType === 'padColor' && padColorCenterSat
									|| selectedColorType === 'tipColor' && tipColorCenterSat]: {
										..._.get(
											selectedGroupFeatureTypeAgentParams,
											`line_item_params.${lintItemName}.params.${
												selectedColorType === 'solderColor' && solderColorCenterSat
												|| selectedColorType === 'padColor' && padColorCenterSat
												|| selectedColorType === 'tipColor' && tipColorCenterSat
											}`
										),
										param_int: {
											..._.get(
												selectedGroupFeatureTypeAgentParams,
												`line_item_params.${lintItemName}.params.${
													selectedColorType === 'solderColor' && solderColorCenterSat
													|| selectedColorType === 'padColor' && padColorCenterSat
													|| selectedColorType === 'tipColor' && tipColorCenterSat
												}.param_int`
											),
											value: cs,
										},
									},
								},
							},
						},
					};

					if (_.isInteger(selectedCid) && selectedScope === 'component') {
						payload.component_id = selectedCid;
					} else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
						payload.part_no = selectedPartNo;
					} else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
						payload.package_no = selectedPackageNo;
					}

					const run = async (payload, selectedGroupFeatureTypeAgentParams) => {
						const res = await updateFeatureParams(payload);

						if (res.error) {
							aoiAlert(
								t('notification.error.updateFeature'),
								ALERT_TYPES.COMMON_ERROR
							);
							console.error('update feature failed', res.error.message);
							return;
						}

						if (!_.isEmpty(res.data)) {
							await updateAllFeaturesState(_.get(res, 'data.feature_ids', []), 'updateGroupParam', _.get(res, 'data.line_item_params', {}));
						}

						setSelectedGroupFeatureTypeAgentParams(selectedGroupFeatureTypeAgentParams);
					};

                                        run(payload, {
                                                ...selectedGroupFeatureTypeAgentParams,
                                                line_item_params: {
                                                        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params', {}),
                                                        ...payload.line_item_params
                                                },
                                        });
                                }}
                        />
                        <Slider
                                range
                                step={1}
                                className='custom-slider w-full'
                                styles={{
                                        track: { background: 'transparent' },
                                        rail: { background: 'linear-gradient(to right, black, white)' },
                                }}
                                min={0}
                                max={100}
                                value={[displayedStartVal, displayedEndVal]}
                                onChange={(value) => {
                                        if (value[0] >= value[1]) return;
                                        setDisplayedStartVal(value[0]);
                                        setDisplayedEndVal(value[1]);
                                        startValRef.current = value[0];
                                        endValRef.current = value[1];
                                }}
                                onChangeComplete={(value) => {
                                        const startParam =
                                                selectedColorType === 'solderColor'
                                                        ? solderColorStartVal
                                                        : selectedColorType === 'padColor'
                                                                ? padColorStartVal
                                                                : tipColorStartVal;
                                        const endParam =
                                                selectedColorType === 'solderColor'
                                                        ? solderColorEndVal
                                                        : selectedColorType === 'padColor'
                                                                ? padColorEndVal
                                                                : tipColorEndVal;

                                        const payload = {
                                                product_id: goldenProductId,
                                                step: 0,
                                                feature_type: selectedFeatureType,
                                                line_item_params: {
                                                        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
                                                        [lintItemName]: {
                                                                ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
                                                                params: {
                                                                        ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params`),
                                                                        [startParam]: {
                                                                                ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${startParam}`),
                                                                                param_int: {
                                                                                        ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${startParam}.param_int`),
                                                                                        value: value[0],
                                                                                },
                                                                        },
                                                                        [endParam]: {
                                                                                ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${endParam}`),
                                                                                param_int: {
                                                                                        ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}.params.${endParam}.param_int`),
                                                                                        value: value[1],
                                                                                },
                                                                        },
                                                                },
                                                        },
                                                },
                                        };

                                        if (_.isInteger(selectedCid) && selectedScope === 'component') {
                                                payload.component_id = selectedCid;
                                        } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
                                                payload.part_no = selectedPartNo;
                                        } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
                                                payload.package_no = selectedPackageNo;
                                        }

                                        const run = async (payload, selectedGroupFeatureTypeAgentParams) => {
                                                const res = await updateFeatureParams(payload);

                                                if (res.error) {
                                                        aoiAlert(
                                                                t('notification.error.updateFeature'),
                                                                ALERT_TYPES.COMMON_ERROR
                                                        );
                                                        console.error('update feature failed', res.error.message);
                                                        return;
                                                }

                                                if (!_.isEmpty(res.data)) {
                                                        await updateAllFeaturesState(_.get(res, 'data.feature_ids', []), 'updateGroupParam', _.get(res, 'data.line_item_params', {}));
                                                }

                                                setSelectedGroupFeatureTypeAgentParams(selectedGroupFeatureTypeAgentParams);
                                        };

                                        run(payload, {
                                                ...selectedGroupFeatureTypeAgentParams,
                                                line_item_params: {
                                                        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params', {}),
                                                        ...payload.line_item_params,
                                                },
                                        });
                                }}
                        />


		</Fragment>
	);
};

export default Lead2DColorRangeParams;
