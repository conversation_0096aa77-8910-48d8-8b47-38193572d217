import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CommonTable from '../../components/CommonTable';
import { Dropdown, Select, Table } from 'antd';
import { localStorageKeys, userRoles } from '../../common/const';
import AddMember from '../../modal/AddMember';
import _ from 'lodash';
import { useGetAccountListQuery, useLazyRemoveAccountQuery } from '../../services/account';
import { backendTimestampToDisplayString } from '../../common/util';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const Team = () => {
  const { t } = useTranslation();

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 10,
  });
  const [isAddMemberModalOpened, setIsAddMemberModalOpened] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);

  const { data: accountList, refetch: refetchAccountList } = useGetAccountListQuery();
  const [removeAccount] = useLazyRemoveAccountQuery();

  useEffect(() => {
    refetchAccountList();
  }, []);

  return (
    <Fragment>
      <AddMember
        isOpened={isAddMemberModalOpened}
        setIsOpened={setIsAddMemberModalOpened}
        selectedRole={selectedRole}
        setSelectedRole={setSelectedRole}
        refetchAccountList={refetchAccountList}
      />
      <div className='flex py-4 px-[140px] flex-1 self-stretch justify-center'>
        <div className='flex w-[800px] flex-col gap-0.5 self-stretch'>
          <div className='flex p-6 items-center self-stretch'>
            <span className='font-source text-[20px] font-normal leading-[normal] tracking-[0.6px]'>
              {t('settings.members')}
            </span>
          </div>
          <div className='flex flex-col items-center gap-[48px] flex-1 self-stretch'>
            <Table
              style={{ width: '100%' }}
              bordered={false}
              columns={[
                {
                  key: 'username',
                  title: <span className='font-source text-[12px] font-semibold leading-[150%] text-gray-5'>
                    {t('settings.username')}
                  </span>,
                  render: (text, record) => (
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {record.name}
                    </span>
                  ),
                },
                // {
                //   key: 'lastActive',
                //   title: <span className='font-source text-[12px] font-semibold leading-[150%] text-gray-5'>
                //     {t('settings.lastActive')}
                //   </span>,
                //   render: (text, record) => (
                //     <span className='font-source text-[12px] font-normal leading-[150%]'>
                //       {record.lastActive}
                //     </span>
                //   ),
                // },
                {
                  key: 'created',
                  title: <span className='font-source text-[12px] font-semibold leading-[150%] text-gray-5'>
                    {t('common.created')}
                  </span>,
                  render: (text, record) => {
                    return (
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {record.created_at ? Date(record.created_at) : ''}
                      </span>
                    );
                  },
                },
                {
                  key: 'role',
                  title: <span className='font-source text-[12px] font-semibold leading-[150%] text-gray-5'>
                    {t('settings.role')}
                  </span>,
                  render: (text, record) => {
                    // return <Select
                    //   size='small'
                    //   style={{ width: '120px' }}
                    //   options={[
                    //     {
                    //       label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    //         {t('settings.programmer')}
                    //       </span>,
                    //       value: userRoles.programmer,
                    //     },
                    //     {
                    //       label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    //         {t('settings.operator')}
                    //       </span>,
                    //       value: userRoles.operator,
                    //     },
                    //     {
                    //       label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    //         {t('common.admin')}
                    //       </span>,
                    //       value: userRoles.admin,
                    //     }
                    //   ]}
                    //   value={record.role}
                    // />;
                    return (
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t(`common.${record.role}`)}
                      </span>
                    );
                  },
                },
                {
                  key: 'setting',
                  title: <></>,
                  render: (text, record) => (
                    <Dropdown
                      menu={{
                        items: [
                          // {
                          //   key: '1',
                          //   label: <span className='font-source text-[14px] font-normal leading-[150%]'>
                          //     {t('settings.viewChangePwd')}
                          //   </span>,
                          // },
                          {
                            key: '2',
                            label: <span className='font-source text-[14px] font-normal leading-[150%]'>
                              {t('common.remove')}
                            </span>,
                            disabled: record.account_id === Number(localStorage.getItem(localStorageKeys.accId)),
                            onClick: async () => {
                              if (record.account_id === Number(localStorage.getItem(localStorageKeys.accId))) return;
                              const res = await removeAccount(record.account_id);

                              if (res.error) {
                                console.error('remove account failed', res.error.message);
                                aoiAlert(t('notification.error.removeAccount'), ALERT_TYPES.COMMON_ERROR);
                                return;
                              }

                              refetchAccountList();
                            },
                          }
                        ]
                      }}
                    >
                      <div className='flex h-6 w-6 justify-center items-center'>
                        <img src='/icn/ellipsis_white.svg' alt='ellipsis' className='h-[2.3px] w-[14px]' />
                      </div>
                    </Dropdown>
                  ),
                }
              ]}
              // dataSource={[
              //   {
              //     key: '1',
              //     username: 'username placeholder',
              //     lastActive: 'lastActive placeholder',
              //     role: userRoles.admin,
              //   },
              //   {
              //     key: '2',
              //     username: 'username placeholder',
              //     lastActive: 'lastActive placeholder',
              //     role: userRoles.programmer,
              //   },
              //   {
              //     key: '3',
              //     username: 'username placeholder',
              //     lastActive: 'lastActive placeholder',
              //     role: userRoles.operator,
              //   }
              // ]}
              dataSource={!_.isUndefined(accountList) ? accountList : []}
              pagination={{
                pageSize: pagination.pageSize,
                total: pagination.total,
                current: pagination.current,
                hideOnSinglePage: true,
                showSizeChanger: false,
                showQuickJumper: true,
              }}
              rowHoverable={false}
            />
            <div className='flex gap-4 self-stretch'>
              <div
                className='flex w-[392px] py-2 px-4 gap-2 items-center self-stretch rounded-[4px] cursor-pointer'
                style={{ background: 'linear-gradient(0deg, rgba(87, 242, 196, 0.10) 0%, rgba(87, 242, 196, 0.10) 100%), rgba(255, 255, 255, 0.03)' }}
                onClick={() => {
                  setIsAddMemberModalOpened(true);
                  setSelectedRole(userRoles.programmer);
                }}
              >
                <div className='flex items-center gap-4 flex-1 self-stretch'>
                  <img src='/icn/plus_green.svg' alt='plus' className='w-4 h-4' />
                  <div className='flex flex-col gap-1 flex-1'>
                    <span className='font-source text-[14px] font-normal leading-[150%]'>
                      {t('settings.addProgrammer')}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('settings.teachPCB')}
                    </span>
                  </div>
                  {/* <div className='flex items-center py-1 px-2 rounded-[2px] border-[1px] border-gray-2 gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {`pleaceholder ${t('settings.remaining')}`}
                    </span>
                  </div> */}
                </div>
                <img src='/icn/arrowRight_green.svg' alt='arrow' className='w-[14px] h-[14px]' />
              </div>
              <div
                className='flex w-[392px] py-2 px-4 gap-2 items-center self-stretch rounded-[4px] cursor-pointer'
                style={{ background: 'linear-gradient(0deg, rgba(87, 242, 196, 0.10) 0%, rgba(87, 242, 196, 0.10) 100%), rgba(255, 255, 255, 0.03)' }}
                onClick={() => {
                  setIsAddMemberModalOpened(true);
                  setSelectedRole(userRoles.operator);
                }}
              >
                <div className='flex items-center gap-4 flex-1 self-stretch'>
                  <img src='/icn/plus_green.svg' alt='plus' className='w-4 h-4' />
                  <div className='flex flex-col gap-1 flex-1'>
                    <span className='font-source text-[14px] font-normal leading-[150%]'>
                      {t('settings.addOperator')}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('settings.onlyCanOperate')}
                    </span>
                  </div>
                  {/* <div className='flex items-center py-1 px-2 rounded-[2px] border-[1px] border-gray-2 gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {`pleaceholder ${t('settings.remaining')}`}
                    </span>
                  </div> */}
                </div>
                <img src='/icn/arrowRight_green.svg' alt='arrow' className='w-[14px] h-[14px]' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default Team;