import { createSlice } from '@reduxjs/toolkit';


const initialState = {
  isContainerLvlLoadingEnabled: false,
  containerLvlLoadingMsg: '',
  conveyorAccessToken: '', // used for programing or inspecting
  isProgrammingUsingConveyor: false, // when conveyor setup, PCB dimension, full PCB capture these three steps require conveyor
  currentControlledConveyorSlotId: null,
  ignoreConveyorReminderInProductDefine: false,
  cameraAccessToken: '', // basically we request/release camera access at the same time with conveyor when programming
  isTrainingRunning: false,
  isTransparentLoadingEnabled: false,
  isGlobalRetrainReminderOpened: false,
  globalRetrainInfo: {},
  shouldRunReevaluateAfterRetrain: {}, // { productId: int, shouldRun: bool }
  isAgentParamRegenRunning: false,
  templateEditorShouldRefetchNotifier: 0,
  isAutoProgramTriggeredFromTemplateEditor: false,
  curProgrammingProductName: null,
};

const setting = createSlice({
  name: 'setting',
  initialState,
  reducers: {
    setIsContainerLvlLoadingEnabled: (state, action) => {
      state.isContainerLvlLoadingEnabled = action.payload;
    },
    setContainerLvlLoadingMsg: (state, action) => {
      state.containerLvlLoadingMsg = action.payload;
    },
    setConveyorAccessToken: (state, action) => {
      state.conveyorAccessToken = action.payload;
    },
    setIsProgrammingUsingConveyor: (state, action) => {
      state.isProgrammingUsingConveyor = action.payload;
    },
    setCurrentControlledConveyorSlotId: (state, action) => {
      state.currentControlledConveyorSlotId = action.payload;
    },
    setIgnoreConveyorReminderInProductDefine: (state, action) => {
      state.ignoreConveyorReminderInProductDefine = action.payload;
    },
    setCameraAccessToken: (state, action) => {
      state.cameraAccessToken = action.payload;
    },
    setIsTrainingRunning: (state, action) => {
      state.isTrainingRunning = action.payload;
    },
    setCurTrainingTaskStartTime(state, action) {
      state.curTrainingTaskStartTime = action.payload;
    },
    setTransparentLoadingEnabled(state, action) {
      state.isTransparentLoadingEnabled = action.payload;
    },
    setIsGlobalRetrainReminderOpened(state, action) {
      state.isGlobalRetrainReminderOpened = action.payload;
    },
    setGlobalRetrainInfo(state, action) {
      state.globalRetrainInfo = action.payload;
    },
    setShouldRunReevaluateAfterRetrain(state, action) {
      state.shouldRunReevaluateAfterRetrain = action.payload;
    },
    setIsAgentParamRegenRunning(state, action) {
      state.isAgentParamRegenRunning = action.payload;
    },
    setTemplateEditorShouldRefetchNotifier(state, action) {
      state.templateEditorShouldRefetchNotifier = action.payload;
    },
    setIsAutoProgramTriggeredFromTemplateEditor(state, action) {
      state.isAutoProgramTriggeredFromTemplateEditor = action.payload;
    },
    setCurProgrammingProductName(state, action) {
      state.curProgrammingProductName = action.payload;
    },
  },
});

export const {
  setIsContainerLvlLoadingEnabled,
  setContainerLvlLoadingMsg,
  setConveyorAccessToken,
  setIsProgrammingUsingConveyor,
  setCurrentControlledConveyorSlotId,
  setIgnoreConveyorReminderInProductDefine,
  setCameraAccessToken,
  setIsTrainingRunning,
  setCurTrainingTaskStartTime,
  setTransparentLoadingEnabled,
  setIsGlobalRetrainReminderOpened,
  setGlobalRetrainInfo,
  setShouldRunReevaluateAfterRetrain,
  setIsAgentParamRegenRunning,
  setTemplateEditorShouldRefetchNotifier,
  setIsAutoProgramTriggeredFromTemplateEditor,
  setCurProgrammingProductName,
} = setting.actions;

export default setting.reducer;