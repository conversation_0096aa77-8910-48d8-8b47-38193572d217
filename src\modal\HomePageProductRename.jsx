import React, { useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Input } from 'antd';
import { useUpdateProductMutation } from '../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { COMMON_HTTP_CODE } from '../common/const';


const HomePageProductRename = (props) => {
  const {
    isOpened,
    setIsOpened,
    targetProduct,
    refetchAllProducts,
  } = props;
  
  const { t } = useTranslation();

  const [displayedName, setDisplayedName] = useState(_.get(targetProduct, 'product_name', ''));

  const [udpateProduct] = useUpdateProductMutation();

  useEffect(() => {
    setDisplayedName(_.get(targetProduct, 'product_name', ''));
  }, [targetProduct]);

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[14px] font-semibold leading-[150%]'>
          {t('home.updatePCBAName')}
        </span>
      }
      footer={null}
    >
      <div className='flex flex-col py-4 px-6 gap-4 self-stretch'>
        <div className='flex items-center gap-2 self-stretch'>
          <span className='font-source text-[12px] font-normal leading-[150%] w-[50%]'>
            {t('home.newPCBAName')}
          </span>
          <Input
            value={displayedName}
            onChange={(e) => setDisplayedName(e.target.value)}
            style={{ width: '50%' }}
            
          />
        </div>
        <div className='flex items-center gap-2 self-stretch justify-center'>
          <Button
            onClick={() => {
              setIsOpened(false);
            }}
            style={{  width: '50%' }}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{  width: '50%' }}
            onClick={() => {
              const run = async (product, newName) => {
                if (_.isEmpty(newName)) {
                  aoiAlert(t('notification.error.pleaseEnterAValidValue'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                const res = await udpateProduct({
                  product_id: Number(_.get(product, 'product_id')),
                  product_name: newName,
                });

                if (res.error) {
                  if (res.error.status === COMMON_HTTP_CODE.conflict) {
                    console.error('product name exist');
                    aoiAlert(t('notification.error.nameExists'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
                  console.error('update product failed', res.error.message);
                  return;
                }

                await refetchAllProducts();
                setIsOpened(false);
              };

              run(targetProduct, displayedName);
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('common.save')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default HomePageProductRename;