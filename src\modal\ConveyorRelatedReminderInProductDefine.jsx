import React, { useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox } from 'antd';
import { useDispatch } from 'react-redux';
import { setIgnoreConveyorReminderInProductDefine } from '../reducer/setting';


const ConveyorRelatedReminderInProductDefine = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [checked, setChecked] = useState(false);

  return (
    <CustomModal
      open={isOpened}
      // onCancel={() => setIsOpened(false)}
      closable={false}
      title={<span className='font-source text-[14px] font-semibold leading-[150%]'>
        {t('common.reminder')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch'>
        <div className='flex py-6 px-4 flex-col self-stretch'>
          <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('productDefine.conveyorRelatedReminder')}
          </span>
        </div>
        <div
          className='flex px-4 items-center gap-2 cursor-pointer'
          onClick={() => setChecked(!checked)}
        >
          <Checkbox
            checked={checked}
          />
          <span className='font-source text-[12px] font-normal pt-0.5 leading-[150%]'>
            {t('common.donNotShowAgain')}
          </span>
        </div>
        <div className='flex p-4 gap-2 items-center self-stretch'>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              if (checked) dispatch(setIgnoreConveyorReminderInProductDefine(true));
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.ok')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  )
};

export default ConveyorRelatedReminderInProductDefine;