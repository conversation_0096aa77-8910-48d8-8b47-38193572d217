import React, { useCallback, useEffect, useRef } from 'react';
import { fabric } from 'fabric';
import { highResoluRefreshInterval, serverHost } from '../common/const';
import _ from 'lodash';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../reducer/setting';
import { useTranslation } from 'react-i18next';


const PCBAOIViewer = (props) => {
  const dispatch = useDispatch();

  const { t } = useTranslation();

  const canvasElRef = useRef();
  const fcanvasRef = useRef();
  const displayedHighResolSceneRef = useRef(); // constanly reload when zooming in/out and paning
  const thumbnailBgSceneRef = useRef();
  const resizeHandlerRef = useRef();
  const containerResizeObserverRef = useRef();
  const isLocked = useRef(false);
  const isPanning = useRef(false);
  const mode = useRef('transform'); // transform, select, select3dDisplay

  const {
    imageUri,
    rawImageW,
    rawImageH,
    viewerContainerRef,
  } = props;

  const loadInitFullSizeThumbnail = async (
    uri,
    rawHeight,
    rawWidth,
  ) => {
    if (!fcanvasRef.current) return;

    // fetch and load
    const img = await new Promise((resolve, reject) => {
      // fabric.util.loadImage(`${serverHost}/image?uri=${uri}&bounding_box=0,0,${rawWidth},${rawHeight}`, (img) => {
      //   resolve(img);
      // });
      fabric.util.loadImage(`http://localhost:8900/image?pmin=0,0&pmax=${rawWidth-1},${rawHeight-1}`, (img) => {
        resolve(img);
      });
    });

    thumbnailBgSceneRef.current = new fabric.Image(img, {
      selectable: false,
      evented: false,
    });

    if (rawHeight !== thumbnailBgSceneRef.current.height || rawWidth !== thumbnailBgSceneRef.current.width) {
      // apply scale
      const scaleY = rawHeight / thumbnailBgSceneRef.current.height;
      const scaleX = rawWidth / thumbnailBgSceneRef.current.width;
      thumbnailBgSceneRef.current.scaleX = scaleX;
      thumbnailBgSceneRef.current.scaleY = scaleY;
    }

    fcanvasRef.current.add(thumbnailBgSceneRef.current);

    updateZIndex();
  };

  const loadHighResolScene = useCallback(
    _.debounce(async (uri) => {
      // calc the current view port's bounding box in raw image's coordinate based on current zoom and the view port's position at the thumbnailBgScene
      const zoom = fcanvasRef.current.getZoom();
      const vpLeft = fcanvasRef.current.viewportTransform[4];
      const vpTop = fcanvasRef.current.viewportTransform[5];
      const vpWidth = fcanvasRef.current.getWidth();
      const vpHeight = fcanvasRef.current.getHeight();

      let rawVpLeft = -(vpLeft / zoom);
      let rawVpTop = -(vpTop / zoom);
      let rawVpRight = -((vpLeft - vpWidth) / zoom) - 1;
      let rawVpBottom = -((vpTop - vpHeight) / zoom) - 1;

      // make sure within 0-rawImageW and 0-rawImageH
      rawVpLeft = Math.min(Math.max(0, rawVpLeft), rawImageW - 1);
      rawVpTop = Math.min(Math.max(0, rawVpTop), rawImageH - 1);
      rawVpRight = Math.min(Math.max(0, rawVpRight), rawImageW - 1);
      rawVpBottom = Math.min(Math.max(0, rawVpBottom), rawImageH - 1);
      
      // round to integer
      rawVpLeft = Math.floor(rawVpLeft);
      rawVpTop = Math.floor(rawVpTop);
      rawVpRight = Math.ceil(rawVpRight);
      rawVpBottom = Math.ceil(rawVpBottom);

      // dispose the previous high resol scene
      if (displayedHighResolSceneRef.current) {
        fcanvasRef.current.remove(displayedHighResolSceneRef.current);
        displayedHighResolSceneRef.current = null;
      }

      // if viewport has no intersection with the raw image, return
      if (rawVpLeft >= rawVpRight || rawVpTop >= rawVpBottom) return;

      // if viewport overlaps with the raw image, return
      if (rawVpLeft === 0 && rawVpTop === 0 && rawVpRight === rawImageW - 1 && rawVpBottom === rawImageH -1) return;

      dispatch(setIsContainerLvlLoadingEnabled(true));
      dispatch(setContainerLvlLoadingMsg(t('loader.loadingCroppedImage')));

      // fetch and load
      const img = await new Promise((resolve, reject) => {
        // fabric.util.loadImage(`${serverHost}/image?uri=${uri}&bounding_box=${rawVpLeft},${rawVpTop},${rawVpRight},${rawVpBottom}`, (img) => {
        //   resolve(img);
        // });

        fabric.util.loadImage(`http://localhost:8900/image?pmin=${rawVpLeft},${rawVpTop}&pmax=${rawVpRight},${rawVpBottom}`, (img) => {
          resolve(img);
        });
      });

      displayedHighResolSceneRef.current = new fabric.Image(img, {
        selectable: false,
        evented: false,
        top: rawVpTop,
        left: rawVpLeft,
      });

      fcanvasRef.current.add(displayedHighResolSceneRef.current);

      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));

      updateZIndex();
    }, highResoluRefreshInterval),
  []);

  const updateZIndex = () => {
    if (!fcanvasRef.current) return;

    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.set('zIndex', 20);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.set('zIndex', 21);

    fcanvasRef.current.requestRenderAll();
  };

  const init = async () => {
    if (!fcanvasRef.current || !viewerContainerRef.current) return;

    const fcanvas = fcanvasRef.current;
    fcanvas.setWidth(viewerContainerRef.current.offsetWidth);
    fcanvas.setHeight(viewerContainerRef.current.offsetHeight);

    // attach mouse event for zooming and panning
    fcanvas.on('mouse:down', (opt) => {
      if (isLocked.current) return;

      if (mode.current === 'transform') {
        if (opt.target) {
          isPanning.current = false;
          return;
        }
        
        isPanning.current = true;
        fcanvas.selection = false;
        fcanvas.setCursor('grab');
      }
    });

    fcanvas.on('mouse:move', (opt) => {
      if (isLocked.current) return;

      if (mode.current === 'transform') {
        if (isPanning.current && opt && opt.e) {
          fcanvas.setCursor('grab');
          const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);

          fcanvas.relativePan(delta);
        }
      }
    });

    fcanvas.on('mouse:up', (opt) => {
      if (isLocked.current) return;

      if (mode.current === 'transform') {
        isPanning.current = false;
        fcanvas.setCursor('default');

        // reload high resol scene
        if (_.isString(imageUri)) loadHighResolScene(imageUri);
      }
    });

    fcanvas.on('mouse:wheel', (opt) => {
      if (isLocked.current) return;

      if (mode.current === 'transform') {
        const delta = opt.e.deltaY;
        let zoom = fcanvas.getZoom();
        zoom *= 0.999 ** delta;
        // if (zoom > 20) zoom = 20;
        // if (zoom < 0.01) zoom = 0.01;
        fcanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
        opt.e.preventDefault();
        opt.e.stopPropagation();

        // reload high resol scene
        if (_.isString(imageUri)) loadHighResolScene(imageUri);
      }
    });

    if (_.isString(imageUri) && _.isNumber(rawImageW) && _.isNumber(rawImageH)) {
      await loadInitFullSizeThumbnail(imageUri, rawImageH, rawImageW);
      await loadHighResolScene(imageUri);
      updateZIndex();
    }

    resizeHandlerRef.current = () => {
      if (!fcanvasRef.current || !viewerContainerRef.current) return;
      fcanvas.setWidth(viewerContainerRef.current.offsetWidth);
      fcanvas.setHeight(viewerContainerRef.current.offsetHeight);
    };

    containerResizeObserverRef.current = new ResizeObserver(resizeHandlerRef.current);
    containerResizeObserverRef.current.observe(viewerContainerRef.current);
  };

  useEffect(() => {
    const fcanvas = new fabric.Canvas(canvasElRef.current, {
      antialias: 'off',
      uniformScaling: false,
    });

    fcanvasRef.current = fcanvas;

    init();

    return () => {
      if (containerResizeObserverRef.current) containerResizeObserverRef.current.disconnect();
    };
  }, []);

  return (
    <canvas ref={canvasElRef} />
  );
};

export default PCBAOIViewer;