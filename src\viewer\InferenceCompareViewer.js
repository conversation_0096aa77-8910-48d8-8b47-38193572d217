import _ from 'lodash';
import ThreeDB<PERSON><PERSON>iewer from './ThreeDBaseViewer';
import * as THREE from 'three';
import { disposePointCloud } from './util';


export default class InferenceCompareViewer extends ThreeDBaseViewer {
  constructor(canvasRef, elementHeight, elementWidth, controlOnChange) {
    super(canvasRef, elementHeight, elementWidth);
    this.scene = super.getScene();
    this.trackball = super.getTrackballControls();
    this.camera = super.getCamera();
    this.renderer = super.getRenderer();
    this.sceneCloudId = null;
    this.pcCenter = null;
    this.pcBoundingBoxMaxMin = {};

    this.controlOnChange = controlOnChange;

    this.trackball.addEventListener('change', () => {
      this.controlOnChange(this.camera, this.trackball);
    });
  };

  loadScene = ({
    positions,
    colors,
    isGolden,
  }) => {
    // start timer
    // console.log(Date.now().valueOf(), 'start load scene');
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    // geometry.setAttribute('color', new THREE.Uint8BufferAttribute(colors, 3));
    // geometry.attributes.color.normalized = true;

    const cusColors = new Uint8Array(positions.length); // Same length as positions
    for (let i = 0; i < positions.length / 3; i++) {
      if (isGolden) {
        cusColors[i * 3] = 187;     // Red channel
        cusColors[i * 3 + 1] = 107; // Green channel
        cusColors[i * 3 + 2] = 217; // Blue channel
      } else {
        cusColors[i * 3] = 242;     // Red channel
        cusColors[i * 3 + 1] = 201; // Green channel
        cusColors[i * 3 + 2] = 76;  // Blue
      }
      // rgb(187, 107, 217) purple
      // rgb(242, 201, 76) yellow
    }

    geometry.setAttribute('color', new THREE.Uint8BufferAttribute(cusColors, 3));
    geometry.attributes.color.normalized = true;

    // get cloud position center
    const center = new THREE.Vector3();
    geometry.computeBoundingBox();
    geometry.boundingBox.getCenter(center);
    this.pcCenter = center;

    this.pcBoundingBoxMaxMin = {
      max: geometry.boundingBox.max,
      min: geometry.boundingBox.min,
    };

    const material = new THREE.ShaderMaterial({
      uniforms: {
        size: { value: 0.3 },
      },
      vertexShader: `
        uniform float size;
        varying vec3 vColor;
        
        void main() {
            vColor = color.rgb;
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * (250.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
  
        void main() {
          gl_FragColor = vec4(vColor, 1.0);
        }
      `,
      transparent: true,
      vertexColors: true,
    });

    // const material = new THREE.PointsMaterial({ vertexColors: true });

    const cloud = new THREE.Points(geometry, material);
    this.scene.add(cloud);
    this.sceneCloudId = cloud.id;

    this.getCamera().position.set(this.pcCenter.x, this.pcCenter.y, this.pcBoundingBoxMaxMin.min.z - 20,);
    this.trackball.target.set(center.x, center.y, center.z);
    this.getCamera().lookAt(center.x, center.y, center.z);
    // console.log(Date.now().valueOf(), 'end load scene');
  };

  updateCameraPose = (camera, trackball) => {
    this.camera.position.copy(camera.position);
    this.camera.quaternion.copy(camera.quaternion);
    this.camera.up.copy(camera.up);
    this.camera.fov = camera.fov;
    this.camera.aspect = camera.aspect;
    this.camera.near = camera.near;
    this.camera.far = camera.far;

    this.trackball.object.position.copy(trackball.object.position);
    this.trackball.target.copy(trackball.target);

    this.camera.updateProjectionMatrix();
    this.trackball.update();
  };

  updateCloudVisibility = (visible) => {
    if (_.isInteger(this.sceneCloudId)) {
      this.scene.getObjectById(this.sceneCloudId).visible = visible;
    }
  };

  updateCameraInZPlane = (desiredObjView) => {
    if (_.isEmpty(this.pcBoundingBoxMaxMin) || _.isEmpty(this.pcCenter)) return;
    // desiredObjView: front, back, left, right, top, bottom
    // camera's position z should be the same as the cloud's center z
    // and should be facing the cloud's center
    // place camera at the front, back, left, right of the pc bounding box
    // also ensure the pc is facing up in the camera view
    const camera = this.getCamera();
    // console.log(this.pcBoundingBoxMaxMin.max.y + 200, this.pcBoundingBoxMaxMin.min.y - 200);
    switch (desiredObjView) {
      case 'front':
        camera.position.set(
          this.pcCenter.x,
          this.pcBoundingBoxMaxMin.max.y + 20,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'back':
        camera.position.set(
          this.pcCenter.x,
          this.pcBoundingBoxMaxMin.min.y - 20,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'left':
        camera.position.set(
          this.pcBoundingBoxMaxMin.min.x - 20,
          this.pcCenter.y,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'right':
        camera.position.set(
          this.pcBoundingBoxMaxMin.max.x + 20,
          this.pcCenter.y,
          this.pcCenter.z,
        );
        camera.up.set(0, 0, -1);
        break;
      case 'top':
        camera.position.set(
          this.pcCenter.x,
          this.pcCenter.y,
          this.pcBoundingBoxMaxMin.min.z - 20,
        );
        camera.up.set(0, -1, 0);
        break;
      default:
        return;
    }
    camera.lookAt(this.pcCenter.x, this.pcCenter.y, this.pcCenter.z);
    this.trackball.target.set(this.pcCenter.x, this.pcCenter.y, this.pcCenter.z);
    this.trackball.update();
    this.renderer.render(this.scene, camera);
  };

  clearScene = () => {
    if (_.isInteger(this.sceneCloudId)) {
      disposePointCloud(this.scene, this.sceneCloudId);
      this.sceneCloudId = null;
    }
  };
};