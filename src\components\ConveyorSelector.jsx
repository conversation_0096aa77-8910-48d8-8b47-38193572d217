import { Button, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getCurrentConveyorStatus } from '../common/util';
import { useLazyGetAllConveyorStatusQuery } from '../services/conveyor';
import { useLazyGetInferenceStatusQuery } from '../services/inference';
import { conveyorNum } from '../common/const';
import _ from 'lodash';


const ConveyorSelector = (props) => {
  const {
    selectedConveyorSlotId,
    setSelectedConveyorSlotId,
    disabled,
  } = props;

  const { t } = useTranslation();

  const [conveyorSelection, setConveyorSelection] = useState([]);
  const [conveyorStatus, setConveyorStatus] = useState(null);

  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();

  useEffect(() => {
    const options = [];
    for (let i = 0; i < conveyorNum; i++) {
      options.push({
        value: `${i}`,
        label: (
          <div className='flex w-[181.5px] px-1 items-center gap-2'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {String.fromCharCode(65 + i)}
            </span>
            {_.get(conveyorStatus, `${i}.taskType`, 'none') === 'none' ? (
              <div className='flex items-center gap-1.5 p-1.5 flex-1'>
                <div className='w-[10px] h-[10px] rounded-full bg-[#81F499]' />
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t('productDefine.available')}
                </span>
              </div>
            ) : (
              <div className='flex items-center gap-1.5 p-1.5 flex-1'>
                <div className='w-[10px] h-[10px] rounded-full bg-[#EB5757]' />
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t('productDefine.inUse')}
                </span>
                -
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t(`home.${_.get(conveyorStatus, `${i}.taskType`, 'none')}`)}
                </span>
              </div>
            )}
          </div>
        ),
        disabled: _.get(conveyorStatus, `${i}.taskType`, 'none') !== 'none',
      });
    }
    setConveyorSelection(options);
  }, [conveyorStatus]);

  useEffect(() => {
    const run = async () => {
      let conveyorStatus;
      try {
        conveyorStatus = await getCurrentConveyorStatus(lazyGetConveyorStatus, lazyGetInferenceStatus, t);
      } catch (e) {
        console.error(e);
        return;
      }
      
      setConveyorStatus(conveyorStatus);
    };
    run();
  }, []);

  return (
    <div className='flex items-center flex-1 justify-between'>
      <Select
        disabled={disabled}
        style={{ width: '174px' }}
        options={conveyorSelection}
        value={selectedConveyorSlotId}
        onChange={(v) => setSelectedConveyorSlotId(v)}
        popupMatchSelectWidth={false}
      />
      <Button
        size='small'
        type='text'
        onClick={() => {
          const run = async () => {
            let conveyorStatus;
            try {
              conveyorStatus = await getCurrentConveyorStatus(lazyGetConveyorStatus, lazyGetInferenceStatus, t);
            } catch (e) {
              console.error(e);
              return;
            }
            
            setConveyorStatus(conveyorStatus);
          };

          run();
        }}
      >
        <div className='flex items-center gap-1'>
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('newInspectionTask.refreshStatus')}
          </span>
        </div>
      </Button>
    </div>
  );
};

export default ConveyorSelector;