import React, { useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button } from 'antd';
import { useInFieldCalibrationMutation } from '../services/camera';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { isAOI3DSMT } from '../common/const';
import _ from 'lodash';


const CalibrationProcess = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;
  
  const { t } = useTranslation();

  const [inFieldCalibration] = useInFieldCalibrationMutation();
  const [isShowingAccuracy, setIsShowingAccuracy] = useState(false);
  const [accuracyResult, setAccuracyResult] = useState(null);

  return (
    <CustomModal
      width={656}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('settings.cameraCalibration')}
      </span>}
      footer={null}
    >
      <div className='flex h-[473px] flex-col justify-center items-center gap-6 flex-[1_0_0] [background:#252525] px-4 py-6'>
        {!isShowingAccuracy ?
          <div className='flex flex-col justify-center items-center gap-4 self-stretch p-4'>
            <div className='flex flex-col items-center gap-1 self-stretch'>
              <span className='text-white font-source text-lg font-normal leading-[150%] tracking-[0.54px]'>
                {t('settings.placeCalibrationPlateOnTheTrack')}
              </span>
              {/* <span className='self-stretch text-gray-5 text-center font-source text-sm font-normal leading-[150%]'>

              </span> */}
            </div>
            {/* <div className='flex flex-col items-center gap-1 self-stretch'>
              <img
                src=''
                alt='calibration_guide'
                className='w-[280px] h-[200px]'
              />
            </div> */}
            <Button
              type='primary'
              onClick={() => {
                const run = async () => {
                  const res = await inFieldCalibration();

                  if (res.error) {
                    aoiAlert(t('notification.error.cameraInfieldCalibrationFailed'), ALERT_TYPES.COMMON_ERROR);
                    console.error('inFieldCalibration error:', _.get(res, 'error.message', ''));
                    setIsOpened(false);
                    return;
                  }

                  if (isAOI3DSMT) {
                    // only display accuracy in 3d
                    setAccuracyResult(_.get(res, 'data.accuracy', null));
                    setIsShowingAccuracy(true);
                    return;
                  }

                  aoiAlert(t('notification.success.cameraInfieldCalibrationFinished'), ALERT_TYPES.COMMON_INFO);
                  setIsOpened(false);
                };

                run();
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('settings.calibrate')}
              </span>
            </Button>
          </div>
        :
          <div className='flex flex-col justify-center items-center gap-6 flex-[1_0_0] self-stretch [background:#252525] px-4 py-6'>
            <div className='flex justify-center items-center self-stretch px-0 py-0.5'>
              <span className='text-white font-source text-sm font-normal leading-[150%]'>
                {t('settings.accuracyAnalysisResult')}
              </span>
            </div>
            <div className='flex items-start gap-4 self-stretch'>
              <div className='flex flex-col items-start gap-1 self-stretch'>
                <div className='flex items-center gap-6 self-stretch'>
                  <div className='flex items-start gap-0.5 flex-[1_0_0]'>
                    <div className='flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {t('settings.planarityError')}
                      </span>
                    </div>
                    <div className='flex flex-col justify-center items-center flex-[1_0_0] [background:var(--default-Gray-1,#333)] px-0 py-1 rounded-sm'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {_.get(accuracyResult, 'planarity_error_mean', 0)} +- {_.get(accuracyResult, 'planarity_error_std', 0)} um
                      </span>
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-6 self-stretch'>
                  <div className='flex items-start gap-0.5 flex-[1_0_0]'>
                    <div className='flex flex-col justify-center items-start flex-[1_0_0] px-0 py-1 rounded-[2px_2px_0_0]'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {t('settings.projectorDiscrepancy')}
                      </span>
                    </div>
                    <div className='flex flex-col justify-center items-center flex-[1_0_0] [background:var(--default-Gray-1,#333)] px-0 py-1 rounded-sm'>
                      <span className='text-white font-source text-xs font-normal leading-[150%]'>
                        {_.get(accuracyResult, 'projector_discrepancy_mean', 0)} +- {_.get(accuracyResult, 'projector_discrepancy_std', 0)} um
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='flex justify-center items-center gap-4 self-stretch px-0 py-4'>
              <Button
                onClick={() => {
                  setIsShowingAccuracy(false);
                  setAccuracyResult(null);
                  setIsOpened(false);
                }}
              >
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.close')}
                </span>
              </Button>
            </div>
          </div>
        }
      </div>
    </CustomModal>
  );
};

export default CalibrationProcess;