<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_3522_4076)">
<rect x="0" y="0" width="24" height="24" rx="2" fill="url(#paint0_linear_3522_4076)"/>
<rect x="0" y="0" width="24" height="24" rx="2" stroke="#BDBDBD" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_3522_4076" x="-1" y="0" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3522_4076"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3522_4076" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3522_4076" x1="4" y1="17" x2="28" y2="17" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF0000"/>
<stop offset="0.137159" stop-color="#FF6B00"/>
<stop offset="0.26287" stop-color="#FAFF00"/>
<stop offset="0.385316" stop-color="#00FF29"/>
<stop offset="0.502864" stop-color="#00FFF0"/>
<stop offset="0.63184" stop-color="#0047FF"/>
<stop offset="0.767347" stop-color="#8F00FF"/>
<stop offset="0.879998" stop-color="#FA00FF"/>
<stop offset="1" stop-color="#FF0000"/>
</linearGradient>
</defs>
</svg>
