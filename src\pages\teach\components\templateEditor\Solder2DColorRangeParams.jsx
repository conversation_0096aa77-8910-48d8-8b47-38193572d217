import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>ton, Slider, Select, Tooltip } from 'antd';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import HSVColorRangePicker from '../../../../modal/HSVColorRangePicker';
import { useUpdateAgentParamsMutation } from '../../../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import {
  solder2DCenterHue,
  solder2DCenterSat,
  solder2DStartHue,
  solder2DStartVal,
  solder2DEndHue,
  solder2DEndVal,
  solder2DValidRatioRanges,
} from '../../../../common/const';
import './CustomSlider.css';

const Solder2DColorRangeParams = (props) => {
  const {
    selectedGroupFeatureTypeAgentParams,
    lintItemName,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
    updateAllFeaturesState,
    setSelectedGroupFeatureTypeAgentParams,
  } = props;

  const { t } = useTranslation();
  const [updateFeatureParams] = useUpdateAgentParamsMutation();

  const [rows, setRows] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const metaRef = useRef({
    startHue: { min: 0, max: 360, active: true },
    startVal: { min: 0, max: 100, active: true },
    endHue: { min: 0, max: 360, active: true },
    endVal: { min: 0, max: 100, active: true },
    centerHue: { min: 0, max: 360, active: true },
    centerSat: { min: 0, max: 100, active: true },
    range: { active: true },
  });

  useEffect(() => {
    const getIntVector = (name) => {
      const vec =
        _.get(
          selectedGroupFeatureTypeAgentParams,
          `line_item_params.${lintItemName}.params.${name}.param_vector`,
          []
        ) || [];
      return {
        values: vec.map((v) => _.get(v, 'param_int.value', 0)),
        min: _.get(vec[0], 'param_int.min', 0),
        max: _.get(vec[0], 'param_int.max', 0),
        active: _.get(vec[0], 'active', true),
      };
    };

    const getRangeVector = () => {
      const vec =
        _.get(
          selectedGroupFeatureTypeAgentParams,
          `line_item_params.${lintItemName}.params.${solder2DValidRatioRanges}.param_vector`,
          []
        ) || [];
      return {
        values: vec.map((v) => [_.get(v, 'param_range.ok_min', 0), _.get(v, 'param_range.ok_max', 100)]),
        active: _.get(vec[0], 'active', true),
      };
    };

    const sh = getIntVector(solder2DStartHue);
    const sv = getIntVector(solder2DStartVal);
    const eh = getIntVector(solder2DEndHue);
    const ev = getIntVector(solder2DEndVal);
    const ch = getIntVector(solder2DCenterHue);
    const cs = getIntVector(solder2DCenterSat);
    const ranges = getRangeVector();

    metaRef.current.startHue = { min: sh.min, max: sh.max, active: sh.active };
    metaRef.current.startVal = { min: sv.min, max: sv.max, active: sv.active };
    metaRef.current.endHue = { min: eh.min, max: eh.max, active: eh.active };
    metaRef.current.endVal = { min: ev.min, max: ev.max, active: ev.active };
    metaRef.current.centerHue = { min: ch.min, max: ch.max, active: ch.active };
    metaRef.current.centerSat = { min: cs.min, max: cs.max, active: cs.active };
    metaRef.current.range = { active: ranges.active };
    const maxLen = Math.max(
      sh.values.length,
      sv.values.length,
      eh.values.length,
      ev.values.length,
      ch.values.length,
      cs.values.length,
      ranges.values.length
    );

    const defaultColor = {
      sh: sh.values[0] ?? 0,
      sv: sv.values[0] ?? 0,
      eh: eh.values[0] ?? 0,
      ev: ev.values[0] ?? 100,
      ch: ch.values[0] ?? 0,
      cs: cs.values[0] ?? 0,
    };
    const defaultRange = ranges.values[0] || [0, 100];

    const tmp = [];
    for (let i = 0; i < maxLen; i++) {
      tmp.push({
        color: {
          sh: sh.values[i] ?? defaultColor.sh,
          sv: sv.values[i] ?? defaultColor.sv,
          eh: eh.values[i] ?? defaultColor.eh,
          ev: ev.values[i] ?? defaultColor.ev,
          ch: ch.values[i] ?? defaultColor.ch,
          cs: cs.values[i] ?? defaultColor.cs,
        },
        range: ranges.values[i] ? _.cloneDeep(ranges.values[i]) : _.cloneDeep(defaultRange),
      });
    }

    if (tmp.length === 0) tmp.push({ color: defaultColor, range: defaultRange });
    setRows(tmp);
    if (selectedIndex >= tmp.length) {
      setSelectedIndex(0);
    }
  }, [selectedGroupFeatureTypeAgentParams, lintItemName]);

  const handleSubmit = async (newRows) => {
    const startHueVec = newRows.map((r) => ({
      param_int: {
        value: r.color.sh,
        max: metaRef.current.startHue.max,
        min: metaRef.current.startHue.min,
      },
      active: metaRef.current.startHue.active,
    }));
    const startValVec = newRows.map((r) => ({
      param_int: {
        value: r.color.sv,
        max: metaRef.current.startVal.max,
        min: metaRef.current.startVal.min,
      },
      active: metaRef.current.startVal.active,
    }));
    const endHueVec = newRows.map((r) => ({
      param_int: {
        value: r.color.eh,
        max: metaRef.current.endHue.max,
        min: metaRef.current.endHue.min,
      },
      active: metaRef.current.endHue.active,
    }));
    const endValVec = newRows.map((r) => ({
      param_int: {
        value: r.color.ev,
        max: metaRef.current.endVal.max,
        min: metaRef.current.endVal.min,
      },
      active: metaRef.current.endVal.active,
    }));
    const centerHueVec = newRows.map((r) => ({
      param_int: {
        value: r.color.ch,
        max: metaRef.current.centerHue.max,
        min: metaRef.current.centerHue.min,
      },
      active: metaRef.current.centerHue.active,
    }));
    const centerSatVec = newRows.map((r) => ({
      param_int: {
        value: r.color.cs,
        max: metaRef.current.centerSat.max,
        min: metaRef.current.centerSat.min,
      },
      active: metaRef.current.centerSat.active,
    }));
    const rangeVectors = newRows.map((r) => ({
      param_range: { ok_min: r.range[0], ok_max: r.range[1] },
      active: metaRef.current.range.active,
    }));

    const payload = {
      product_id: goldenProductId,
      step: 0,
      feature_type: selectedFeatureType,
      line_item_params: {
        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
        [lintItemName]: {
          ..._.get(
            selectedGroupFeatureTypeAgentParams,
            `line_item_params.${lintItemName}`
          ),
          params: {
            ..._.get(
              selectedGroupFeatureTypeAgentParams,
              `line_item_params.${lintItemName}.params`
            ),
            [solder2DStartHue]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DStartHue}`
              ),
              param_vector: startHueVec,
            },
            [solder2DStartVal]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DStartVal}`
              ),
              param_vector: startValVec,
            },
            [solder2DEndHue]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DEndHue}`
              ),
              param_vector: endHueVec,
            },
            [solder2DEndVal]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DEndVal}`
              ),
              param_vector: endValVec,
            },
            [solder2DCenterHue]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DCenterHue}`
              ),
              param_vector: centerHueVec,
            },
            [solder2DCenterSat]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DCenterSat}`
              ),
              param_vector: centerSatVec,
            },
            [solder2DValidRatioRanges]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${solder2DValidRatioRanges}`
              ),
              param_vector: rangeVectors,
            },
          },
        },
      },
    };

    if (_.isInteger(selectedCid) && selectedScope === 'component') {
      payload.component_id = selectedCid;
    } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
      payload.part_no = selectedPartNo;
    } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
      payload.package_no = selectedPackageNo;
    }

    const res = await updateFeatureParams(payload);

    if (res.error) {
      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error('update feature failed', res.error.message);
      return;
    }

    if (!_.isEmpty(res.data)) {
      await updateAllFeaturesState(
        _.get(res, 'data.feature_ids', []),
        'updateGroupParam',
        _.get(res, 'data.line_item_params', {})
      );
    }
  };

  const handleAdd = () => {
    const defaultColor = rows[0]?.color || { sh: 0, sv: 0, eh: 0, ev: 100, ch: 0, cs: 0 };
    const defaultRange = rows[0]?.range || [0, 100];
    const newRows = [...rows, { color: _.cloneDeep(defaultColor), range: _.cloneDeep(defaultRange) }];
    setRows(newRows);
    setSelectedIndex(newRows.length - 1); // 自动选择新添加的项
    handleSubmit(newRows);
  };

  const handleDelete = () => {
    if (rows.length <= 1) return; // 至少保留一个
    const newRows = rows.filter((_, idx) => idx !== selectedIndex);
    setRows(newRows);
    // 调整选中的索引
    if (selectedIndex >= newRows.length) {
      setSelectedIndex(newRows.length - 1);
    }
    handleSubmit(newRows);
  };

  const handleColorSubmit = (index) => (sh, ss, _sv, eh, es, _ev, ch, cs, _cv) => {
    // console.log('submit at index:', selectedIndex);
    const newRows = rows.map((r, i) =>
      i === index ? { ...r, color: { ...r.color, sh, eh, ch, cs } } : r
    );
    setRows(newRows);
    handleSubmit(newRows);
  };

  const selectOptions = rows.map((_, idx) => ({
    value: idx,
    label: `${t('agentParamName.lead_inspection_2d_v2.solderColorRange')} ${idx + 1}`,
  }));

  return (
    <div className='flex flex-col gap-4'>
      {/* 控制按钮区域 */}
      <div className='flex gap-2'>


      </div>

      {/* 选择器 */}
      {rows.length > 0 && (
        <div className='flex flex-col gap-2'>
          <div className="flex flex-row items-center gap-2">
            <Select
              style={{ width: '100%' }}
              value={selectedIndex}
              onChange={setSelectedIndex}
              options={selectOptions}
            />

            <Button style={{ flex: 1 }} onClick={handleAdd}>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                + {t('common.add')}
              </span>
            </Button>

            {rows.length > 1 && (
              <Button style={{ flex: 1 }} onClick={handleDelete} danger>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  - {t('common.delete')}
                </span>
              </Button>
            )}
          </div>

          <div className='flex flex-col gap-2'>
            <div className='flex flex-row items-center gap-2'>
              {/* Range Slider */}


                <div className="flex flex-row items-center gap-1 my-auto">
                  <span className="font-source text-[12px] font-normal whitespace-nowrap text-ellipsis overflow-hidden"
                    alt='solder123'
                    title={t('agentParamName.lead_inspection_2d_v2.solder123')}
                    style={{ maxWidth: '150px' }}
                  >
                    {t('agentParamName.lead_inspection_2d_v2.solder123')}
                  </span>

                  <Tooltip
                    title={t('agentParamName.lead_inspection_2d_v2.solder123Desc')}
                  >
                    <img
                      src={'/icn/info_white.svg'}
                      alt='info'
                      className='w-3 h-3 cursor-pointer'
                    />
                  </Tooltip>
                 </div>
                <Slider
                  className='custom-slider w-full'
                  range
                  step={0.001}
                  min={0}
                  max={100}
                  styles={{ track: { background: '#81F499' }, rail: { background: '#F46D6D' } }}
                  value={rows[selectedIndex]?.range || [0, 100]}
                  onChange={(value) => {
                    if (value[0] >= value[1]) return;
                    const newRows = rows.map((r, i) => (i === selectedIndex ? { ...r, range: value } : r));
                    setRows(newRows);
                  }}
                  onAfterChange={(value) => {
                    if (value[0] >= value[1]) return;
                    const newRows = rows.map((r, i) => (i === selectedIndex ? { ...r, range: value } : r));
                    handleSubmit(newRows);
                  }}
                />

            </div>
          </div>



          <div className='flex flex-col gap-2'>
            <HSVColorRangePicker
              key={selectedIndex}
              isOpened={true}
              setIsOpened={() => {}}
              sh={rows[selectedIndex]?.color.sh || 0}
              ss={100}
              sv={100}
              eh={rows[selectedIndex]?.color.eh || 0}
              es={100}
              ev={100}
              ch={rows[selectedIndex]?.color.ch || 0}
              cs={rows[selectedIndex]?.color.cs || 0}
              cv={100}
              noValueSlider={true}
              submit={(sh, ss, _sv, eh, es, _ev, ch, cs, _cv) => {
                const index = selectedIndex;
                const newRows = rows.map((r, i) =>
                  i === index
                    ? { ...r, color: { ...r.color, sh, eh, ch, cs } }
                    : r
                );
                setRows(newRows);
                handleSubmit(newRows);
              }}
            />
            <Slider
              className='custom-slider w-full'
              range
              step={1}
              min={0}
              max={100}
              styles={{
                track: { background: 'transparent' },
                rail: { background: 'linear-gradient(to right, black, white)' },
              }}
              value={[
                rows[selectedIndex]?.color.sv ?? 0,
                rows[selectedIndex]?.color.ev ?? 100,
              ]}
              onChange={(value) => {
                if (value[0] >= value[1]) return;
                const newRows = rows.map((r, i) =>
                  i === selectedIndex
                    ? { ...r, color: { ...r.color, sv: value[0], ev: value[1] } }
                    : r
                );
                setRows(newRows);
              }}
              onAfterChange={(value) => {
                if (value[0] >= value[1]) return;
                const newRows = rows.map((r, i) =>
                  i === selectedIndex
                    ? { ...r, color: { ...r.color, sv: value[0], ev: value[1] } }
                    : r
                );
                handleSubmit(newRows);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Solder2DColorRangeParams;
