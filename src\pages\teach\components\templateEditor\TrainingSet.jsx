import { But<PERSON>, Select, Tabs, Tooltip } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _, { set } from 'lodash';
import { useAnnotateGroupMutation, useGetDataSetExampleQuery, useGetInspectedComponentQuery, useLazyGetDataSetExampleQuery, useLazyGetInspectedComponentQuery, useMarkTrainingExampleMutation } from '../../../../services/inference';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { componentRoiPadding, filletLowerThreshold, filletOpenThreshold, filletUpperThreshold, filletVolumeRatio, highResoluCroppedDisplayMegaPixelCount, serverHost, trainingSetCropImgPadding, mountingInspection3D, leadInspection3D, solderInspection3D, isAOI2DSMT, leadFeatureType, leadGapFeatureType, leadInspection2D } from '../../../../common/const';
import { SmallTabsConfigProvider } from '../../../../common/styledComponent';
import { systemApi } from '../../../../services/system';
import { useSelector } from 'react-redux';
import { getAgentParamTypeNValueByParam, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import InferenceResult from '../../../../components/InferenceResult';
import FilterOutlined from '@ant-design/icons/FilterOutlined';


const TrainingSet = (props) => {
  const {
    selectedComponent,
    selectedFeature,
    reevaluateTriggered,
    setReevaluateTriggered,
    trainingSetSelectedDetail,
    setTrainingSetSelectedDetail,
    trainingSetSelectedErrorType,
    setTrainingSetSelectedErrorType,
    selectedFeatureType,
    setSelectedFid,
    selectedCid,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    
  } = props;

  const tableContainerRef = useRef(null);

  const { t } = useTranslation();

  const [displayedItems, setDisplayedItems] = useState([]);
  const [passedOnly, setPassedOnly] = useState(null);
  const [hasFeedbackOnly, setHasFeedbackOnly] = useState(null);
  const [tableHeight, setTableHeight] = useState(0);
  const [selectedLineItemResultKey, setSelectedLineItemResultKey] = useState(null);
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [trainingExample, setTrainingExample] = useState(null);
  const [selectedLineItemResult, setSelectedLineItemResult] = useState(null);
  const [selectedLineItemResultParsedError, setSelectedLineItemResultParsedError] = useState(null);
  const [selectedErrorCategory, setSelectedErrorCategory] = useState(null);
  const [feedbackErrorType, setFeedbackErrorType] = useState(null);
  const [isResizing, setIsResizing] = useState(false);
  const [minTableHeight] = useState(200);
  const [maxTableHeight] = useState(600);

  const [lazyGetDataExample] = useLazyGetDataSetExampleQuery();
  const [markTrainingExample] = useMarkTrainingExampleMutation();
  const [lazyGetInspectedComponent] = useLazyGetInspectedComponentQuery();

  const handleMouseDown = (e) => {
    setIsResizing(true);
    e.preventDefault();
  };

  const handleMouseMove = React.useCallback((e) => {
    if (!isResizing || !tableContainerRef.current) return;

    const containerRect = tableContainerRef.current.getBoundingClientRect();
    const newHeight = e.clientY - containerRect.top;

    if (newHeight >= minTableHeight && newHeight <= maxTableHeight) {
      setTableHeight(newHeight);
    }
  }, [isResizing, minTableHeight, maxTableHeight]);

  const handleMouseUp = React.useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleRefetch = async (
    goldenProductId,
    passedOnly,
    selectedFeatureType,
    selectedCid,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
  ) => {
    const query = generateCurrentQuery(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      componentListGroupMode,
      selectedPartNo,
      selectedPackageNo,
    );

    await updateDisplayedItems(query);
  };

  const generateCurrentQuery = (
    goldenProductId,
    passedOnly,
    selectedFeatureType,
    selectedCid,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
  ) => {
    let payload = {
      component_id: selectedCid,
      golden_product_id: Number(goldenProductId),
      has_feedback: true,
      step: 0,
    };
    if (componentListGroupMode === 'component') payload.feature_type = selectedFeatureType;
    if (componentListGroupMode === 'part') payload.part_no = selectedPartNo;
    if (componentListGroupMode === 'package') payload.package_no = selectedPackageNo;

    if (_.isBoolean(passedOnly)) payload.pass = passedOnly;
    // if (_.isBoolean(hasFeedbackOnly)) payload.annotated = hasFeedbackOnly;

    return payload;
  };

  const updateDisplayedItems = async (query) => {
    const res = await lazyGetDataExample(query);

    if (res.error) {
      aoiAlert(t('notification.error.getDataExample'), ALERT_TYPES.COMMON_ERROR);
      console.error('getDataExample error:', _.get(res, 'error.message', ''));
      return;
    }

    // flatten the data
    const result = [];
    let trainingExample;
    for (const key of _.keys(res.data.line_item_results)) {
      result.push({
        agentResults: res.data.line_item_results[key],
        line_item_result_key: key,
      });

      if (_.get(res.data.line_item_results[key], '[0].training_example', false)) trainingExample = res.data.line_item_results[key];
    }

    if (_.isEmpty(trainingExample)) return;

    setTrainingExample(trainingExample);
    setDisplayedItems(result);

    setSelectedLineItemResultKey(_.get(result, '[0].line_item_result_key', null));
    setSelectedDetail(_.get(result, '[0].agentResults[0].detail', null));
    setSelectedLineItemResult(_.get(result, '[0]', null));
    setSelectedFid(_.get(result, '[0].agentResults[0].feature_id', null));
  };

  useEffect(() => {
    const query = generateCurrentQuery(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      componentListGroupMode,
      selectedPartNo,
      selectedPackageNo,
    );

    updateDisplayedItems(query);
  }, [
    passedOnly,
    hasFeedbackOnly,
    selectedComponent,
    selectedFeatureType,
    selectedCid,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
  ]);

  useEffect(() => {
    if (!reevaluateTriggered) return;

    const refetch = async (
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      componentListGroupMode,
      selectedPartNo,
      selectedPackageNo,
    ) => {
      const query = generateCurrentQuery(
        goldenProductId,
        passedOnly,
        selectedFeatureType,
        selectedCid,
        componentListGroupMode,
        selectedPartNo,
        selectedPackageNo,
      );

      await updateDisplayedItems(query);

      setReevaluateTriggered(false);
    };

    refetch(
      goldenProductId,
      passedOnly,
      selectedFeatureType,
      selectedCid,
      componentListGroupMode,
      selectedPartNo,
      selectedPackageNo,
    );
  }, [reevaluateTriggered]);

  useEffect(() => {
    // console.log('selectedLineItemResult', selectedLineItemResult);

    if (_.isEmpty(selectedLineItemResult)) return;

    const ipcProductId = Number(_.split(_.get(selectedLineItemResult, 'line_item_result_key'), '_')[0]);
    const rcid = Number(_.get(selectedLineItemResult, 'agentResults[0].component_id', -1));

    const run = async (ipcProductId, rcid) => {
      const res = await lazyGetInspectedComponent({
        inspected_product_id: ipcProductId,
        step: 0,
      });

      if (res.error) {
        aoiAlert(t('notification.error.getInspectedComponent'), ALERT_TYPES.COMMON_ERROR);
        console.error('getInspectedComponent error:', _.get(res, 'error.message', ''));
        return;
      }

      const componentInferenceResult = _.find(res.data, i => i.result_component_id === rcid);
      setFeedbackErrorType(_.get(componentInferenceResult, 'feedback_error_type', null));
    };

    run(ipcProductId, rcid);
  }, [
    selectedLineItemResult,
    selectedFeatureType,
  ]);

  useEffect(() => {
    if (tableContainerRef.current && tableHeight === 0) {
      // 设置初始高度为300px，用户可以调整
      setTableHeight(300);
    }
  }, [tableHeight]);

  return (
    <div className='flex flex-col flex-1 self-stretch'>
      <div className='flex flex-col items-start gap-2 self-stretch bg-[#ffffff0d]'>
        <div className='flex p-4 flex-col justify-center items-start gap-2 self-stretch'>
          <div className='flex items-center justify-between self-stretch'>
            <div className='flex items-center gap-1 min-w-0 flex-1 mr-4'>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'>
                {_.get(selectedComponent, 'designator', '')}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                /
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] truncate'>
                {selectedFeatureType}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                /
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px] flex-shrink-0'>
                {t('productDefine.trainingSet')}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {t('productDefine.allItems')}:
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
                {displayedItems.length}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <FilterOutlined />
            <Select
              popupMatchSelectWidth={false}
              style={{ width: '80%' }}
              size='small'
              value={passedOnly}
              onChange={(value) => {
                setPassedOnly(value);
              }}
              options={[
                {
                  value: true,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.passedOnly')}
                  </span>
                },
                {
                  value: false,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.failedOnly')}
                  </span>
                },
                {
                  value: null,
                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.showAll')}
                  </span>
                }
              ]}
            />
          </div>
        </div>
      </div>
      <div className='flex flex-col self-stretch'>
        <div
          ref={tableContainerRef}
          className='flex self-stretch'
          style={{ height: `${tableHeight}px` }}
        >
          <div
            className='flex items-center flex-wrap gap-2 self-stretch overflow-auto px-2'
            style={{
              height: tableHeight,
            }}
          >
            {tableHeight > 0 && _.map(displayedItems, (item, index) => (
              <LineItemRecordCard
                key={index}
                selected={_.get(item, 'line_item_result_key') === selectedLineItemResultKey}
                selectedLineItemResult={item}
                setSelectedLineItemResultKey={setSelectedLineItemResultKey}
                setSelectedDetail={setTrainingSetSelectedDetail}
                trainingExample={trainingExample}
                setSelectedLineItemResult={setSelectedLineItemResult}
                setSelectedLineItemResultParsedError={setSelectedLineItemResultParsedError}
                setSelectedFid={setSelectedFid}
              />
            ))}
          </div>
        </div>
        <div
          className={`flex h-[4px] self-stretch cursor-row-resize bg-gray-600 hover:bg-blue-500 transition-colors duration-200 ${
            isResizing ? 'bg-blue-500' : ''
          }`}
          onMouseDown={handleMouseDown}
          title="拖拽调整表格高度"
        >
          <div className='flex w-full h-full items-center justify-center'>
            <div className='w-8 h-0.5 bg-white rounded opacity-60'></div>
          </div>
        </div>
      </div>
      <div className='flex flex-1 py-2 flex-col self-stretch border-t-[1px] border-t-gray-1 bg-[#ffffff08]'>
        <div className='flex py-1 px-2 flex-col gap-2 self-stretch flex-1'>
          <div className='flex px-2 gap-1 self-stretch items-center'>
            {!_.isNull(selectedLineItemResultKey) && !_.isNull(trainingSetSelectedDetail) && (
              <Button
                onClick={() => {
                  const run = async (selectedLineItemResult, refetchPayload, selectedFeature, displayedItems) => {
                    for (const agentResult of _.get(selectedLineItemResult, 'agentResults', [])) {
                      let inspection_result_id = _.get(agentResult, 'inspection_result_id', null);
                      if (_.isNull(inspection_result_id)) {
                        const curTrainingExample = _.find(displayedItems, i => {
                          let f = true;
                          for (const a of _.get(i, 'agentResults', [])) {
                            if (!a.training_example) f = false;
                          }
                          return f;
                        });
                        for (const a of _.get(curTrainingExample, 'agentResults', [])) {
                          const res = await markTrainingExample({
                            inspection_result_id: _.get(a, 'inspection_result_id', null),
                            mark: false,
                          });
                          if (res.error) {
                            aoiAlert(t('notification.error.markTrainingExample'), ALERT_TYPES.COMMON_ERROR);
                            console.error('markTrainingExample error:', _.get(res, 'error.message', ''));
                            return;
                          }
                        }

                        await updateDisplayedItems(refetchPayload);
                        return;
                      }
                      const res = await markTrainingExample({
                        inspection_result_id,
                        mark: !_.isNull(_.get(selectedLineItemResult, 'agentResults[0].inspection_result_id', null)),
                      });

                      if (res.error) {
                        aoiAlert(t('notification.error.markTrainingExample'), ALERT_TYPES.COMMON_ERROR);
                        console.error('markTrainingExample error:', _.get(res, 'error.message', ''));
                        return;
                      }
                    }
                    await updateDisplayedItems(refetchPayload);
                  };

                  const selectedLineItemResult = _.find(displayedItems, (item) => item.line_item_result_key === selectedLineItemResultKey);

                  const refetchPayload = generateCurrentQuery(
                    goldenProductId,
                    passedOnly,
                    selectedFeatureType,
                    selectedCid,
                    componentListGroupMode,
                    selectedPartNo,
                    selectedPackageNo,
                  );

                  run(selectedLineItemResult, refetchPayload, selectedFeature, displayedItems);
                }}
              >
                <div className='flex items-center gap-1'>
                  <div className='flex items-center justify-center w-[16px] h-[16px]'>
                    <img
                      src='/icn/star_yellow.svg'
                      alt='star'
                      className='w-[10px] h-[10px]'
                    />
                  </div>
                  <span className='font-source text-[12px] font-normal leading-[normal]'>
                    {t('productDefine.setAsGolden')}
                  </span>
                </div>
              </Button>
            )}
          </div>
          <SelectedFeatureErrorInfo
            selectedLineItemResult={selectedLineItemResult}
            selectedDetail={trainingSetSelectedDetail}
            setSelectedDetail={setTrainingSetSelectedDetail}
            trainingSetSelectedErrorType={trainingSetSelectedErrorType}
            setTrainingSetSelectedErrorType={setTrainingSetSelectedErrorType}
            selectedFeature={selectedFeature}
            handleRefetch={handleRefetch}
            feedbackErrorType={feedbackErrorType}
            selectedFeatureType={selectedFeatureType}
            selectedPartNo={selectedPartNo}
            selectedPackageNo={selectedPackageNo}
            goldenProductId={goldenProductId}
            passedOnly={passedOnly}
            componentListGroupMode={componentListGroupMode}
            selectedCid={selectedCid}
            
          />
        </div>
      </div>
    </div>
  );
};

const LineItemRecordCard = (props) => {
  const {
    selected,
    selectedLineItemResult,
    setSelectedLineItemResultKey,
    setSelectedDetail,
    trainingExample,
    setSelectedLineItemResult,
    setSelectedLineItemResultParsedError,
    setSelectedFid,
  } = props;

  const { t } = useTranslation();

  const [url, setUrl] = useState(null);
  const [latestPassStatus, setLatestPassStatus] = useState('empty');

  useEffect(() => {
    const firstAgent = _.get(selectedLineItemResult, 'agentResults[0]', {});

    let tmpUrl = `${serverHost}/blob?type=image`;
    tmpUrl = tmpUrl.concat(`&color_uri=${encodeURIComponent(_.get(firstAgent, 'component_color_map_uri', ''))}`);
    tmpUrl = tmpUrl.concat(`&depth_uri=${encodeURIComponent(_.get(firstAgent, 'component_depth_map_uri', ''))}`);
    tmpUrl = tmpUrl.concat(`&x_min=${_.get(firstAgent, 'roi.points[0].x', 0)}`);
    tmpUrl = tmpUrl.concat(`&y_min=${_.get(firstAgent, 'roi.points[0].y', 0)}`);
    tmpUrl = tmpUrl.concat(`&x_max=${_.get(firstAgent, 'roi.points[1].x', 0)}`);
    tmpUrl = tmpUrl.concat(`&y_max=${_.get(firstAgent, 'roi.points[1].y', 0)}`);
    tmpUrl = tmpUrl.concat(`&angle=${_.get(firstAgent, 'roi.angle', 0)}`);
    tmpUrl = tmpUrl.concat(`&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`);
    setUrl(tmpUrl);

    let flag;
    for (const a of _.get(selectedLineItemResult, 'agentResults', [])) {
      if (_.isEmpty(_.get(a, 'reevaluation_result', {}))) {
        flag = 'empty';
      } else {
        flag = _.get(a, 'reevaluation_result.pass', false);
        if (!flag) break;
      }
    }

    setLatestPassStatus(flag);
  }, [selectedLineItemResult]);

  return (
    <div
      className={`flex p-1 flex-col gap-0.5 items-center justify-center rounded-[2px] border-[1px] border-gray-2
        ${selected ? 'border-gray-3 bg-[#ffffff1a]' : 'bg-transparent'} cursor-pointer hover:bg-[#ffffff1a] transition-all duration-300`}
      onClick={() => {
        setSelectedLineItemResultKey(_.get(selectedLineItemResult, 'line_item_result_key'));
        setSelectedDetail(_.get(selectedLineItemResult, 'agentResults[0].detail'));
        setSelectedLineItemResult(selectedLineItemResult);
        setSelectedFid(_.get(selectedLineItemResult, 'agentResults[0].feature_id', null));
      }}
    >
      <img
        src={url}
        className='w-[120px] h-[120px] object-contain'
        alt='line item'
      />
      <div className='flex py-1 px-2 items-center justify-center gap-1 self-stretch w-[136px]'>
        {_.get(selectedLineItemResult, 'agentResults[0].training_example', false) && (
          <img
            src='/icn/star_yellow.svg'
            alt='star'
            className='w-[10px] h-[10px]'
          />
        )}
        <span className={`font-source text-[10px] font-normal leading-[150%] tracking-[normal]
          ${latestPassStatus === 'empty'
            ? 'text-[#fff]'
            : (latestPassStatus
              ?
                'text-[#6FCF97]'
              :
                'text-[#FF6A6A]'
              )
          }`}>
          {(() => {
            const featureType = _.get(selectedLineItemResult, 'agentResults[0].feature_type', '');
            return featureType.startsWith('_text') ? '_text' : featureType;
          })()}
        </span>
      </div>
    </div>
  );
};

const SelectedFeatureErrorInfo = (props) => {
  const {
    selectedLineItemResult,
    selectedDetail,
    setSelectedDetail,
    trainingSetSelectedErrorType,
    setTrainingSetSelectedErrorType,
    selectedFeature,
    handleRefetch,
    feedbackErrorType,
    selectedFeatureType,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    passedOnly,
    componentListGroupMode,
    selectedCid,
  } = props;

  const { t } = useTranslation();

  const [details, setDetails] = useState(null);
  const [curDisplayedErrorInfo, setCurDisplayedErrorInfo] = useState(null);
  const [selectedRCid, setSelectedRCid] = useState(null);
  const [selectedDetailsAgentResult, setSelectedDetailsAgentResult] = useState(null);
  const [selectedRPid, setSelectedRPid] = useState(null);
  const [selectedAgentParsedErrorDetail, setSelectedAgentParsedErrorDetail] = useState(null);
  const [currentParsedErrorInfo, setCurrentParsedErrorInfo] = useState(null);
  const [errorType, setErrorType] = useState(null);
  // const [feedbackErrorType, setFeedbackErrorType] = useState(null);

  const [annotateGroup] = useAnnotateGroupMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  useEffect(() => {
    // console.log('selectedLineItemResult', selectedLineItemResult);
    const newDetails = [];
    let firstDetailAgentResult;
    let firstPid;

    for (const a of _.get(selectedLineItemResult, 'agentResults', [])) {
      if (
        (selectedFeatureType === leadGapFeatureType &&
          _.get(a, 'detail') === leadInspection2D) ||
        (isAOI2DSMT &&
          [mountingInspection3D, leadInspection3D, solderInspection3D].includes(
            _.get(a, 'detail')
          ))
      ) {
        continue;
      }
      const d = { name: _.get(a, 'detail', null) };
      if (_.isEmpty(a.reevaluation_result)) {
        d.reevaluatePass = null;
      } else {
        d.reevaluatePass = _.get(a, 'reevaluation_result.pass', false);
      }
      newDetails.push(d);
      if (_.isEmpty(firstDetailAgentResult)) {
        firstDetailAgentResult = a;
        firstPid = _.split(_.get(selectedLineItemResult, 'line_item_result_key', ''), '_')[0];
      }
    }

    setSelectedRCid(_.get(firstDetailAgentResult, 'component_id', -1));
    setSelectedDetail(_.get(firstDetailAgentResult, 'detail', ''));
    setSelectedDetailsAgentResult(firstDetailAgentResult);
    setSelectedRPid(firstPid);
    setDetails(newDetails);
  }, [
    selectedLineItemResult,
    selectedFeatureType,
  ]);

  useEffect(() => {
    if (!selectedDetailsAgentResult) return;

    // console.log('selectedDetailsAgentResult', selectedDetailsAgentResult);

    let currentErrorDetail;

    // setFeedbackErrorType(_.get(selectedDetailsAgentResult, 'feedback_error_type', ''));

    if (_.isEmpty(selectedDetailsAgentResult.reevaluation_result)) {
      currentErrorDetail = _.get(JSON.parse(_.get(selectedDetailsAgentResult, 'error', '{}')), 'error_detail', {});
      setErrorType(_.get(selectedDetailsAgentResult, 'error_type', ''));
    } else {
      currentErrorDetail = JSON.parse(_.get(selectedDetailsAgentResult, 'reevaluation_result.error', {}));
      setErrorType(_.get(selectedDetailsAgentResult, 'reevaluation_result.error_type', ''));
    }

    const parsedError = {};

    for (const resultErrorType of _.keys(currentErrorDetail)) {
      let referenceLineItemParamName = _.get(systemMetadata, `score_to_param_map.[${selectedDetailsAgentResult.detail}/${resultErrorType}]`, null);
      referenceLineItemParamName = _.split(referenceLineItemParamName, '/')[1];
      const param = _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${referenceLineItemParamName}`);
      let paramTypeAndValue;
      if (resultErrorType === filletVolumeRatio) {
        // one result refers to three reference

        paramTypeAndValue = {
          type: filletVolumeRatio,
          filletUpperThreshold: _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletUpperThreshold}.param_float.value`, null),
          filletLowerThreshold: _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletLowerThreshold}.param_float.value`, null),
          filletOpenThreshold: _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletOpenThreshold}.param_float.value`, null),
          filletMinThreshold: _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletLowerThreshold}.param_float.min`, null),
          filletMaxThreshold: _.get(selectedFeature, `line_item_params.${selectedDetailsAgentResult.detail}.params.${filletUpperThreshold}.param_float.max`, null),
        };
      } else {
        paramTypeAndValue = getAgentParamTypeNValueByParam(
          param,
          selectedDetailsAgentResult.detail,
          referenceLineItemParamName,
        );
      }


      const rawInference = _.get(currentErrorDetail, `${resultErrorType}`, null);
      let inferenceResult = rawInference;
      let wrongPositions = [];
      let groupEquivalence = [];
      if (_.isObject(rawInference) && _.has(rawInference, 'predicted_text')) {
        inferenceResult = _.get(rawInference, 'predicted_text', '');
        paramTypeAndValue = {
          ...paramTypeAndValue,
          value: _.get(rawInference, 'expected_text', _.get(paramTypeAndValue, 'value')),
        };
        wrongPositions = _.get(rawInference, 'wrong_positions', []);
        groupEquivalence = _.get(rawInference, 'group_equivalence', []);
      }

      if ((selectedFeatureType === leadGapFeatureType
          ? twoDLeadGapAgentParamCheck(selectedDetailsAgentResult.detail, referenceLineItemParamName)
          : true) &&
        (selectedFeatureType === leadFeatureType
          ? referenceLineItemParamName !== 'bridge_threshold'
          : true)) {
        parsedError[`${resultErrorType}`] = {
          reference: paramTypeAndValue,
          inferenceResult,
          referenceAgentParamName: referenceLineItemParamName,
          detail: _.get(selectedDetailsAgentResult, 'detail', ''),
          wrongPositions,
          groupEquivalence,
        };
      }
    }

    setCurrentParsedErrorInfo({
      featureType: _.get(selectedFeature, `featureType`, ''),
      featureId: Number(_.get(selectedFeature, 'feature_id', 0)),
      parsedError,
    });
  }, [
    selectedDetailsAgentResult,
    selectedFeature,
    selectedFeatureType,
  ]);

  if (_.isEmpty(selectedLineItemResult)) return null;

  return (
    <Fragment>
      <div className='flex px-1 pt-1 items-center self-stretch gap-2'>
        <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px] text-gray-4'>
          {t(`featureTypeDisplayText.${(() => {
            const featureType = _.get(selectedLineItemResult, 'agentResults[0].feature_type', '');
            return featureType.startsWith('_text') ? '_text' : featureType;
          })()}`)}
        </span>
        <Select
          size='small'
          options={_.map(_.keys(_.get(systemMetadata, 'error_type_to_code', {})), (err) => ({
            value: err,
            label:
              <div className='flex items-center gap-1 h-[26px]'>
                {/* {!_.isEmpty(errorType) && errorType !== 'no_error' && errorType === err &&
                  <img
                    src='/icn/warning_red.svg'
                    alt='warning'
                    className='w-[12px] h-[12px]'
                  />
                }
                {!_.isEmpty(errorType) && errorType === 'no_error' && errorType === err &&
                  <img
                    src='/icn/check_green.svg'
                    alt='check'
                    className='w-[12px] h-[12px]'
                  />
                } */}
                {!_.isEmpty(feedbackErrorType) && feedbackErrorType === err && feedbackErrorType !== 'no_error' && (
                  <img
                    src='/icn/thumbdown_red.svg'
                    alt='thumbdown'
                    className='w-[12px] h-[12px]'
                  />
                )}
                {!_.isEmpty(feedbackErrorType) && feedbackErrorType === err && feedbackErrorType === 'no_error'&& (
                  <img
                    src='/icn/thumbup_green.svg'
                    alt='thumbup'
                    className='w-[12px] h-[12px]'
                  />
                )}
                {/* <span
                  className={`font-source text-[12px] font-normal leading-[150%] pt-0.5 ${errorType === err && errorType !== 'no_error' && 'text-[#EB5757]'}`}
                >
                  {t(`inferenceErrorType.${err}`)}
                </span> */}
                <span
                  className={`font-source text-[12px] font-normal leading-[150%] pt-0.5 `}
                >
                  {t(`inferenceErrorType.${err}`)}
                </span>
              </div>,
          }))}
          value={feedbackErrorType}
          onChange={(value) => {
            const run = async (selectedDetailsAgentResult, selectedRPid,
              goldenProductId,
              passedOnly,
              selectedFeatureType,
              selectedCid,
              componentListGroupMode,
              selectedPartNo,
              selectedPackageNo,
            ) => {
              const res = await annotateGroup({
                product_id: Number(selectedRPid),
                group_id: _.get(selectedDetailsAgentResult, 'component_id', -1),
                step: 0,
                error_type: value,
              });

              if (res.error) {
                aoiAlert(t('notification.error.annotateGroup'), ALERT_TYPES.COMMON_ERROR);
                console.error('annotateGroup error:', _.get(res, 'error.message', ''));
                return;
              }

              // TODO: refetch...
              await handleRefetch(
                goldenProductId,
                passedOnly,
                selectedFeatureType,
                selectedCid,
                componentListGroupMode,
                selectedPartNo,
                selectedPackageNo,
              );
            };

            run(selectedDetailsAgentResult, selectedRPid,
              goldenProductId,
              passedOnly,
              selectedFeatureType,
              selectedCid,
              componentListGroupMode,
              selectedPartNo,
              selectedPackageNo,
            );
          }}
          popupMatchSelectWidth={false}
          style={{ width: '120px' }}
        />
        <Tooltip
          title={
            <div className='flex flex-col gap-1 self-stretch'>
              {/* <div className='flex items-center gap-2'>
                <img
                  src='/icn/check_green.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.checkerDesc')}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/warning_red.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.warningDesc')}
                </span>
              </div> */}
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/thumbup_green.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.thumbsUpDesc')}
                </span>
              </div>
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/thumbdown_red.svg'
                  alt='info'
                  className='w-[12px] h-[12px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.thumbsDownDesc')}
                </span>
              </div>
            </div>
          }
        >
          <div className='flex items-center justify-center w-[16px] h-[16px]'>
            <img
              src='/icn/info_white.svg'
              alt='info'
              className='w-[12px] h-[12px]'
            />
          </div>
        </Tooltip>
      </div>
      <SmallTabsConfigProvider>
        <Tabs
          type='card'
          items={
            _.map(details, (detail) => {
              let color = '';
              if (detail.reevaluatePass === null) {
                color = 'text-[#fff]';
              } else {
                color = detail.reevaluatePass ? 'text-[#57F2C4]' : 'text-[#EB5757]';
              }
              return {
                key: detail.name,
                label: <span className={`font-source text-[12px] font-normal leading-[150%] ${color}`}>{t(`lineItemName.${detail.name}`)}</span>,
              };
            })
          }
          activeKey={_.get(selectedDetailsAgentResult, 'detail', '')}
          onChange={(key) => {
            setSelectedDetailsAgentResult(_.find(_.get(selectedLineItemResult, 'agentResults'), r => r.detail === key));
          }}
        />
      </SmallTabsConfigProvider>
      <div className='flex items-center gap-2 self-stretch px-1 py-1'>
        <span className='font-source text-[12px] font-normal leading-[150%] tracking-[0.36px]'>
          {t('productDefine.predictedErrorType')}: {t(`inferenceErrorType.${_.isEmpty(errorType) ? 'no_error' : errorType}`)}
        </span>
      </div>
      <div className='flex flex-1 self-stretch px-2 py-1 gap-2'>
        {!_.isEmpty(currentParsedErrorInfo) &&
          <InferenceResult
            featureError={currentParsedErrorInfo}
          />
        }
      </div>
    </Fragment>
  );
};

export default TrainingSet;