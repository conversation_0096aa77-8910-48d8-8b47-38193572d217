import { Input, DatePicker, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import i18n from '../../i18n';
import enUS from 'antd/es/calendar/locale/en_US';
import { customZhCNDatePickerLocale } from '../../common/const';
import CommonTable from '../../components/CommonTable';
import _ from 'lodash';
import { useGetAllProductsQuery } from '../../services/product';
import { backendTimestampToDisplayString, convertBackendTimestampToMoment } from '../../common/util';
import { useLazyGetAllSessionsQuery } from '../../services/inference';
import SessionList from './SessionList';
import InspectionList from './InspectionList';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AllInspectionList from './AllInspectionList';


const { RangePicker } = DatePicker;

const Worklist = () => {
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const prevIpcSessionId = searchParams.get('prev-ipc-session-id');

  const { t } = useTranslation();

  const [displayedData, setDisplayedData] = useState([]);
  const [curPageSize, setCurPageSize] = useState(0);
  const [displayedContentType, setDisplayedContentType] = useState('session');
  const [refreshToggle, setRefreshToggle] = useState(0);
  const [inspectionPagination, setInspectionPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });
  const [sessionPagination, setSessionPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });
  const [selectedSession, setSelectedSession] = useState(null);
  const [ipcSessionId, setIpcSessionId] = useState(null);
  const [startTimeInSession, setStartTimeInSession] = useState(null);
  const [endTimeInSession, setEndTimeInSession] = useState(null);
  const [selectedGoldenProdId, setSelectedGoldenProdId] = useState(null);
  const [startTimeInInspection, setStartTimeInInspection] = useState(null);
  const [endTimeInInspection, setEndTimeInInspection] = useState(null);
  const [onlyFeedbackProvided, setOnlyFeedbackProvided] = useState(false);
  const [onlyDefectiveItems, setOnlyDefectiveItems] = useState(false);
  const [searchSerialNumber, setSearchSerialNumber] = useState('');

  const { data: allProducts,  } = useGetAllProductsQuery();
  const [lazyGetAllSessions] = useLazyGetAllSessionsQuery();

  useEffect(() => {
    const prevIpcSessionIdInt = parseInt(prevIpcSessionId, 10);
    if (!_.isInteger(prevIpcSessionIdInt)) return;

    navigate('/worklist');

    const run = async () => {
      const res = await lazyGetAllSessions({
        ipc_session_id: prevIpcSessionIdInt,
      });

      if (res.error) {
        console.error('Failed to fetch sessions', res.error);
        return;
      }

      setIpcSessionId(prevIpcSessionIdInt);
      setDisplayedContentType('inspection');
      setSelectedSession(res.data?.[0] || null);
    };

    run();
  }, []);

  return (
    <div className='flex px-[140px] gap-4 flex-1 self-stretch bg-[#131313]'>
      <div className='flex flex-1 self-stretch pt-2 pb-4 gap-4 flex-col'>
        <div className='flex h-[48px] py-2 items-end gap-6 self-stretch'>
          <div className='flex gap-3 items-center'>
            <img src='/icn/checklist_white.svg' alt='checklist' className='w-5 h-5' />
            <span className='font-source text-[20px] font-semibold leading-[150%]'>
              {displayedContentType === 'session' && t('worklist.worklist')}
              {displayedContentType === 'inspection' && t('worklist.worklist')}
              {displayedContentType === 'allInspection' && t('worklist.allInspectionResult')}
            </span>
          </div>
          {displayedContentType !== 'inspection' &&
          <Button
            className='!bg-[#1A1A1A] border border-gray-1 !text-[#FFFFFF] !text-[14px] !leading-[150%] !font-source'
            onClick={() => {
              if (displayedContentType === 'session') {
                setDisplayedContentType('allInspection');
              } else if (displayedContentType === 'allInspection') {
                setDisplayedContentType('session');
              }
              setSelectedSession(null);
              setIpcSessionId(null);
              setStartTimeInSession(null);
              setEndTimeInSession(null);
              setSelectedGoldenProdId(null);
              setStartTimeInInspection(null);
              setEndTimeInInspection(null);
              setOnlyFeedbackProvided(false);
              setOnlyDefectiveItems(false);
              setSearchSerialNumber('');
            }}
          >
            {displayedContentType === 'session' ? t('worklist.displayAllInspectionResult')
              : t('worklist.displayWorklist')
            }
          </Button>}
        </div>
        <div className='w-full h-[1px] bg-[#333]' />
        { displayedContentType === 'session' &&
          <SessionList
            setDisplayedContentType={setDisplayedContentType}
            allProducts={allProducts}
            selectedGoldenProdId={selectedGoldenProdId}
            setSelectedGoldenProdId={setSelectedGoldenProdId}
            startTimeInSession={startTimeInSession}
            setStartTimeInSession={setStartTimeInSession}
            endTimeInSession={endTimeInSession}
            setEndTimeInSession={setEndTimeInSession}
            // setIsExportModalOpened={setIsExportModalOpened}
            setIpcSessionId={setIpcSessionId}
            setSelectedSession={setSelectedSession}
            pagination={sessionPagination}
            setPagination={setSessionPagination}
            refreshToggle={refreshToggle}
            setRefreshToggle={setRefreshToggle}
          />
        }
        { displayedContentType === 'inspection' &&
          <InspectionList
            setDisplayedContentType={setDisplayedContentType}
            selectedGoldenProdId={selectedGoldenProdId}
            allProducts={allProducts}
            startTimeInInspection={startTimeInInspection}
            setStartTimeInInspection={setStartTimeInInspection}
            endTimeInInspection={endTimeInInspection}
            setEndTimeInInspection={setEndTimeInInspection}
            onlyFeedbackProvided={onlyFeedbackProvided}
            setOnlyFeedbackProvided={setOnlyFeedbackProvided}
            onlyDefectiveItems={onlyDefectiveItems}
            setOnlyDefectiveItems={setOnlyDefectiveItems}
            setSelectedGoldenProdId={setSelectedGoldenProdId}
            ipcSessionId={ipcSessionId}
            selectedSession={selectedSession}
            pagination={inspectionPagination}
            setPagination={setInspectionPagination}
            refreshToggle={refreshToggle}
            setRefreshToggle={setRefreshToggle}
            setSearchSerialNumber={setSearchSerialNumber}
            searchSerialNumber={searchSerialNumber}
          />
        }
        { displayedContentType === 'allInspection' &&
          <AllInspectionList  />
        }
        {/* <div className='w-full h-[1px] bg-[#333]' />
        <div className='flex h-[64px] self-stretch py-4'>

        </div> */}
      </div>
    </div>
  );
};

export default Worklist;