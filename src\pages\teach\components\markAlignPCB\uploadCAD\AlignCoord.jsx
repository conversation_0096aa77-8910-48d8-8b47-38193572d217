import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Slider } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetImageMetaDataQuery } from '../../../../../services/camera';
import AlignCoordViewer from '../../../../../viewer/AlignCoordViewer';
import _ from 'lodash';
import { useRegisterProductFeatureMutation, useRunSemiAutoProgramMutation } from '../../../../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../../../../common/alert';
import ConfigAutoProgram from '../../../../../modal/ConfigAutoProgram';
import { useDispatch } from 'react-redux';
import { setIsContainerLvlLoadingEnabled, setContainerLvlLoadingMsg, setIsTrainingRunning, setCurTrainingTaskStartTime, setShouldRunReevaluateAfterRetrain, setIsAgentParamRegenRunning } from '../../../../../reducer/setting';
import { useModelUpdateTriggerMutation } from '../../../../../services/inference';
import { modelTypes } from '../../../../../common/const';
import { useNavigate } from 'react-router-dom';


const AlignCoord = (props) => {
	const {
		parsedComponentInfo,
		setCurrentStep,
		curProduct,
		currentFileUri,
		parseRules,
		currentTranslation,
		setCurrentTranslation,
		currentRotation,
		setCurrentRotation,
		horizontallyFlipped,
		setHorizontallyFlipped,
		verticallyFlipped,
		setVerticallyFlipped,
		isInAutoProgramming,
		selectedInspectionTypes,
		autoProgramInspectionRegion,
	} = props;

	const navigate = useNavigate();

        const dispatch = useDispatch();
        const [retrainTrigger] = useModelUpdateTriggerMutation();

	const { t } = useTranslation();

	// const [currentTranslation, setCurrentTranslation] = useState({
	//   x: 0,
	//   y: 0,
	// });
	// const [currentRotation, setCurrentRotation] = useState(0);
	const [isConfigAutoProgramOpened, setIsConfigAutoProgramOpened] = useState(false);
	const [editAlignCoord, setEditAlignCoord] = useState(0);
	const [cadInfo, setCadInfo] = useState({ rules: {} });

	const handleAutoGenerateAgentParams = async (productId) => {
		dispatch(setIsContainerLvlLoadingEnabled(true));
		dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));

		const res = await retrainTrigger({
			model_types: [
				modelTypes.mounting3DModel,
				modelTypes.lead3DModel,
				modelTypes.solder3DModel,
				modelTypes.lead2DV2Model,
				modelTypes.solder2DModel,
				modelTypes.leadModel,
				modelTypes.mountingModel,
				modelTypes.textDirectionModel,
			],
			golden_product_id: Number(productId),
			update_parameters: true,
		});

		if (res.error) {
			aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
			console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
			dispatch(setIsContainerLvlLoadingEnabled(false));
			dispatch(setContainerLvlLoadingMsg(''));
			return false;
		}

		dispatch(setIsContainerLvlLoadingEnabled(true));
		dispatch(setContainerLvlLoadingMsg(t('loader.refetchingFeatures')));
		dispatch(setIsAgentParamRegenRunning(true));
		dispatch(setIsTrainingRunning(true));
		dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
		return true;
	};

	const { data: prodMetadata } = useGetImageMetaDataQuery({
		uri: _.get(curProduct, 'inspectables[0].color_map_uri'),
	});
	const [runSemiAutoProgram] = useRunSemiAutoProgramMutation();

	const handleRunSemiAutoProgram = async (
		parseRules,
		currentFileUri,
		productId,
		selectedInspectionTypes,
		currentTranslation,
		currentRotation,
		horizontallyFlipped,
		autoProgramInspectionRegion,
	) => {
		// console.log('parseRules when submit', parseRules);
    if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
    if (_.isEmpty(parseRules.delimiter)) {
      aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isNumber(parseRules.unitMutiplier)) {
      aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (
      // !_.isInteger(parseRules.partNumberCol) ||
      // !_.isInteger(parseRules.packageCol) ||
			!_.isInteger(parseRules.xCol) || !_.isInteger(parseRules.yCol)) {
      aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (_.get(parseRules, 'isIgnoreBotLayer', false) && !_.isString(parseRules, 'botLayerId', null) && !_.isInteger(parseRules.botLayerCol)) {
      aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.firstRowIndex) || !_.isInteger(parseRules.lastRowIndex)) {
      aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.designatorCol)) {
      aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // init parse we use 0 for tx, ty, rotation
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex'),
      data_row_end: _.get(parseRules, 'lastRowIndex'),
      unit_multiplier: _.get(parseRules, 'unitMutiplier'),
      part_number_col: _.get(parseRules, 'partNumberCol'),
      package_number_col: _.get(parseRules, 'packageCol'),
      x_col: _.get(parseRules, 'xCol'),
      y_col: _.get(parseRules, 'yCol'),
      designator_col: _.get(parseRules, 'designatorCol'),
    };

    // TODO: waiting for backend to support this
    if (_.isUndefined(parseRules.partNumberCol)) {
      delete rules['part_number_col'];
    }

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoProgramming')));

    const res = await runSemiAutoProgram({
      product_id: Number(productId),
      config: {
        component_for_line_item: selectedInspectionTypes,
      },
      step: 0,
      cad_info: {
        file_path: currentFileUri,
        rules,
        product_id: Number(productId),
        tx: currentTranslation.x,
        ty: currentTranslation.y,
        rotation: currentRotation,
        flip_board: horizontallyFlipped,
      },
			roi: {
				type: 'obb',
				points: [
					{
						x: _.round(_.get(autoProgramInspectionRegion, 'pmin.x', 0), 0),
						y: _.round(_.get(autoProgramInspectionRegion, 'pmin.y', 0), 0),
					},
					{
						x: _.round(_.get(autoProgramInspectionRegion, 'pmax.x', 0), 0) - 1,
						y: _.round(_.get(autoProgramInspectionRegion, 'pmax.y', 0), 0) - 1,
					}
				],
				center: null,
				angle: 0,
			}
    });

    if (res.error) {
      aoiAlert(t('autoProgramming.semiAutoProgramFail'), ALERT_TYPES.COMMON_ERROR);
      console.error('runSemiAutoProgram error:', _.get(res, 'error.message', ''));

      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

		dispatch(setIsContainerLvlLoadingEnabled(false));
		dispatch(setContainerLvlLoadingMsg(''));

		// dispatch(setShouldRunReevaluateAfterRetrain({ productId: Number(productId), shouldRun: true}));
		// await handleAutoGenerateAgentParams(productId);

		navigate(`/teach?product-id=${productId}&from-auto-programming=true`);
		return;
	};


	useEffect(() => {
		if (!isInAutoProgramming) return;

    // console.log('parseRules when submit', parseRules);
    if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
    if (_.isEmpty(parseRules.delimiter)) {
      // aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isNumber(parseRules.unitMutiplier)) {
      // aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (
      // !_.isInteger(parseRules.partNumberCol) ||
      // !_.isInteger(parseRules.packageCol) ||
			!_.isInteger(parseRules.xCol) || !_.isInteger(parseRules.yCol)) {
      // aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (_.get(parseRules, 'isIgnoreBotLayer', false) && !_.isString(parseRules, 'botLayerId', null) && !_.isInteger(parseRules.botLayerCol)) {
      // aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.firstRowIndex) || !_.isInteger(parseRules.lastRowIndex)) {
      // aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.designatorCol)) {
      // aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // init parse we use 0 for tx, ty, rotation
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex'),
      data_row_end: _.get(parseRules, 'lastRowIndex'),
      unit_multiplier: _.get(parseRules, 'unitMutiplier'),
      part_number_col: _.get(parseRules, 'partNumberCol'),
      package_number_col: _.get(parseRules, 'packageCol'),
      x_col: _.get(parseRules, 'xCol'),
      y_col: _.get(parseRules, 'yCol'),
      designator_col: _.get(parseRules, 'designatorCol'),
    };

    // TODO: waiting for backend to support this
    if (_.isUndefined(parseRules.partNumberCol)) {
      delete rules['part_number_col'];
    }

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    setCadInfo({
      file_path: currentFileUri,
      rules,
      product_id: Number(curProduct.product_id),
      tx: currentTranslation.x,
      ty: currentTranslation.y,
      rotation: currentRotation,
			flip_board: horizontallyFlipped,
    });
	}, [
		parseRules,
		currentFileUri,
		curProduct,
		currentTranslation,
		currentRotation,
		horizontallyFlipped,
	]);

	return (
		<Fragment>
		{/* <ConfigAutoProgram
      isOpened={isConfigAutoProgramOpened}
      setIsOpened={setIsConfigAutoProgramOpened}
      productId={Number(curProduct.product_id)}
      step={0}
      onFinish={async () => {
				// if semi auto programming failed, no need to do anything this time
				return;
      }}
      isFullAutoProgram={isInAutoProgramming}
      cadInfo={cadInfo}
			isNotInitialSemiAutoProgram={true}
    /> */}
		<div className="flex gap-8 flex-1 self-stretch px-[64px] justify-center">
			<div className="flex flex-1 self-stretch">
				<AlignCoordViewer
					curProduct={curProduct}
					prodMetadata={prodMetadata}
					currentTranslation={currentTranslation}
					setCurrentTranslation={setCurrentTranslation}
					currentRotation={currentRotation}
					setCurrentRotation={setCurrentRotation}
					parsedComponentInfo={parsedComponentInfo}
					editAlignCoord={editAlignCoord}
					horizontallyFlipped={horizontallyFlipped}
					verticallyFlipped={verticallyFlipped}
				/>
			</div>
			<div className="flex flex-col gap-2 self-stretch">
				<div className="flex py-1 items-center gap-2 w-[348px]">
					<div
						className="flex h-4 w-4 items-center justify-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px] transition-all duration-300 ease-in-out"
						onClick={() => {
							setCurrentStep(0);
						}}
					>
						<img
							src="/icn/arrowLeft_white.svg"
							alt="arrowLeft"
							className="w-[6px] h-[10px]"
						/>
					</div>
					<span className="font-source text-[16px] font-semibold leading-[normal]">
						{t('common.previous')}
					</span>
				</div>
				<div className="flex w-[346px] p-4 bg-[#ffffff0d] gap-6 flex-col self-stretch">
					<div className="flex flex-col gap-1 self-stretch">
						<span className="font-source text-[16px] font-normal leading-[150%]">
							{t('productDefine.alignCoord')}
						</span>
						<span className="font-source text-[14px] font-normal leading-[150%]">
							{t('productDefine.moveAndAlign')}
						</span>
						<Button
							type="primary"
							onClick={() => {
								setEditAlignCoord(editAlignCoord + 1);
							}}
						>
							<span className="font-source text-[12px] font-semibold leading-[normal]">
								{t('common.translate')}
							</span>
						</Button>
						<Button
							onClick={() => {
								setEditAlignCoord(0);
							}}
						>
							<span className="font-source text-[12px] font-semibold leading-[normal]">
								{t('common.cancel')}
							</span>
						</Button>
					</div>
					<div className="flex flex-col justify-center gap-4 self-stretch">
						<div className="flex gap-1 flex-col self-stretch">
							<span className="font-source text-[12px] font-normal leading-[150%]">
								{t('allUpperCases.translate')}
							</span>
							<div className="flex items-center gap-4 self-stretch">
								<div className="flex flex-1 h-[26px] items-center gap-2">
									<span className="font-source text-[14px] font-normal leading-[150%]">
										X
									</span>
									<InputNumber
										style={{ width: '100%' }}
										step={1}
										size="small"
										value={currentTranslation.x}
										onChange={(value) =>
											setCurrentTranslation({ ...currentTranslation, x: value })
										}
										controls={false}
									/>
								</div>
								<div className="flex flex-1 h-[26px] items-center gap-2">
									<span className="font-source text-[14px] font-normal leading-[150%]">
										Y
									</span>
									<InputNumber
										style={{ width: '100%' }}
										step={1}
										size="small"
										value={currentTranslation.y}
										onChange={(value) =>
											setCurrentTranslation({ ...currentTranslation, y: value })
										}
										controls={false}
									/>
								</div>
							</div>
						</div>
						<div className="flex gap-1 flex-col self-stretch">
							<span className="font-source text-[12px] font-normal leading-[150%]">
								{t('allUpperCases.rotate')}
							</span>
							<div className="flex items-center gap-4 self-stretch">
								<div className="flex flex-1 h-[26px] items-center gap-2">
									<InputNumber
										style={{ width: '100%' }}
										size="small"
										value={currentRotation}
										onChange={(value) => setCurrentRotation(value)}
										controls={true}
										max={360}
										step={0.0001}
										addonBefore={
											<div className="flex h-[14px] w-[12px] items-center justify-center">
												<img
													src="/icn/rightAngle_white.svg"
													alt="rightAngle"
													className="w-[10px] h-[10px]"
												/>
											</div>
										}
									/>
								</div>
								<div className="flex flex-1 h-[26px] items-center gap-1">
									<div
										className="flex h-[26px] w-[26px] items-center justify-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px] transition-all duration-300 ease-in-out"
										onClick={() => {
											setCurrentRotation((currentRotation + 90) % 360);
										}}
									>
										<img
											src="/icn/clockWise90Deg_white.svg"
											className="w-[10px] h-[12px]"
										/>
									</div>
									<div
										className="flex h-[26px] w-[26px] items-center justify-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px] transition-all duration-300 ease-in-out"
										onClick={() => {
											setCurrentRotation((currentRotation - 90) % 360);
										}}
									>
										<img
											src="/icn/counterClock90Deg_white.svg"
											className="w-[10px] h-[12px]"
										/>
									</div>
									<div
										className={`flex h-[26px] w-[26px] items-center justify-center cursor-pointer rounded-[4px] transition-all duration-300 
                      ease-in-out ${
												horizontallyFlipped
													? 'bg-[#56CCF2]'
													: 'hover:bg-[#ffffff0d]'
											}`}
										onClick={() => {
											setHorizontallyFlipped(!horizontallyFlipped);
										}}
									>
										<img
											src={
												!horizontallyFlipped
													? '/icn/flipHorizontally_white.svg'
													: '/icn/flipHorizontally_gray.svg'
											}
											alt="flipHorizontally"
											className="w-[13px] h-[16.55px]"
										/>
									</div>
									{/* <div
                    className={`flex h-[26px] w-[26px] items-center justify-center cursor-pointer rounded-[4px] transition-all duration-300 
                      ease-in-out ${verticallyFlipped ? 'bg-[#56CCF2]' : 'hover:bg-[#ffffff0d]'}`}
                    onClick={() => {
                      setVerticallyFlipped(!verticallyFlipped);
                    }}
                  >
                    <img
                      src={!verticallyFlipped ? '/icn/flipVertically_white.svg' : '/icn/flipVertically_gray.svg'}
                      alt='flipVertically'
                      className='w-[16px] h-[12.57px]'
                    />
                  </div> */}
								</div>
							</div>
						</div>
					</div>
					{!isInAutoProgramming ?
						<Button
							type="primary"
							onClick={() => {
								setCurrentStep(2);
							}}
						>
							<span className="font-source text-[12px] font-semibold leading-[normal]">
								{t('common.continue')}
							</span>
						</Button>
					:
						<Fragment>
							<Button
								type='primary'
								onClick={() => {
									// setIsConfigAutoProgramOpened(true);
									handleRunSemiAutoProgram(
										parseRules,
										currentFileUri,
										Number(curProduct.product_id),
										selectedInspectionTypes,
										currentTranslation,
										currentRotation,
										horizontallyFlipped,
										autoProgramInspectionRegion,
									);
								}}
							>
								<span className='font-source text-[12px] font-semibold leading-[normal]'>
									{t('autoProgramming.reAnalyze')}
								</span>
							</Button>
							<Button
								onClick={() => {
									setCurrentStep(2);
								}}
							>
								<span className='font-source text-[12px] font-semibold leading-[normal]'>
									{t('autoProgramming.skipToTemplateEditor')}
								</span>
							</Button>
						</Fragment>
					}
				</div>
			</div>
		</div>
		</Fragment>
	);
};

export default AlignCoord;
