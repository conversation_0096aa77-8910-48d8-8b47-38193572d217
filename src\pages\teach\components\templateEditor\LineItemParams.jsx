import { Checkbox, Collapse, ConfigProvider } from 'antd';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateComponentMutation, useUpdateFeatureMutation } from '../../../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import AgentParams from './AgentParams';


const LineItemParams = (props) => {
  const {
    selectedFeature,
    refetchAllFeatures,
    setSelectedAgentParam,
    allFeatures,
    refetchAllComponents,
    updateAllFeaturesState,
  } = props;
  
  const { t } = useTranslation();

  const [updateFeature] = useUpdateFeatureMutation();
  const [updateComponent] = useUpdateComponentMutation();

  return (
    <Fragment>
      {_.map(_.keys(_.get(selectedFeature, 'line_item_params')), (agent<PERSON><PERSON>, id) => (
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                headerBg: '#00000033',
              }
            }
          }}
        >
          <Collapse
            style={{ width: '100%' }}
            defaultActiveKey={[agentKey]}
            items={[
              {
                key: agentKey,
                label: <div className='flex flex-1 justify-between items-center'>
                  <span className='font-source text-[12px] font-semibold leading-[normal] pt-0.5'>
                    {t(`lineItemName.${agentKey}`)}
                  </span>
                  <Checkbox
                    checked={_.get(selectedFeature, `line_item_params.${agentKey}.enabled`, false)}
                    onClick={(e) => {
                      e.stopPropagation();
                      let newFeatureObj = _.cloneDeep(selectedFeature);
                      newFeatureObj = _.set(newFeatureObj, `line_item_params.${agentKey}.enabled`, !_.get(selectedFeature, `line_item_params.${agentKey}.enabled`, false));

                      const submit = async (newFeatureObj) => {
                        const res = await updateFeature({ body: newFeatureObj, params: { allComponents: _.isInteger(newFeatureObj.group_id) } });

                        if (res.error) {
                          aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error('update feature failed', res.error.message);
                          return;
                        }

                        // await updateAllFeaturesState([newFeatureObj.feature_id], 'update', [newFeatureObj]);
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
                        }
                      };

                      submit(newFeatureObj);
                    }}
                  />
                </div>,
                children: <AgentParams
                  agentObj={_.get(selectedFeature, `line_item_params.${agentKey}`, {})}
                  refetchAllFeatures={refetchAllFeatures}
                  updateAllFeaturesState={updateAllFeaturesState}
                  selectedFeature={selectedFeature}
                  lintItemName={agentKey}
                  setSelectedAgentParam={setSelectedAgentParam}
                  allFeatures={allFeatures}
                  refetchAllComponents={refetchAllComponents}
                  updateComponent={updateComponent}
                />
              }
            ]}
          />
        </ConfigProvider>
      ))}
    </Fragment>
  );
};

export default LineItemParams;