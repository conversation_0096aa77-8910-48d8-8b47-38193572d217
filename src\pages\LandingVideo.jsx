import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';


const LandingVideo = () => {
  const navigate = useNavigate();

  const videoRef = useRef(null);

  useEffect(() => {
    if (!videoRef.current) return;

    const video = videoRef.current;
    // Prevent right-click menu
    video.addEventListener("contextmenu", e => e.preventDefault());

    // Prevent pause
    video.addEventListener("pause", () => {
      if (!video.ended) video.play();
    });

    // Prevent seeking
    video.addEventListener("seeking", () => {
      if (video.currentTime !== 0 && !video.ended) {
        video.currentTime = video._lastTime || 0;
      }
    });

    // Store last time (so we can lock it)
    video.addEventListener("timeupdate", () => {
      video._lastTime = video.currentTime;
    });

    video.addEventListener("ended", () => {
      navigate('/login');
    });

    return () => {
      video.removeEventListener("contextmenu", e => e.preventDefault());
      video.removeEventListener("pause", () => {
        if (!video.ended) video.play();
      });
      video.removeEventListener("seeking", () => {
        if (video.currentTime !== 0 && !video.ended) {
          video.currentTime = video._lastTime || 0;
        }
      });
      video.removeEventListener("timeupdate", () => {
        video._lastTime = video.currentTime;
      });
      video.removeEventListener("ended", () => {
        navigate('/login');
      });      
    }    
  }, []);

  return (
    <div
      className='flex w-full h-full items-center justify-center'
    >
      <video
        style={{
          width: '100vw',
          height: '100vh',
        }}
        muted
        ref={videoRef}
        autoPlay={true}
        controls={false}
        disablePictureInPicture
        controlsList="nodownload nofullscreen noremoteplayback"
        className='object-contain'
        src={'/video/landing.mp4'}
      />
    </div>
  );
};

export default LandingVideo;