import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { serverHost, highResoluCroppedDisplayMegaPixelCount, leadInspection3D, leadInspection2D, isAOI2DSMT, leadInspection2DBase } from '../../../../common/const';

const LeadGapSegmentRecordCard = (props) => {
  const {
    selected,
    selectedLineItemResult,
    gapIndex,
    setSelectedLineItemResultKey,
    setSelectedDetail,
    setSelectedLineItemResult,
    setSelectedLineItemResultParsedError,
    setSelectedFid,
    setSelectedCid,
    allFeatures,
    setRequiredLocateRect,
    clickFromTrainingSetCardRef,
    setSelectedArrayIndex,
    mmToPixelRatio,
    isPesudoColorDisplayed,
  } = props;

  const { t } = useTranslation();
  const [url, setUrl] = useState(null);
  const [latestPassStatus, setLatestPassStatus] = useState('empty');
  const [passStatus, setPassStatus] = useState(null);
  const [isGroudTrueGreen, setIsGroudTrueGreen] = useState(true);
  const [isTrainingExample, setIsTrainingExample] = useState(false);

  useEffect(() => {
    const firstAgent = _.get(selectedLineItemResult, '[0]', {});
    const feature = _.find(allFeatures, f => f.feature_id === _.get(firstAgent, 'feature_id', null));
    const agentName = isAOI2DSMT ? leadInspection2D : leadInspection3D;
    const leadCount = _.get(feature, `line_item_params.${agentName}.params.lead_count.param_int.value`, 0);
    const leadWidthMM = _.get(feature, `line_item_params.${agentName}.params.lead_width_mm.param_float.value`, 0);
    const pMin = _.get(firstAgent, 'roi.points[0]', { x: 0, y: 0 });
    const pMax = _.get(firstAgent, 'roi.points[1]', { x: 0, y: 0 });
    const width = pMax.x - pMin.x + 1;
    const leadWidth = leadWidthMM * mmToPixelRatio;
    let xCenter = _.get(firstAgent, 'roi.points[0].x', 0) + (_.get(firstAgent, 'roi.points[1].x', 0) - _.get(firstAgent, 'roi.points[0].x', 0) + 1) / 2;
    let yCenter = _.get(firstAgent, 'roi.points[0].y', 0) + (_.get(firstAgent, 'roi.points[1].y', 0) - _.get(firstAgent, 'roi.points[0].y', 0) + 1) / 2;

    const leadGapWidth = (width - leadCount * leadWidth) / (leadCount - 1);

    const xMin = pMin.x + leadWidth + gapIndex * leadGapWidth;
    const xMax = xMin + leadGapWidth;

    let tmpUrl = `${serverHost}/blob?type=${isPesudoColorDisplayed ? 'depth' : 'image'}`;
    tmpUrl += `&color_uri=${encodeURIComponent(_.get(firstAgent, 'component_color_map_uri', ''))}`;
    tmpUrl += `&depth_uri=${encodeURIComponent(_.get(firstAgent, 'component_depth_map_uri', ''))}`;
    // tmpUrl += `&x_min=${Math.floor(xMin)}`;
    // tmpUrl += `&y_min=${Math.round(pMin.y)}`;
    // tmpUrl += `&x_max=${Math.ceil(xMax)}`;
    // tmpUrl += `&y_max=${Math.round(pMax.y)}`;

    tmpUrl += `&x_min=${Math.round(pMin.x)}`;
    tmpUrl += `&y_min=${Math.round(pMin.y)}`;
    tmpUrl += `&x_max=${Math.round(pMax.x)}`;
    tmpUrl += `&y_max=${Math.round(pMax.y)}`;

    tmpUrl += `&angle=${_.get(firstAgent, 'roi.angle', 0)}`;
    tmpUrl += `&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
    tmpUrl += `&x_center=${xCenter}`;
    tmpUrl += `&y_center=${yCenter}`;
    tmpUrl += `&t=${Date.now()}`;

    const downloadAndCrop = async () => {
      const res = await fetch(tmpUrl);
      const blob = await res.blob();
      const img = await createImageBitmap(blob);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = leadGapWidth;
      canvas.height = pMax.y - pMin.y;
      const sx = (gapIndex + 1) * leadWidth + gapIndex * leadGapWidth;
      const sh = pMax.y - pMin.y;

      ctx.drawImage(img, sx, 0, leadGapWidth, sh, 0, 0, leadGapWidth, sh);
      const croppedBlob = await new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/jpeg');
      });
      const croppedUrl = URL.createObjectURL(croppedBlob);
      setUrl(croppedUrl);
    };

    downloadAndCrop();
    // setUrl(tmpUrl);

    let flag;
    for (const a of selectedLineItemResult) {
      if (a.detail !== leadInspection2DBase) continue;
      setIsTrainingExample(_.get(a, 'training_example', false));
      if (_.isEmpty(_.get(a, 'reevaluation_result', {}))) {
        flag = 'empty';
      } else {
        const parsedError = JSON.parse(_.get(a, 'reevaluation_result.error', '{}'));
        flag = _.get(parsedError, 'bridge_pass', false);
        // flag = _.get(a, 'reevaluation_result.pass', false);
        if (!flag) break;
      }
    }

    setPassStatus(_.get(selectedLineItemResult, '[0].pass', false));
    setLatestPassStatus(flag);

    if (_.get(selectedLineItemResult, '[0].training_example', false)) {
      setIsGroudTrueGreen(true);
    } else {
      // search for agent result that has feedback
      let feedbackFound = false;
      for (const r of selectedLineItemResult) {
        // if (!_.isEmpty(_.get(r, 'feedback', {})) && r.detail === leadInspection2DBase && !_.isEmpty(_.get(r, 'reevaluation_result', {}))) {
        //   const parsedError = JSON.parse(_.get(r, 'reevaluation_result.error', '{}'));
        //   const pass = _.get(parsedError, 'bridge_pass', false);
        //   if (
        //     (pass && _.get(r, 'feedback.correct', false)) ||
        //     (!pass && !_.get(r, 'feedback.correct', false))
        //   ) {
        //     setIsGroudTrueGreen(true);
        //   } else {
        //     setIsGroudTrueGreen(false);
        //   }
        //   break;
        // }

        if (!_.isEmpty(_.get(r, 'feedback', {})) && r.detail === leadInspection2DBase) {
          const inferenceResult = JSON.parse(_.get(r, 'error', '{}'));
          const pass = _.get(inferenceResult, 'error_detail.bridge_pass', false);

          if (
            (pass && _.get(r, 'feedback.correct', false)) ||
            (!pass && !_.get(r, 'feedback.correct', false))
          ) {
            setIsGroudTrueGreen(true);
          } else {
            setIsGroudTrueGreen(false);
          }
          feedbackFound = true;
          break;
        }
      }
      // set to pass status ow
      if (!feedbackFound) setIsGroudTrueGreen(_.get(selectedLineItemResult, '[0].pass', false));
    }
  }, [selectedLineItemResult, gapIndex, mmToPixelRatio, isPesudoColorDisplayed]);

  return (
    <div
      className={`flex p-1 flex-col gap-0.5 items-center justify-center rounded-[2px] border-[1px] border-gray-2 ${selected ? 'border-gray-3 bg-[#ffffff1a]' : 'bg-transparent'} cursor-pointer hover:bg-[#ffffff1a] transition-all duration-300`}
      onClick={() => {
        clickFromTrainingSetCardRef.current = true;
        const dcid = _.get(_.find(allFeatures, f => f.feature_id === _.get(selectedLineItemResult, '[0].feature_id', null)), 'group_id', null);
        const cid = _.get(selectedLineItemResult, '[0].component_id', null);
        setSelectedLineItemResultKey(`${cid}_${_.get(selectedLineItemResult, '[0].feature_id', null)}_${_.get(selectedLineItemResult, '[0].array_index', null)}`);
        setSelectedDetail(_.get(selectedLineItemResult, '[0].detail'));
        setSelectedLineItemResult(selectedLineItemResult);
        setSelectedArrayIndex(_.get(selectedLineItemResult, '[0].array_index', null));
        const fid = _.get(selectedLineItemResult, '[0].feature_id', null);
        setSelectedCid(dcid);
        setSelectedFid(fid);
        setRequiredLocateRect({ cid: dcid, fid });
      }}
    >
      <img src={url} className='w-[120px] h-[120px] object-contain' alt='lead gap'/>

      <div className="flex h-2 items-center gap-0.5 self-stretch w-[120px] mx-auto">
        {/* <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${passStatus ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'} pl-3 pr-1.5 py-1`} /> */}
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${
          isGroudTrueGreen ? '[background:var(--Failed,#27AE60)]' : '[background:var(--Failed,#FA4D56)]'
        } pl-3 pr-1.5 py-1`} />
        <div className={`flex items-center gap-2.5 flex-[1_0_0] self-stretch ${latestPassStatus === 'empty'
          ? 'border border-[color:var(--default-Gray-3,#828282)]'
          : (latestPassStatus
            ?
              '[background:var(--Failed,#27AE60)]'
            :
              '[background:var(--Failed,#FA4D56)]'
            ) } pl-3 pr-1.5 py-1`}
        />
      </div>

      <div className='flex py-1 px-2 items-center justify-center gap-1 self-stretch w-[136px]'>
        {isTrainingExample && (
          <img
            src='/icn/star_yellow.svg'
            alt='star'
            className='w-[10px] h-[10px]'
          />
        )}
        <span className={`font-source text-[10px] font-normal leading-[150%] tracking-[normal] ${latestPassStatus === 'empty' ? 'text-[#fff]' : (latestPassStatus ? 'text-[#6FCF97]' : 'text-[#FF6A6A]')}`}>
          {/* {t('common.gap')} {gapIndex + 1} */}
          {_.isInteger(_.get(selectedLineItemResult, '[0].array_index', null)) && `${t('common.subBoard')}${_.get(selectedLineItemResult, '[0].array_index', '') + 1}.`}
          gap
        </span>
      </div>
    </div>
  );
};

export default LeadGapSegmentRecordCard;
