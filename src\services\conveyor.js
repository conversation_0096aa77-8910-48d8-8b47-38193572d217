import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const conveyorApi = createApi({
  reducerPath: 'conveyorApi',
  baseQuery,
  tagTypes: ['Conveyor'],
  endpoints: (build) => ({
    getAllConveyorStatus: build.query({
      query: (id) => ({
        url: '/conveyorSlots',
        method: 'GET',
      }),
    }),
    acquireConveyorControl: build.mutation({
      query: (body) => ({
        url: '/conveyorControl',
        method: 'POST',
        body,
      }),
    }),
    resizeConveyor: build.mutation({
      query: (body) => ({
        url: '/resizeConveyor',
        method: 'POST',
        body,
      }),
    }),
    releaseConveyorControl: build.mutation({
      query: (body) => ({
        url: '/releaseConveyorControl',
        method: 'POST',
        body,
      }),
    }),
    submitConveyorOperation: build.mutation({
      query: (body) => ({
        url: '/conveyorOperation',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useGetAllConveyorStatusQuery,
  useAcquireConveyorControlMutation,
  useLazyGetAllConveyorStatusQuery,
  useResizeConveyorMutation,
  useReleaseConveyorControlMutation,
  useSubmitConveyorOperationMutation,
} = conveyorApi;