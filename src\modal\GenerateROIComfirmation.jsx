import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button } from 'antd';
import { useRegisterFeaturesMutation } from '../services/inference';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import _ from 'lodash';
import { useNavigate } from 'react-router-dom';


const GenerateROIComfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    parsedComponentInfo,
    parseRules,
    currentTranslation,
    currentRotation,
    curProduct,
    currentFileUri,
    horizontallyFlipped,
    verticallyFlipped,
  } = props;

  const navigate = useNavigate();

  const { t } = useTranslation();

  const [registerFeatures] = useRegisterFeaturesMutation();

  const handleSubmit = async (parseRules, currentTranslation, currentRotation, curProduct, currentFileUri, horizontallyFlipped) => {
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex', 0),
      data_row_end: _.get(parseRules, 'lastRowIndex', 1),
      unit_multiplier: _.get(parseRules, 'unitMutiplier', 'mm'),
      part_number_col: _.get(parseRules, 'partNumberCol', 0),
      package_number_col: _.get(parseRules, 'packageCol', 1),
      x_col: _.get(parseRules, 'xCol', 2),
      y_col: _.get(parseRules, 'yCol', 3),
      designator_col: _.get(parseRules, 'designatorCol', 4),
    };

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    // TODO: add support for flipped
    const res = await registerFeatures({
      file_path: currentFileUri,
      rules,
      product_id: Number(curProduct.product_id),
      tx: currentTranslation.x,
      ty: currentTranslation.y,
      rotation: currentRotation,
      flip_board: horizontallyFlipped,
    });

    if (res.error) {
      aoiAlert(t('notification.error.registerFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    navigate(`/teach?product-id=${curProduct.product_id}&from-upload-cad=true`);
  };

  return (
    <CustomModal
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>{t('existingComponentsFound.existingComponentsFound')}</span>}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      footer={null}
    >
      <div className='flex flex-col self-stretch'>
        <div className='flex py-6 px-4 flex-col self-stretch'>
          {/* <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('existingComponentsFound.existingComponentsDetectedOn')}
          </span>
          <h1 className='list-item font-source text-[14px] font-normal leading-[150%] text-gray-6 list-disc ml-[1rem]'>
            {t('existingComponentsFound.replaceAll')}
          </h1>
          <h1 className='list-item font-source text-[14px] font-normal leading-[150%] text-gray-6 list-disc ml-[1rem]'>
            {t('existingComponentsFound.onlyAddNew')}
          </h1> */}
          <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('existingComponentsFound.thisActionWillReplaceExistingComponents')}
          </span>
        </div>
        <div className='flex p-4 gap-2 flex-col self-stretch'>
          <Button
            onClick={() => {
              handleSubmit(parseRules, currentTranslation, currentRotation, curProduct, currentFileUri, horizontallyFlipped);
            }}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('existingComponentsFound.replaceAllExisting')}
            </span>
          </Button>
          {/* <Button>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('existingComponentsFound.onlyAddNewComponents')}
            </span>
          </Button> */}
        </div>
      </div>
    </CustomModal>
  )
};

export default GenerateROIComfirmation;