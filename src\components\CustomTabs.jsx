import React, { useState, useRef, useEffect } from 'react';
import { ConfigProvider } from 'antd';
import styled from 'styled-components';
import _ from 'lodash';

const TabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const TabsHeader = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: flex-end;
  border-bottom: 1px solid #4f4f4f;
  padding-bottom: 0;
  min-height: 42px;
`;

const TabItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.cardPadding || '10px 24px'};
  margin: ${props => props.horizontalMargin || '4px 16px 0 16px'};
  background: ${props => props.isActive ? '#ffffff0d' : '#1E1E1E'};
  border: 1px solid #4F4F4F;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.isActive ? '#fff' : '#fff'};
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 12px;
  font-weight: normal;
  line-height: 150%;
  white-space: nowrap;
  position: relative;
  outline: none;

  &:hover {
    color: #fff;
    background: ${props => props.isActive ? '#ffffff0d' : '#ffffff1a'};
  }

  &:focus {
    outline: 2px solid #56CCF2;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background: ${props => props.isActive ? '#ffffff0d' : '#1E1E1E'};
    }
  }

  ${props => props.isActive && `
    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 1px;
      background: #ffffff0d;
    }
  `}
`;

const TabContent = styled.div`
  flex: 1;
  padding: 16px 0;
`;

const CustomTabs = ({
  items = [],
  activeKey,
  onChange,
  type = 'card',
  size = 'default',
  style = {},
  className = '',
  tabBarStyle = {},
  children,
  // Custom styling props
  cardPadding,
  horizontalMargin,
  colorBgContainer,
  colorBorder,
  cardBg,
  itemSelectedColor,
  itemHoverColor,
  itemActiveColor,
  ...restProps
}) => {
  const [internalActiveKey, setInternalActiveKey] = useState(activeKey || _.get(items, '[0].key'));
  const tabsRef = useRef(null);

  const currentActiveKey = activeKey !== undefined ? activeKey : internalActiveKey;

  useEffect(() => {
    if (activeKey !== undefined) {
      setInternalActiveKey(activeKey);
    }
  }, [activeKey]);

  const handleTabClick = (key, disabled) => {
    if (disabled) return;

    if (activeKey === undefined) {
      setInternalActiveKey(key);
    }

    if (_.isFunction(onChange)) {
      onChange(key);
    }
  };

  const handleKeyDown = (event, key, disabled) => {
    if (disabled) return;

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTabClick(key, disabled);
    }
  };

  const getActiveTabContent = () => {
    const activeItem = _.find(items, item => item.key === currentActiveKey);
    return activeItem?.children || null;
  };

  // If using children prop (legacy support)
  if (children) {
    return (
      <ConfigProvider
        theme={{
          components: {
            Tabs: {
              cardPadding: cardPadding || '4px 8px',
              horizontalMargin: horizontalMargin || '4px 16px 0 16px',
              colorBgContainer: colorBgContainer || '#ffffff0d',
              colorBorder: colorBorder || '#4F4F4F',
              cardBg: cardBg || '#1E1E1E',
              itemSelectedColor: itemSelectedColor || '#fff',
              itemHoverColor: itemHoverColor || '#fff',
              itemActiveColor: itemActiveColor || '#fff',
            },
          }
        }}
      >
        {children}
      </ConfigProvider>
    );
  }

  return (
    <TabsContainer style={style} className={className} ref={tabsRef} {...restProps}>
      <TabsHeader style={tabBarStyle} data-testid="tabs-header">
        {_.map(items, (item) => (
          <TabItem
            key={item.key}
            isActive={item.key === currentActiveKey}
            onClick={() => handleTabClick(item.key, item.disabled)}
            onKeyDown={(event) => handleKeyDown(event, item.key, item.disabled)}
            disabled={item.disabled}
            cardPadding={cardPadding}
            horizontalMargin={horizontalMargin}
            title={item.title || (typeof item.label === 'string' ? item.label : '')}
            role="tab"
            tabIndex={item.disabled ? -1 : 0}
            aria-selected={item.key === currentActiveKey}
            aria-disabled={item.disabled}
            data-testid="tab-item"
          >
            {item.label}
          </TabItem>
        ))}
      </TabsHeader>
      {/* <TabContent role="tabpanel">
        {getActiveTabContent()}
      </TabContent> */}
    </TabsContainer>
  );
};

// Higher-order component for easy theming
export const CustomTabsWithTheme = ({
  theme = {},
  ...props
}) => {
  const defaultTheme = {
    cardPadding: '10px 24px',
    horizontalMargin: '4px 16px 0 16px',
    colorBgContainer: '#ffffff0d',
    colorBorder: '#4F4F4F',
    cardBg: '#1E1E1E',
    itemSelectedColor: '#fff',
    itemHoverColor: '#fff',
    itemActiveColor: '#fff',
  };

  const mergedTheme = { ...defaultTheme, ...theme };

  return <CustomTabs {...mergedTheme} {...props} />;
};

// Preset configurations similar to existing patterns in the codebase
export const SmallCustomTabs = (props) => (
  <CustomTabs
    cardPadding="2px 8px"
    horizontalMargin="4px 4px 0 4px"
    {...props}
  />
);

export const CompactCustomTabs = (props) => (
  <CustomTabs
    cardPadding="6px 12px"
    horizontalMargin="4px 4px 0 4px"
    {...props}
  />
);

export const LargeCustomTabs = (props) => (
  <CustomTabs
    cardPadding="6px 18px"
    horizontalMargin="4px 4px 0 4px"
    {...props}
  />
);

export default CustomTabs;
