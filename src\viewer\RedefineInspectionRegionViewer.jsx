import React, { useEffect, useRef } from 'react';


const RedefineInspectionREgionViewer = (props) => {
  const canvasElRef = useRef(null);
  const fcanvasRef = useRef(null);
  const viewerContRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      if (!fcanvasRef.current || !viewerContRef.current) return;

      fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
      fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(viewerContRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div className='relative w-full h-full'>
      {/* scene starts */}
      <div
        className='absolute top-0 left-0 w-full h-full z-[20]'
        ref={viewerContRef}
      >
        <canvas
          ref={canvasElRef}
        />
      </div>
    </div>
  );
};

export default RedefineInspectionREgionViewer;