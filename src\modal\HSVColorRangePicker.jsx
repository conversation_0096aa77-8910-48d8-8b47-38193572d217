import React, { useEffect, useRef } from 'react';
import iro from '@jaames/iro';
import _ from 'lodash';


const HSVColorRangePicker = (props) => {
  const {
    isOpened,
    setIsOpened,
    sh,
    ss,
    sv,
    eh,
    es,
    ev,
    ch,
    cs,
    cv,
    submit,
    noValueSlider = false,
  } = props;

  const size = 300;

  const colorPickerElRef = useRef(null);
  const colorPickerRef = useRef(null);
  const svgRef = useRef(null);

  useEffect(() => {
    if (!colorPickerElRef.current) return;

    if (colorPickerRef.current) {
      colorPickerElRef.current.querySelector('.IroColorPicker').remove();
      colorPickerRef.current = null;
      svgRef.current.innerHTML = "";
      return;
    }

    colorPickerRef.current = new iro.ColorPicker(colorPickerElRef.current, {
      width: size,
      colors: [
        `hsl(${sh}, ${ss}%, ${noValueSlider ? 100 : sv}%)`, // start
        `hsl(${eh}, ${es}%, ${noValueSlider ? 100 : ev}%)`, // end
        `hsl(${ch}, ${cs}%, ${noValueSlider ? 100 : cv}%)`, // center
      ],
      handleRadius: 9,
      borderWidth: 0,
      borderColor: "#fff",
      padding: 0,
      ...(noValueSlider
        ? {
            layout: [
              {
                component: iro.ui.Wheel,
              },
            ],
          }
        : {}),
      wheelAngle: -90,
      wheelDirection: "clockwise",
    });

    const redraw = () => {
      if (!svgRef.current) return;

      const svg = svgRef.current;
      svg.innerHTML = "";

      let isValid = true;

      colorPickerRef.current.colors.forEach(color => {
        const index = color.index;
        const hsv = color.hsv;

        if ((index === 0 || index === 1) && hsv.s !== 100) {
          isValid = false;
          color.saturation = 100;
        }
        if (noValueSlider && hsv.v !== 100) {
          color.value = 100;
        }
      });

      const pickerRect = colorPickerElRef.current.getBoundingClientRect();
      const r = size / 2;
      // const cx = r;
      // const cy = r;
      const cx = 150;
      const cy = 150;

      // get positions
      const pts = colorPickerRef.current.colors.map(c => {
        const hr = c.hsv.s === 100 ? r : r * (c.hsv.s / 100);
        const angle = ((c.hsv.h - 90) * Math.PI) / 180;
        return {
          x: cx + hr * Math.cos(angle),
          y: cy + hr * Math.sin(angle),
        };
      });
      const [start, end, center] = pts;

      const maskId = "wheelMask";
      const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs");
      const mask = document.createElementNS("http://www.w3.org/2000/svg", "mask");
      mask.setAttribute("id", maskId);

      const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      circle.setAttribute("cx", cx);
      circle.setAttribute("cy", cy);
      circle.setAttribute("r", r);
      circle.setAttribute("fill", "white");
      mask.appendChild(circle);

      const angleBetween = ((colorPickerRef.current.colors[1].hsv.h - colorPickerRef.current.colors[0].hsv.h + 360) % 360);
      const useLargeArc = angleBetween > 180 ? 1 : 0;

      const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
      const d = `
        M ${center.x},${center.y}
        L ${start.x},${start.y}
        A ${r},${r} 0 ${useLargeArc} 1 ${end.x},${end.y}
        Z
      `;
      path.setAttribute("d", d);
      path.setAttribute("fill", "black");
      mask.appendChild(path);

      defs.appendChild(mask);
      svg.appendChild(defs);

      const overlay = document.createElementNS("http://www.w3.org/2000/svg", "rect");
      overlay.setAttribute("x", "0");
      overlay.setAttribute("y", "0");
      overlay.setAttribute("width", size.toString());
      overlay.setAttribute("height", size.toString());
      overlay.setAttribute("fill", "#000");
      overlay.setAttribute("fill-opacity", "0.5");
      overlay.setAttribute("mask", `url(#${maskId})`);
      svg.appendChild(overlay);

      if (!isValid) return;
    };

    const delayUpdateColor = _.debounce(redraw, 0);

    colorPickerRef.current.on([
      "mount",
      "color:change",
    ], delayUpdateColor);

    const handleColorChangeEnd = _.debounce(() => {
      submit(
        colorPickerRef.current.colors[0].hsv.h,
        colorPickerRef.current.colors[0].hsv.s,
        noValueSlider ? 100 : colorPickerRef.current.colors[0].hsv.v,
        colorPickerRef.current.colors[1].hsv.h,
        colorPickerRef.current.colors[1].hsv.s,
        noValueSlider ? 100 : colorPickerRef.current.colors[1].hsv.v,
        colorPickerRef.current.colors[2].hsv.h,
        colorPickerRef.current.colors[2].hsv.s,
        noValueSlider ? 100 : colorPickerRef.current.colors[2].hsv.v,
      );
    }, 300);

    colorPickerRef.current.on("input:end", handleColorChangeEnd);
  }, [isOpened]);

  useEffect(() => {
    if (!colorPickerRef.current || !colorPickerElRef.current) return;

    colorPickerRef.current.colors[0].hsv = {
      h: sh,
      s: ss,
      v: noValueSlider ? 100 : sv,
    };
    colorPickerRef.current.colors[1].hsv = {
      h: eh,
      s: es,
      v: noValueSlider ? 100 : ev,
    };
    colorPickerRef.current.colors[2].hsv = {
      h: ch,
      s: cs,
      v: noValueSlider ? 100 : cv,
    };
  }, [
    sh,
    ss,
    sv,
    eh,
    es,
    ev,
    ch,
    cs,
    cv,
  ]);

	return (
      <div className='flex flex-1 items-center justify-center self-stretch flex-col p-4 gap-2 bg-transparent'>
        <div className={`relative w-[${size}px] h-[${size}px]`}>
          <div ref={colorPickerElRef} />
          <svg
            ref={svgRef}
            width={size}
            height={size}
            style={{ position: "absolute", top: 0, left: 0, pointerEvents: "none" }}
          />
        </div>
      </div>
	);
};

export default HSVColorRangePicker;