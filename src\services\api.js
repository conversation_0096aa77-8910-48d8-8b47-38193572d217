import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { COMMON_HTTP_CODE, localStorageKeys, serverHost } from '../common/const';
import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { text } from '../common/translation';
import i18n from '../i18n';


export const baseDevQuery = async (args, WebApi, extraOptions) => {
  // const localBaseUrl = sessionStorage.getItem('baseUrl') || null;

  const baseUrl =
    (() => {
      const baseUrl = serverHost;
      sessionStorage.setItem('baseUrl', baseUrl);
      return baseUrl;
    })();
  const rawBaseQuery = fetchBaseQuery({
    baseUrl: baseUrl,
    prepareHeaders: (headers) => {
      if (!headers.get('Authorization') && localStorage.getItem(localStorageKeys.accessToken)) {
        const token = localStorage.getItem(localStorageKeys.accessToken);
        headers.set('Authorization', token);
      }
    },
    // set headers
    // prepareHeaders: (headers) => {
    //   if (args.headers) {
    //     Object.keys(args.headers).forEach((key) => {
    //       headers.set(key, args.headers[key]);
    //     });
    //   }
    // },
    responseHandler: async (response) => {
      if (response.status === COMMON_HTTP_CODE.forbidden) {
        // Handle unauthorized access
        localStorage.removeItem(localStorageKeys.accessToken);
        // window.location.href = '/login';
        aoiAlert(_.get(text, `common.permissionRestrictedOrLoginHasExpired.${i18n.language}`), ALERT_TYPES.COMMON_ERROR);
        return;
      }
      const text = await response.text()
      return text.length ? JSON.parse(text) : null
    },
    timeout: 60000 * 3, // 60 seconds * 3(3 minutes)
  });
  return rawBaseQuery(args, WebApi, extraOptions);
};

export const baseQuery = baseDevQuery;