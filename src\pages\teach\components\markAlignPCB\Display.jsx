import React, { useEffect, useState } from 'react';
import { CustomSegmented } from '../../../../common/styledComponent';
import AlignPCBViewer from '../../../../viewer/AlignPCBViewer';
import _ from 'lodash';


const Display = (props) => {
  const {
    curProduct,
    selectedTool,
    setSelectedTool,
    refetchMarkers,
    selectedLocateFid,
    setSelectedLocateFid,
    selectedEditFid,
    setSelectedEditFid,
    markerFeatures,
    shapeChangeUpdate,
    featureIdsToDisplayCenterDot,
  } = props;

  return (
    <div className='relative w-full h-full'>
      <div className='absolute z-[10] w-full h-full'>
        <AlignPCBViewer
          imageUri={_.get(curProduct, 'inspectables[0].color_map_uri')}
          depthUri={_.get(curProduct, 'inspectables[0].depth_map_uri')}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          curProduct={curProduct}
          refetchMarkers={refetchMarkers}
          selectedLocateFid={selectedLocateFid}
          setSelectedLocateFid={setSelectedLocateFid}
          selectedEditFid={selectedEditFid}
          setSelectedEditFid={setSelectedEditFid}
          markerFeatures={markerFeatures}
          shapeChangeUpdate={shapeChangeUpdate}
          featureIdsToDisplayCenterDot={featureIdsToDisplayCenterDot}
        />
      </div>
      {/* <div className='absolute top-[50%] right-[8px] z-[11]'>
        <CustomSegmented
          style={{ width: '36px' }}
          vertical
          value={selectedTool}
          onChange={(value) => setSelectedTool(value)}
          options={[
            {
              value: 'transform',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/backHand_white.svg'
                  alt=''
                  className='w-4 h-4'
                />
              </div>,
            },
            {
              value: 'select3DArea',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/squareDot_white.svg'
                  alt='navigator'
                  className='w-3 h-3'
                />
              </div>,
              disabled: !_.isUndefined(markerFeatures) && markerFeatures.length === 2,
            },
          ]}
        />
      </div> */}
    </div>
  );
};

export default Display;