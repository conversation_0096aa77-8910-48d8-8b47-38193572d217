import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { applyTemplateString } from '../common/util';

const DeletePrivateTemplateConfirmation = (props) => {
  const { isOpened, setIsOpened, templateName, onConfirm } = props;
  const { t } = useTranslation();

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('common.deleteTemplate')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col gap-4 p-4'>
        <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
          {applyTemplateString(t('common.thisActionWillDeleteTemplate'), { templateName })}
        </span>
        <div className='flex gap-2 items-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => {
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              onConfirm();
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.delete')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default DeletePrivateTemplateConfirmation;
