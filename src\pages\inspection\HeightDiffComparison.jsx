import React, { useEffect, useRef } from 'react';
import _ from 'lodash';
import { debounce } from 'lodash';
import { loadAndDecodePoints } from '../../viewer/util';
import InferenceCompareViewer from '../../viewer/InferenceCompareViewer';
import { backendAutoGenTimeToDisplayString } from '../../common/util';
import { highResoluCroppedDisplayMegaPixelCount, serverHost } from '../../common/const';
import { HiddenCanvasDimensionCalcDiv, ThreeDDisplayWrapper } from '../../common/styledComponent';
import { useTranslation } from 'react-i18next';


const HeightDiffComparison = (props) => {
  const {
    // goldenCloudUri,
    // ipcCloudUri,
    goldenComponentInfo,
    ipcComponentInfo,
    // inspectionInfo,
    goldenFeatureInfo,
    ipcFeatureInfo,
    goldenProduct,
    isIpcCloudVisible,
    isGoldenCloudVisible,
    pointCloudDisplayedView,
  } = props;

  const { t } = useTranslation();

  const goldenCanvasDimensionCalcDivRef = useRef(null);
  const ipcCanvasDimensionCalcDivRef = useRef(null);
  const goldenCanvasRef = useRef(null);
  const ipcCanvasRef = useRef(null);
  const goldenViewerRef = useRef(null);
  const ipcViewerRef = useRef(null);
  const isSyncing = useRef(false);

  const syncGoldenViewerCam = (camera, trackball) => {
    if (!ipcViewerRef.current || !goldenViewerRef.current || isSyncing.current) return;
    isSyncing.current = true;
    goldenViewerRef.current.updateCameraPose(camera, trackball);
    isSyncing.current = false;
  };

  const syncIpcViewerCam = (camera, trackball) => {
    if (!ipcViewerRef.current || !goldenViewerRef.current || isSyncing.current) return;
    isSyncing.current = true;
    ipcViewerRef.current.updateCameraPose(camera, trackball);
    isSyncing.current = false;
  };

  const loadScene = async (
    viewer,
    isGolden,
    ipcFeatureInfo,
    goldenFeatureInfo,
    goldenComponentInfo,
  ) => {
    if (!viewer || !ipcFeatureInfo || !goldenFeatureInfo) return;

    // const { positions, colors } = await loadAndDecodePoints(`${serverEndpoint}/data?data_uri=${uri}`);
    let url = `${serverHost}/blob?type=point_cloud`;
    url += `&color_uri=${isGolden ? encodeURIComponent(_.get(goldenComponentInfo, 'color_map_uri', '')) : encodeURIComponent(_.get(ipcFeatureInfo, 'component_color_map_uri', ''))}`;
    url += `&depth_uri=${isGolden ? encodeURIComponent(_.get(goldenComponentInfo, 'depth_map_uri', '')) : encodeURIComponent(_.get(ipcFeatureInfo, 'component_depth_map_uri', ''))}`;
    url += `&x_min=${isGolden ? _.get(goldenFeatureInfo, 'roi.points[0].x', 0) - _.get(goldenComponentInfo, 'shape.points[0].x', 0) : _.get(ipcFeatureInfo, 'roi.points[0].x', 0)}`;
    url += `&y_min=${isGolden ? _.get(goldenFeatureInfo, 'roi.points[0].y', 0) - _.get(goldenComponentInfo, 'shape.points[0].y', 0) : _.get(ipcFeatureInfo, 'roi.points[0].y', 0)}`;
    url += `&x_max=${isGolden ? _.get(goldenFeatureInfo, 'roi.points[1].x', 0) - _.get(goldenComponentInfo, 'shape.points[0].x', 0) : _.get(ipcFeatureInfo, 'roi.points[1].x', 0)}`;
    url += `&y_max=${isGolden ? _.get(goldenFeatureInfo, 'roi.points[1].y', 0) - _.get(goldenComponentInfo, 'shape.points[0].y', 0) : _.get(ipcFeatureInfo, 'roi.points[1].y', 0)}`;
    if (_.isNumber(_.get(goldenFeatureInfo, 'roi.angle', null)) && isGolden) url += `&angle=${_.get(goldenFeatureInfo, 'roi.angle', 0)}`;
    if (_.isNumber(_.get(ipcFeatureInfo, 'roi.angle', null)) && !isGolden) url += `&angle=${_.get(ipcFeatureInfo, 'roi.angle', 0)}`;
    url += `&max_megapixel=${highResoluCroppedDisplayMegaPixelCount}`;
    url += `&t=${Date.now().valueOf()}`; // to prevent cache
    
    const {
      positions,
      colors,
    } = await loadAndDecodePoints(url);

    viewer.clearScene();

    viewer.loadScene({
      positions,
      colors,
      isGolden,
    });
  };

  useEffect(() => {
    if (!ipcViewerRef.current || !goldenViewerRef.current) return;
    ipcViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    goldenViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
  }, [pointCloudDisplayedView]);

  useEffect(() => {
    if (!ipcViewerRef.current || !goldenViewerRef.current) return;
    ipcViewerRef.current.updateCloudVisibility(isIpcCloudVisible);
    goldenViewerRef.current.updateCloudVisibility(isGoldenCloudVisible);
  }, [
    isGoldenCloudVisible,
    isIpcCloudVisible,
  ]);

  useEffect(() => {
    if (!_.isEmpty(goldenFeatureInfo) && goldenViewerRef.current && !_.isEmpty(ipcFeatureInfo)) {
      loadScene(goldenViewerRef.current, true, ipcFeatureInfo, goldenFeatureInfo, goldenComponentInfo);
      goldenViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    }
  }, [goldenFeatureInfo, goldenComponentInfo]);

  useEffect(() => {
    if (!_.isEmpty(ipcFeatureInfo) && ipcViewerRef.current && !_.isEmpty(goldenFeatureInfo)) {
      loadScene(ipcViewerRef.current, false, ipcFeatureInfo, goldenFeatureInfo, goldenComponentInfo);
      ipcViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    }
  }, [ipcFeatureInfo, goldenComponentInfo]);

  useEffect(() => {
    if (!goldenCanvasRef.current || !goldenCanvasDimensionCalcDivRef.current || !ipcCanvasRef.current || !ipcCanvasDimensionCalcDivRef.current) return;

    const goldenViewer = new InferenceCompareViewer(
      goldenCanvasRef.current,
      goldenCanvasDimensionCalcDivRef.current.offsetHeight,
      goldenCanvasDimensionCalcDivRef.current.offsetWidth,
      (c, t) => syncIpcViewerCam(c, t),
    );

    const ipcViewer = new InferenceCompareViewer(
      ipcCanvasRef.current,
      ipcCanvasDimensionCalcDivRef.current.offsetHeight,
      ipcCanvasDimensionCalcDivRef.current.offsetWidth,
      (c, t) => syncGoldenViewerCam(c, t),
    );

    goldenViewerRef.current = goldenViewer;
    ipcViewerRef.current = ipcViewer;

    const updateCanvasDimension = () => {
      if (!goldenViewer || !ipcViewer || !goldenCanvasDimensionCalcDivRef.current || !ipcCanvasDimensionCalcDivRef.current) return;
      goldenViewer.updateSceneSize(goldenCanvasDimensionCalcDivRef.current.offsetWidth, goldenCanvasDimensionCalcDivRef.current.offsetHeight);
      ipcViewer.updateSceneSize(ipcCanvasDimensionCalcDivRef.current.offsetWidth, ipcCanvasDimensionCalcDivRef.current.offsetHeight);
    };

    const debounceUpdateCanvasDimension = debounce(updateCanvasDimension, 300);

    window.addEventListener('resize', debounceUpdateCanvasDimension);

    if (!_.isEmpty(goldenFeatureInfo)) {
      loadScene(goldenViewer, true, ipcFeatureInfo, goldenFeatureInfo, goldenComponentInfo);
      goldenViewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    if (!_.isEmpty(ipcFeatureInfo)) {
      loadScene(ipcViewer, false, ipcFeatureInfo, goldenFeatureInfo, goldenComponentInfo);
      ipcViewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    return () => {
      if (goldenViewer) {
        goldenViewer.clearScene();
        goldenViewer.destroy();
        goldenViewerRef.current = null;
      }

      if (ipcViewer) {
        ipcViewer.clearScene();
        ipcViewer.destroy();
        ipcViewerRef.current = null;
      }
      
      window.removeEventListener('resize', debounceUpdateCanvasDimension);
    };
  }, []);

  return (
    <div className='flex w-full h-full gap-2'>
      <div className='flex flex-col gap-2 w-[50%] h-full'>
        <div className='flex py-1 justify-between items-center self-stretch'>
          <span className='font-source text-[12px] font-semibold'>
            {t('review.sample')}
          </span>
          <div className='flex py-1 px-2 gap-1 justify-center items-center'>
            <div className='flex w-[14px] h-[14px] p-[1px] justify-center items-center gap-2.5'>
              <img src='/icn/icn_calendar_white.svg' className='w-2.5 h-2.5 shrink' alt='calendar' />
            </div>
            <span className='font-source text-[10px] font-normal'>
              {_.get(ipcComponentInfo, 'created_at', '') ? backendAutoGenTimeToDisplayString(_.get(ipcComponentInfo, 'created_at', '')) : ''}
            </span>
          </div>
        </div>
        <div className='relative w-full h-full'>
          <ThreeDDisplayWrapper>
            <canvas ref={ipcCanvasRef} />
          </ThreeDDisplayWrapper>
          <HiddenCanvasDimensionCalcDiv ref={ipcCanvasDimensionCalcDivRef} />
        </div>
      </div>
      <div className='flex flex-col gap-2 w-[50%] h-full'>
        <div className='flex py-1 justify-between items-center self-stretch'>
          <span className='font-source text-[12px] font-semibold'>
            {t('review.golden')}
          </span>
          <div className='flex py-1 px-2 gap-1 justify-center items-center'>
            <div className='flex w-[14px] h-[14px] p-[1px] justify-center items-center gap-2.5'>
              <img src='/icn/icn_calendar_white.svg' className='w-2.5 h-2.5 shrink' alt='calendar' />
            </div>
            <span className='font-source text-[10px] font-normal'>
              {backendAutoGenTimeToDisplayString(_.get(goldenProduct, 'last_modified', ''))}
            </span>
          </div>
        </div>
        <div className='relative w-full h-full'>
          <ThreeDDisplayWrapper>
            <canvas ref={goldenCanvasRef} />
          </ThreeDDisplayWrapper>
          <HiddenCanvasDimensionCalcDiv ref={goldenCanvasDimensionCalcDivRef} />
        </div>
      </div>
    </div>
  )
};

export default HeightDiffComparison;