import _ from 'lodash';
import ThreeDBaseViewer from './ThreeDBaseViewer';
import * as THREE from 'three';
import { detachTransformControlsFromObject, disposePointCloud, generatePCBasedOnModes } from './util';


export default class CroppedPointCloudViewer extends ThreeDBaseViewer {
  constructor(canvasRef, elementHeight, elementWidth, getCanvasHeightWidth, updateZMinMax) {
    super(canvasRef, elementHeight, elementWidth, getCanvasHeightWidth);
    this.scene = super.getScene();
    this.trackball = super.getTrackballControls();
    this.camera = super.getCamera();
    this.renderer = super.getRenderer();
    this.sceneCloudId = null;
    this.cloudCenter = null;
    this.updateZMinMax = updateZMinMax;
    this.scenedInited = false;
    this.transformControl = null;
    this.twoDDimension = null;
  };

  loadScene = ({
    pointNColor,
    mode: mode,
    zRoiMode: zRoiMode,
    zRoiMin: zRoiMin,
    zRoiMax: zRoiMax,
    twoDDimension,
  }) => {
    const {
      positions: newPositions,
      colors: newColors,
      colorsModified,
      center,
      zMin,
      zMax,
    } = generatePCBasedOnModes({
      pointNColor: pointNColor,
      mode: mode,
      zRoiMode: zRoiMode,
      zRoiMin: zRoiMin,
      zRoiMax: zRoiMax,
    });

    // start timer
    // console.log(Date.now().valueOf(), 'start load scene');
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));
    // geometry.setAttribute('color', new THREE.Uint8BufferAttribute(newColors, 3));
    if (!colorsModified) {
      geometry.setAttribute('color', new THREE.Uint8BufferAttribute(newColors, 3));
      geometry.attributes.color.normalized = true;
    } else {
      geometry.setAttribute('color', new THREE.Float32BufferAttribute(newColors, 3));
    }
    // geometry.attributes.color.normalized = true;

    // get cloud position center
    // const center = new THREE.Vector3();
    geometry.computeBoundingBox();
    // geometry.boundingBox.getCenter(center);
    this.cloudCenter = center;

    if (this.updateZMinMax && !this.scenedInited) {
      // get z max and z min
      this.updateZMinMax(zMin, zMax);
      this.scenedInited = true;
    }

    const material = new THREE.ShaderMaterial({
      uniforms: {
        size: { value: 0.3 },
      },
      vertexShader: `
        uniform float size;
        varying vec3 vColor;
        
        void main() {
            vColor = color.rgb;
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * (90.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
  
        void main() {
          gl_FragColor = vec4(vColor, 1.0);
        }
      `,
      transparent: true,
      vertexColors: true,
    });

    const cloud = new THREE.Points(geometry, material);
    
    this.scene.add(cloud);

    const referenceSphere = new THREE.SphereGeometry(1, 10, 10);
    const referenceSphereMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    const referenceSphereMesh = new THREE.Mesh(referenceSphere, referenceSphereMaterial);
    referenceSphereMesh.position.set(center.x, center.y, center.z);
    this.scene.add(referenceSphereMesh);
    referenceSphereMesh.attach(cloud);
    referenceSphereMesh.rotateX(Math.PI);
    referenceSphereMesh.rotateZ(Math.PI);
    
    // detach from reference sphere
    // and remove reference sphere
    cloud.updateMatrixWorld();
    const worldPosition = new THREE.Vector3();
    cloud.getWorldPosition(worldPosition);
    cloud.position.copy(worldPosition);
    const worldQuaternion = new THREE.Quaternion();
    cloud.getWorldQuaternion(worldQuaternion);
    cloud.quaternion.copy(worldQuaternion);

    cloud.clear();
    this.scene.add(cloud);
    this.scene.remove(referenceSphereMesh);
    // dispose reference sphere
    if (referenceSphereMesh.geometry) referenceSphereMesh.geometry.dispose();
    if (referenceSphereMesh.material) referenceSphereMesh.material.dispose();

    this.sceneCloudId = cloud.id;

    if (!this.twoDDimension) this.twoDDimension = twoDDimension;

    this.resetCamera();

    // this.trackball.target.set(center.x, center.y, center.z);

    // this.getCamera().lookAt(center.x, center.y, center.z);
    // this.trackball.update();
  };

  clearScene = () => {
    if (_.isInteger(this.sceneCloudId)) {
      if (this.transformControl) {
        this.scene.remove(this.transformControl);
        detachTransformControlsFromObject(this.transformControl);
        this.transformControl.dipose();
        this.transformControl = null;
      }
      disposePointCloud(this.scene, this.sceneCloudId);
      this.sceneCloudId = null;
    }
  };

  resetCamera = () => {
    if (this.twoDDimension) {
      // get z based on twoDDimension in order to display full point cloud
      const longSideLength = Math.max(this.twoDDimension.width, this.twoDDimension.height);
      const z = longSideLength / 32;
      this.currentCamera.position.set(this.cloudCenter.x, this.cloudCenter.y, z);
    } else {
      this.currentCamera.position.set(this.cloudCenter.x, this.cloudCenter.y, 60);
    }
    this.currentCamera.up.set(0, -1, 0);
    this.currentCamera.lookAt(this.cloudCenter.x, this.cloudCenter.y, this.cloudCenter.z);
    this.trackball.target.set(this.cloudCenter.x, this.cloudCenter.y, this.cloudCenter.z);
    this.trackball.update();
  };
};