import React, { Fragment, useEffect, useState } from 'react';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../../common/styledComponent';
import { Button, Input, InputNumber, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { conveyorUsage, fieldConstraints } from '../../../common/const';
import { useAcquireConveyorControlMutation, useGetAllConveyorStatusQuery, useLazyGetAllConveyorStatusQuery, useReleaseConveyorControlMutation, useResizeConveyorMutation } from '../../../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';
import _ from 'lodash';
import ConveyorInUse from '../../../modal/ConveyorInUse';
import { useDispatch, useSelector } from 'react-redux';
import { setCameraAccessToken, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsProgrammingUsingConveyor } from '../../../reducer/setting';
import { useUpdateProductMutation } from '../../../services/product';
import { useAcquireCameraControlMutation, useReleaseCameraControlMutation } from '../../../services/camera';
import ConveyorSelector from '../../../components/ConveyorSelector';
import { getCurrentConveyorStatus } from '../../../common/util';
import { useLazyGetInferenceStatusQuery } from '../../../services/inference';


const ConveyorSetup = (props) => {
  const {
    productId,
    refetchCurProduct,
    curProduct,
    setActiveTab,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [selectedConveyorSlot, setSelectedConveyorSlot] = useState(null);
  const [conveyorInUseOpened, setConveyorInUseOpened] = useState(false);
  const [conveyorWidth, setConveyorWidth] = useState(50);

  const [acquireConveyorControl] = useAcquireConveyorControlMutation();
  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [resizeConveyor] = useResizeConveyorMutation();
  const [releaseConveyorControl] = useReleaseConveyorControlMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [acquireCameraControl] = useAcquireCameraControlMutation();
  const [releaseCameraControl] = useReleaseCameraControlMutation();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);
  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);
  const currentControlledConveyorSlotId = useSelector((state) => state.setting.currentControlledConveyorSlotId);

  const handleSaveAndContinue = async (conveyorWidth, productId) => {
    if (!_.isNumber(conveyorWidth) || conveyorWidth <= 0) {
      aoiAlert(t('notification.error.invalidConveyorWidth'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await updateProduct({
      product_id: Number(productId),
      conveyor_width_mm: conveyorWidth,
    });

    if (res.error) {
      aoiAlert(t('notification.error.updateProduct'), ALERT_TYPES.COMMON_ERROR);
      console.error('updateProduct error:', _.get(res, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.updateProduct'), ALERT_TYPES.COMMON_INFO);
    await refetchCurProduct();

    setActiveTab('PCBDimension');
  };

  const handleReleaseConveyorSubmit = async (selectedConveyorSlot, conveyorToken, cameraToken) => {
    if (_.isEmpty(selectedConveyorSlot)) return;

    // check if it's already released
    const conveStatusRes = await lazyGetConveyorStatus();

    if (conveStatusRes.error) {
      aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
      console.error('getAllConveyorStatus error:', _.get(conveStatusRes, 'error.message', ''));
      return;
    }

    if (_.get(conveStatusRes, `data.conveyor_map.${selectedConveyorSlot}.available`, false)) {
      aoiAlert(t('notification.error.conveyorAlreadyReleased'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await releaseConveyorControl(conveyorToken);

    if (res.error) {
      aoiAlert(t('notification.error.releaseConveyorControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('releaseConveyorControl error:', _.get(res, 'error.message', ''));
      return;
    }

    const releaseCameraRes = await releaseCameraControl(cameraToken);

    if (releaseCameraRes.error) {
      aoiAlert(t('notification.error.releaseCameraControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('releaseCameraControl error:', _.get(releaseCameraRes, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.releaseConveyorControl'), ALERT_TYPES.COMMON_INFO);
    dispatch(setConveyorAccessToken(''));
    // setSelectedConveyorSlot('');
    dispatch(setIsProgrammingUsingConveyor(false));
    dispatch(setCurrentControlledConveyorSlotId(null));
    dispatch(setCameraAccessToken(''));
  };

  const handleResizeSubmit = async (conveyorWidth, conveyorAccessToken) => {
    if (_.isEmpty(conveyorAccessToken)) return;

    if (!_.isNumber(conveyorWidth) || conveyorWidth <= 0) {
      aoiAlert(t('notification.error.invalidConveyorWidth'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await resizeConveyor({
      conveyor_access_token: conveyorAccessToken,
      width_mm: conveyorWidth,
    });

    if (res.error) {
      aoiAlert(t('notification.error.resizeConveyor'), ALERT_TYPES.COMMON_ERROR);
      console.error('resizeConveyor error:', _.get(res, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.resizeConveyor'), ALERT_TYPES.COMMON_INFO);
    await refetchCurProduct();
  };

  const handleLockConveyorSubmit = async (selectedConveyorSlot, productId) => {
    if (_.isEmpty(selectedConveyorSlot)) {
      aoiAlert(t('notification.error.pleaseSelectAConveyor'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const conveyorStatus = await getCurrentConveyorStatus(
      lazyGetConveyorStatus,
      lazyGetInferenceStatus,
      t,
    );

    // if any conveyor is in use
    for(const slotId of _.keys(conveyorStatus)) {
      if (_.get(conveyorStatus, `${slotId}.taskType`, '') !== 'none') {
        aoiAlert(t('notification.error.conveyorInUsedWhenTeach'), ALERT_TYPES.COMMON_ERROR);
        return;
      }
    }
    
    const res = await acquireConveyorControl({
      slot_id: Number(selectedConveyorSlot),
      product_id: Number(productId),
      intension: conveyorUsage.program,
    });

    if (res.error) {
      aoiAlert(t('notification.error.acquireConveyorControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('acquireConveyorControl error:', _.get(res, 'error.message', ''));
      return;
    }

    dispatch(setConveyorAccessToken(_.get(res, 'data.conveyor_access_token', '')));
    dispatch(setIsProgrammingUsingConveyor(true));
    dispatch(setCurrentControlledConveyorSlotId(selectedConveyorSlot));

    const acquireCameraRes = await acquireCameraControl({
      slot_id: Number(selectedConveyorSlot),
      // slot_id: 1,
      conveyor_access_token: _.get(res, 'data.conveyor_access_token', ''),
    });

    if (res.error) {
      aoiAlert(t('notification.error.acquireCameraControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('acquireCameraControl error:', _.get(acquireCameraRes, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.acquireConveyorControl'), ALERT_TYPES.COMMON_INFO);
    dispatch(setCameraAccessToken(_.get(acquireCameraRes, 'data.camera_access_token', '')));
  };

  useEffect(() => {
    setConveyorWidth(_.get(curProduct, 'conveyor_width_mm', 50));
  }, [curProduct]);

  return (
    <Fragment>
      <ConveyorInUse
        isOpened={conveyorInUseOpened}
        setIsOpened={setConveyorInUseOpened}
      />
      <div className='flex w-[320px] flex-col self-stretch gap-0.5 bg-[#ffffff0d]'>
        <div className='flex flex-col p-4 self-stretch gap-4'>
          <div className='flex flex-col'>
            <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
              {t('productDefine.conveyorSetup')}:
            </span>
            <div className='flex flex-wrap items-center py-2'>
              <span className='font-source text-[12px] font-normal leading-[150%] text-gray-6 flex-wrap'>
                {t('productDefine.conveyorSetupDescPart1')}
                <img
                  src='/icn/pcbIn_color.svg'
                  alt='pcbIn'
                  className='w-[22px] h-[14px] inline-block vertical-middle px-1'
                />
                {t('productDefine.conveyorSetupDescPart2')}
              </span>
            </div>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex h-[26px] items-center justify-between py-1 self-stretch'>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.selectAConveyor')}
              </span>
            </div>
            <ConveyorSelector
              disabled={!_.isEmpty(conveyorAccessToken)}
              selectedConveyorSlotId={selectedConveyorSlot}
              setSelectedConveyorSlotId={setSelectedConveyorSlot}
            />
            <GreenPrimaryButtonConfigProvider>
              <Button
                type='primary'
                onClick={() => {
                  handleLockConveyorSubmit(selectedConveyorSlot, productId);
                }}
              >
                <div className='flex items-center gap-2'>
                  <img src='/icn/locked_black.svg' alt='locked' className='w-[9px] h-[12px]' />
                  <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                    {t('productDefine.lockConveyorForThisTask')}
                  </span> 
                </div>
              </Button>
            </GreenPrimaryButtonConfigProvider>
          </div>
          <div className='flex flex-col gap-1 self-stretch'>
            <div className='flex py-1 items-center self-stretch'>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.setConveyorWidth')}
              </span>
            </div>
            <div className='flex py-1 gap-2 items-center self-stretch justify-between'>
              <InputNumber
                style={{ width: '50%' }}
                controls={false}
                min={fieldConstraints.productDefine.conveyorWidth.min}
                max={fieldConstraints.productDefine.conveyorWidth.max}
                value={conveyorWidth}
                onChange={(v) => setConveyorWidth(v)}
                step={1}
              />
              <GreenDefaultButtonConfigProvider>
                <Button
                  disabled={_.isEmpty(conveyorAccessToken)}
                  style={{ width: '50%' }}
                  onClick={() => {
                    handleResizeSubmit(conveyorWidth, conveyorAccessToken);
                  }}
                >
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('productDefine.moveToWidth')}
                  </span>
                </Button>
              </GreenDefaultButtonConfigProvider>
            </div>
            <div className='flex items-center justify-center'>
              <span className='font-source text-[12px] font-normal leading-[150%] italic'>
                {`${t('productDefine.currentConveyorWidth')}: ${_.isInteger(_.get(curProduct, 'conveyor_width_mm', 0)) ? `${_.get(curProduct, 'conveyor_width_mm', 0)}mm` : 'N/A'}`}
              </span>
            </div>
          </div>
        </div>
        <div className='flex flex-col py-8 px-2 gap-6 self-stretch'>
          <GreenPrimaryButtonConfigProvider>
            <Button
              type='primary'
              style={{ width: '100%' }}
              onClick={() => {
                handleSaveAndContinue(conveyorWidth, productId);
              }}
              disabled={_.isEmpty(conveyorAccessToken) || _.isEmpty(cameraAccessToken)}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('productDefine.saveAndContinue')}
              </span>
            </Button>
          </GreenPrimaryButtonConfigProvider>
          <GreenDefaultButtonConfigProvider>
            <Button
              style={{ width: '100%' }}
              onClick={() => {
                handleReleaseConveyorSubmit(currentControlledConveyorSlotId, conveyorAccessToken, cameraAccessToken);
              }}
              disabled={_.isEmpty(conveyorAccessToken)}
            >
              <div className='flex items-center gap-2'>
                <img src='/icn/unlock_white.svg' alt='unlock' className='w-[9px] h-[12px]' />
                <span className='font-source text-[12px] font-normal leading-[normal] pt-0.5'>
                  {t('productDefine.releaseConveyor')}
                </span>
              </div>
            </Button>
          </GreenDefaultButtonConfigProvider>
        </div>
      </div>
    </Fragment>
  )
};

export default ConveyorSetup;