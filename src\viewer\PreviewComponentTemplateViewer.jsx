import React, { useEffect, useRef, useState } from 'react';
import { useComponentTemplateMapMutation } from '../services/product';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { newRectStrokeWidth } from '../common/const';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';


const PreviewComponentTemplateViewer = (props) => {
  const {
    templateInfo,
    curProduct,
  } = props;

  const { t } = useTranslation();
  
  const canvasElRef = useRef(null);
  const viewerContainerRef = useRef(null);
  const fcanvasRef = useRef(null);
  const displayedRects = useRef([]);
  const curRectsCenter = useRef(null);
  const curRectsDimension = useRef(null);

  const [physicalCoordMapping] = useComponentTemplateMapMutation();

  const resetView = () => {
    const canvasWidth = fcanvasRef.current.getWidth();
    const canvasHeight = fcanvasRef.current.getHeight();

    const zoom = Math.min(
      canvasWidth/ curRectsDimension.current.width,
      canvasHeight / curRectsDimension.current.height,
    ) * 0.5;

    fcanvasRef.current.zoomToPoint(curRectsCenter.current, zoom);

    const newRectCenter = fabric.util.transformPoint(curRectsCenter.current, fcanvasRef.current.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + fcanvasRef.current.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + fcanvasRef.current.viewportTransform[5];

    // Apply the pan adjustment
    fcanvasRef.current.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
  };  

  const loadTemplateRois = async (templateInfo, curProduct) => {
    if (_.isUndefined(templateInfo) || _.isEmpty(templateInfo)) return;
    if (_.isEmpty(_.get(templateInfo, 'model.canned_rois', []))) return;

    if (!fcanvasRef.current) return;

    if (!_.isEmpty(displayedRects.current)) {
      displayedRects.current.forEach((rect) => {
        fcanvasRef.current.remove(rect);
      });
      displayedRects.current = [];
    }

    const res = await physicalCoordMapping({
      depth_map_uri: _.get(curProduct, 'inspectables[0].depth_map_uri', ''),
      u: 0,
      v: 0,
      canned_rois: _.get(templateInfo, 'model.canned_rois', []),
    });

    if (res.error) {
      console.error('physicalCoordMapping error:', _.get(res, 'error.message', ''));
      aoiAlert(t('notification.error.mapComponentTemplateCoord'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    let minx = Infinity;
    let maxx = -Infinity;
    let miny = Infinity;
    let maxy = -Infinity;

    for (const r of _.get(res, 'data', [])) {
      minx = Math.min(minx, _.get(r, 'shape.points[0].x', 0));
      maxx = Math.max(maxx, _.get(r, 'shape.points[1].x', 0));
      miny = Math.min(miny, _.get(r, 'shape.points[0].y', 0));
      maxy = Math.max(maxy, _.get(r, 'shape.points[1].y', 0));
    
      const rect = new fabric.Rect({
        left: _.get(r, 'shape.points[0].x', 0),
        top: _.get(r, 'shape.points[0].y', 0),
        width: _.get(r, 'shape.points[1].x', 0) - _.get(r, 'shape.points[0].x', 0) + newRectStrokeWidth,
        height: _.get(r, 'shape.points[1].y', 0) - _.get(r, 'shape.points[0].y', 0) + newRectStrokeWidth,
        fill: 'transparent',
        stroke: '#57F2C4',
        strokeWidth: newRectStrokeWidth,
        selectable: false,
        evented: false,
      });

      rect.rotate(_.get(r, 'shape.angle', 0));

      rect.setControlsVisibility({
        hoverCursor: 'default',
      })

      fcanvasRef.current.add(rect);
      displayedRects.current.push(rect);
    }

    curRectsCenter.current = {
      x: (maxx - minx) / 2 + minx,
      y: (maxy - miny) / 2 + miny,
    };

    curRectsDimension.current = {
      width: Math.abs(maxx - minx),
      height: Math.abs(maxy - miny),
    };

    fcanvasRef.current.renderAll();
  };

  useEffect(() => {
    if (!fcanvasRef.current) return;
    if (_.isUndefined(templateInfo) || _.isUndefined(curProduct)) return;

    const reload = async (templateInfo, curProduct) => {
      await loadTemplateRois(templateInfo, curProduct);
      resetView();
    };

    reload(templateInfo, curProduct);
  }, [templateInfo, curProduct]);

  useEffect(() => {
    if (!canvasElRef.current || !viewerContainerRef.current) return;

    const fcanvas = new fabric.Canvas(canvasElRef.current, {
      antialias: 'off',
      uniformScaling: false,
      selection: false,
    });

    fcanvas.setWidth(viewerContainerRef.current.offsetWidth);
    fcanvas.setHeight(viewerContainerRef.current.offsetHeight);

    fcanvasRef.current = fcanvas;

    if (_.isUndefined(templateInfo) || _.isUndefined(curProduct)) return;

    const reload = async (templateInfo, curProduct) => {
      await loadTemplateRois(templateInfo, curProduct);
      resetView();
    };

    reload(templateInfo, curProduct);
  }, []);

  return (
    <div className='relative w-full h-full'>
      <div
        className='absolute top-0 left-0 w-full h-full z-[10]'
        ref={viewerContainerRef}
      >
        <canvas ref={canvasElRef} />
      </div>
    </div>
  );
};

export default PreviewComponentTemplateViewer;