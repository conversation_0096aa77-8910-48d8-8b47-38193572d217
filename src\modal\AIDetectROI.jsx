import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox, Collapse, ConfigProvider } from 'antd';
import styled from 'styled-components';


const AIDetectROI = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const { t } = useTranslation();

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[14px] font-semibold leading-[150%]'>
        {t('productDefine.aiDetectROI')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch'>
        <div className='flex py-6 px-4 flex-col gap-2 self-stretch'>
          <span className='font-source text-[14px] font-normal leading-[150%]'>
            {t('productDefine.selectInspectionTypes')}
          </span>
          <ConfigProvider
            theme={{
              components: {
                Collapse: {
                  headerBg: '#333',
                  contentBg: '#333',
                  headerPadding: '4px 0px',
                  contentPadding: '0 0',
                }
              }
            }}
          >
            <Collapse
              defaultActiveKey={['all']}
              items={[
                {
                  key: 'all',
                  label: <div className='flex gap-2 items-center slef-stretch'>
                    <Checkbox
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('common.all')}
                    </span>
                  </div>,
                  children: <div className='flex flex-col self-stretch flex-1'>
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    {/* body starts */}
                    <div className='flex self-stretch '>
                      <CustomCollapse
                        style={{ width: '100%' }}
                        defaultActiveKey={['body']}
                        items={[
                          {
                            key: 'body',
                            label: <div className='flex gap-2 items-center slef-stretch '>
                              <Checkbox
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              />
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.body')}
                              </span>
                            </div>,
                            children: <div className='flex flex-col self-stretch'>
                              <div className='flex flex-col self-stretch'>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox
                                    onClick={(e) => {
                                      e.stopPropagation();
                                    }}
                                  />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.twoD')}
                                  </span>
                                </div>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.threeD')}
                                  </span>
                                </div>
                              </div>
                            </div>
                          },
                        ]}
                      />
                    </div>
                    {/* body ends */}
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    {/* ic lead starts */}
                    <div className='flex self-stretch '>
                      <CustomCollapse
                        style={{ width: '100%' }}
                        defaultActiveKey={['ICLead']}
                        items={[
                          {
                            key: 'ICLead',
                            label: <div className='flex gap-2 items-center slef-stretch '>
                              <Checkbox
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              />
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.ICLead')}
                              </span>
                            </div>,
                            children: <div className='flex flex-col self-stretch'>
                              <div className='flex flex-col self-stretch'>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox
                                    onClick={(e) => {
                                      e.stopPropagation();
                                    }}
                                  />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.twoD')}
                                  </span>
                                </div>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.threeD')}
                                  </span>
                                </div>
                              </div>
                            </div>
                          },
                        ]}
                      />
                    </div>
                    {/* ic lead ends */}
                    {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
                    {/* ocr 2d starts */}
                    {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
                      <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
                      <div className='flex items-center gap-2 slef-stretch'>
                        <Checkbox
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('productDefine.OCR2D')}
                        </span>
                      </div>
                    </div> */}
                    {/* ocr 2d ends */}
                    {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
                    {/* qr 2d starts */}
                    {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
                      <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
                      <div className='flex items-center gap-2 slef-stretch'>
                        <Checkbox
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('productDefine.QR2D')}
                        </span>
                      </div>
                    </div> */}
                    {/* qr 2d ends */}
                    {/* <div className='w-full h-[1px] bg-[#4F4F4F]' /> */}
                    {/* text 2d starts */}
                    {/* <div className='pl-[18px] flex items-center gap-4 self-stretch py-1'>
                      <img src='/icn/arrowRightFilled_black.svg' alt='arrow' className='w-3 h-3 ' />
                      <div className='flex items-center gap-2 slef-stretch'>
                        <Checkbox
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {t('productDefine.Text2D')}
                        </span>
                      </div>
                    </div> */}
                    {/* text 2d ends */}
                    <div className='w-full h-[1px] bg-[#4F4F4F]' />
                    {/* solder starts */}
                    <div className='flex self-stretch '>
                      <CustomCollapse
                        style={{ width: '100%' }}
                        defaultActiveKey={['solder']}
                        items={[
                          {
                            key: 'solder',
                            label: <div className='flex gap-2 items-center slef-stretch '>
                              <Checkbox
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              />
                              <span className='font-source text-[12px] font-normal leading-[150%]'>
                                {t('productDefine.solder')}
                              </span>
                            </div>,
                            children: <div className='flex flex-col self-stretch'>
                              <div className='flex flex-col self-stretch'>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox
                                    onClick={(e) => {
                                      e.stopPropagation();
                                    }}
                                  />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.twoD')}
                                  </span>
                                </div>
                                <div className='w-full h-[1px] bg-[#4F4F4F]' />
                                <div className='pl-[48px] flex items-center gap-2 self-stretch py-1'>
                                  <Checkbox />
                                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                                    {t('common.threeD')}
                                  </span>
                                </div>
                              </div>
                            </div>
                          },
                        ]}
                      />
                    </div>
                    {/* solder ends */}
                  </div>
                }
              ]}
            />
          </ConfigProvider>
        </div>
        <div className='flex p-4 gap-2 items-center flex-1 self-stretch'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            style={{ width: '50%' }}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('productDefine.generateRoi')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  )
};

const CustomCollapse = styled(Collapse)`
  .ant-collapse-expand-icon {
    padding-left: 16px;
  }
`;

export default AIDetectROI;