import React, { Fragment, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Parse from './Parse';
import AlignCoord from './AlignCoord';
import ConfigROI from './ConfigROI';
import { useTranslation } from 'react-i18next';
import { Steps } from 'antd';
import _ from 'lodash';
import Upload from './Upload';
import { useGetProductByIdQuery } from '../../../../../services/product';


const UploadCAD = () => {
  const [searchParams] = useSearchParams();
  const productId = searchParams.get('product-id');

  const navigate = useNavigate();

  if (!_.isString(productId)) {
    navigate('/teach');
    return;
  }

  const { t } = useTranslation();

  const [currentStep, setCurrentStep] = useState(-1); // -1: upload  0: parse  1: align  2: configROI
  const [fileObj, setFileObj] = useState(null);
  const [currentFileUri, setCurrentFileUri] = useState('');
  const [parseRules, setParseRules] = useState({
    delimiter: ',',
    firstRowIndex: null,
    lastRowIndex: null,
    unitMutiplier: null,
    partNumberCol: undefined,
    packageCol: undefined,
    xCol: undefined,
    yCol: undefined,
    isIgnoreBotLayer: false,
    topLayerId: '',
    botLayerId: '',
    botLayerCol: undefined,
    isRotationEnabled: false,
    rotationCol: undefined,
    designatorCol: undefined,
  });
  const [parsedComponentInfo, setParsedComponentInfo] = useState([]);

  const [currentTranslation, setCurrentTranslation] = useState({
    x: 0,
    y: 0,
  });
  const [currentRotation, setCurrentRotation] = useState(0);
  const [horizontallyFlipped, setHorizontallyFlipped] = useState(false);
  const [verticallyFlipped, setVerticallyFlipped] = useState(false);

  const { data: curProduct } = useGetProductByIdQuery(productId);

  return (
    <div className='flex flex-1 self-stretch flex-col items-center gap-8'>
      {currentStep === -1 &&
        <Upload
          setCurrentFileUri={setCurrentFileUri}
          setFileObj={setFileObj}
          setCurrentStep={setCurrentStep}
          productId={productId}
          isInAutoProgramming={false}
        />
      }
      {currentStep !== -1 &&
        <Fragment>
          <div
            className='flex p-4 gap-4 self-stretch bg-[#ffffff0d] flex-col relative'
            style={{ boxShadow: '4px 4px 8px 0px rgba(0, 0, 0, 0.25)' }}
          >
            <img
              className='absolute right-4 top-4 cursor-pointer'
              src='/icn/close_white.svg'
              alt='close'
              onClick={() => navigate(`/teach${!_.isUndefined(productId) && `?product-id=${productId}`}`)}
            />
            <div className='flex flex-col gap-6 flex-1 items-center'>
              <span className='font-source text-[18px] font-normal leading-[150%] tracking-[0.54px]'>
                {t('productDefine.importPickAndPlaceFile')}
              </span>
              <div className='flex gap-4 justify-center items-center self-stretch px-[300px]'>
                <Steps
                  style={{ width: '100%' }}
                  current={currentStep}
                  items={[
                    {
                      title: t('productDefine.parseFile'),
                    },
                    {
                      title: t('productDefine.alignCoord'),
                    },
                    {
                      title: t('productDefine.configRois'),
                    }
                  ]}
                />
              </div>
            </div>
          </div>
          {currentStep === 0 && (
            <Parse
              fileObj={fileObj}
              currentFileUri={currentFileUri}
              parseRules={parseRules}
              setParseRules={setParseRules}
              setCurrentFileUri={setCurrentFileUri}
              setFileObj={setFileObj}
              productId={productId}
              setParsedComponentInfo={setParsedComponentInfo}
              setCurrentStep={setCurrentStep}
            />
          )}
          {currentStep === 1 && (
            <AlignCoord
              parsedComponentInfo={parsedComponentInfo}
              setCurrentStep={setCurrentStep}
              curProduct={curProduct}
              currentFileUri={currentFileUri}
              parseRules={parseRules}
              currentTranslation={currentTranslation}
              setCurrentTranslation={setCurrentTranslation}
              currentRotation={currentRotation}
              setCurrentRotation={setCurrentRotation}
              horizontallyFlipped={horizontallyFlipped}
              setHorizontallyFlipped={setHorizontallyFlipped}
              verticallyFlipped={verticallyFlipped}
              setVerticallyFlipped={setVerticallyFlipped}
            />
          )}
          {currentStep === 2 && (
            <ConfigROI
              setCurrentStep={setCurrentStep}
              parsedComponentInfo={parsedComponentInfo}
              parseRules={parseRules}
              currentTranslation={currentTranslation}
              currentRotation={currentRotation}
              curProduct={curProduct}
              currentFileUri={currentFileUri}
              horizontallyFlipped={horizontallyFlipped}
              verticallyFlipped={verticallyFlipped}
            />
          )}
        </Fragment>
      }
    </div>
  );
};

export default UploadCAD;