import React, { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { localStorageKeys, userRoles, conveyorNum, serverHost } from '../common/const';
import _ from 'lodash';
import CommonTable from '../components/CommonTable';
import { backendTimestampToDisplayString, convertBackendTimestampToMoment, getCurrentConveyorStatus } from '../common/util';
import { Button, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useCloneGoldenProductMutation, useGetAllProductsQuery, useLazyExportDefinitionsQuery } from '../services/product';
import { useGetAllSessionsQuery, useGetInferenceStatusQuery, useLazyGetInferenceStatusQuery } from '../services/inference';
import NewInspection from '../modal/NewInspection';
import { useLazyGetAllConveyorStatusQuery } from '../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import HomePageProductRename from '../modal/HomePageProductRename';
import RemovePCBProgram from '../modal/RemovePCBProgram';


const Home = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // const userRole = 'operator';
  const userRole = localStorage.getItem(localStorageKeys.userRole);

  const [curRunningTask, setCurRunningTask] = useState(conveyorNum);
  const [pcbTypeFilter, setPcbTypeFilter] = useState('');
  const [isNewInspectionOpened, setIsNewInspectionOpened] = useState(false);
  const [conveyorStatus, setConveyorStatus] = useState(null);
  const [isRenameProductModalOpened, setIsRenameProductModalOpened] = useState(false);
  const [renameTargetProduct, setRenameTargetProduct] = useState(null);
  const [isRemovePCBProgramModalOpened, setIsRemovePCBProgramModalOpened] = useState(false);
  const [removeProductTarget, setRemoveProductTarget] = useState(null);

  const { data: allProducts, isError: allProductsError, isLoading: allProductsLoading, refetch: refetchAllProducts, isFetching: allProductsFetching } = useGetAllProductsQuery();
  const { data: recentSessions, isLoading: sessionsLoading, isError: sessionsError, refetch: refetchRecntSessions, isFetching: recentSessionsFetching } = useGetAllSessionsQuery({ limit: 20, page: 0 });
  // const { data: conveyorStatus } = useGetAllConveyorStatusQuery();
  const { data: inferenceStatus } = useGetInferenceStatusQuery();
  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [cloneGoldenProduct] = useCloneGoldenProductMutation();
  const [exportDefinitions] = useLazyExportDefinitionsQuery();

  const exportAsFile = useCallback(async (productId) => {
    const res = await exportDefinitions({
      product_ids: productId,
    });

    if (res.error) {
      aoiAlert(t('notification.error.exportProductAsFile'), ALERT_TYPES.COMMON_ERROR);
      console.error('exportDefinitions error:', _.get(res, 'error.message', ''));
      return;
    }

    window.open(`${serverHost}/file?file_uri=${_.get(res, 'data.data_uri')}`, '_blank');
  }, [exportDefinitions, t]);

  const initConveyorStatus = useCallback(async () => {
    let status;
    try {
      status = await getCurrentConveyorStatus(
        lazyGetConveyorStatus,
        lazyGetInferenceStatus,
        t
      );
    } catch (e) {
      console.error('failed to get conveyor status', e);
    }

    setConveyorStatus(status);
  }, [lazyGetConveyorStatus, lazyGetInferenceStatus, t]);

  useEffect(() => {
    initConveyorStatus();
    refetchAllProducts();
    refetchRecntSessions();
  }, [initConveyorStatus, refetchAllProducts, refetchRecntSessions]);

  const handleCloneGoldenProduct = useCallback(async (productId, productName) => {
    const res = await cloneGoldenProduct({
      product_id: Number(productId),
      new_product_name: productName
     }).unwrap();
    refetchAllProducts();
  }, [cloneGoldenProduct, refetchAllProducts]);

  const filteredProducts = useMemo(() => {
    if (!allProducts) return [];
    if (!pcbTypeFilter.trim()) return allProducts;

    return allProducts.filter(product =>
      product.product_name.toLowerCase().includes(pcbTypeFilter.toLowerCase())
    );
  }, [allProducts, pcbTypeFilter]);

  const recentSessionsData = useMemo(() => {
    return _.isUndefined(recentSessions) ? [] : _.get(recentSessions, 'data');
  }, [recentSessions]);

  const conveyorItems = useMemo(() => {
    return _.times(curRunningTask, (i) => ({
      id: i,
      taskType: _.get(conveyorStatus, `${i}.taskType`, 'none'),
      sessionId: _.get(conveyorStatus, `${i}.info.session_id`, 0),
      goldenProductId: _.get(conveyorStatus, `${i}.info.golden_product_id`, 0),
      goldenId: _.get(conveyorStatus, `${i}.info.golden_id`, 0),
      productName: _.get(
        _.find(allProducts, p =>
          Number(p.product_id) === (
            _.get(conveyorStatus, `${i}.info.golden_product_id`, 0) ||
            _.get(conveyorStatus, `${i}.info.golden_id`, 0)
          )
        ),
        'product_name',
        'unknown product'
      )
    }));
  }, [curRunningTask, conveyorStatus, allProducts]);

  const ConveyorItem = useMemo(() => {
    return ({ item, navigate, t }) => (
      <div
        className='flex p-6 justify-between items-center self-stretch rounded-[4px] bg-[#57f2c433]'
        key={item.id}
      >
        <div className='flex items-center gap-4'>
          <img src='/icn/meter_white.svg' alt='meter' className='w-8 h-[25px]' />
          <div className='flex flex-col justify-center items-start'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {`Conveyor #${String.fromCharCode(65 + item.id)}`}
            </span>
            <span className='font-source text-[16px] font-semibold leading-[150%]'>
              {item.taskType === 'inspecting' &&
                `${t('home.runningInspectionTask')}: ${item.productName}`}
              {item.taskType === 'none' &&
                `${t('home.idle')}`}
              {item.taskType === 'programming' && item.goldenId > 0 &&
                `${t('home.definingPCB')}: ${item.productName}`}
            </span>
          </div>
        </div>
        <div
          className='flex w-6 h-6 items-center justify-center rounded-[4px] hover:bg-gray-2 cursor-pointer transition-all duration-300 ease-in-out'
          onClick={() => {
            if (item.taskType === 'inspecting') {
              navigate(`/inspection/live?running-session-id=${item.sessionId}&slot-id=${item.id}&golden-product-id=${item.goldenProductId}`);
            } else if (item.taskType === 'programming') {
              navigate(`/teach?product-id=${item.goldenId}`);
            }
          }}
        >
          <img src='/icn/arrowRight_green.svg' alt='arrow' className='w-[14px] h-[14px]' />
        </div>
      </div>
    );
  }, []);

  const totalProductsCount = useMemo(() => {
    return allProducts ? allProducts.length : 0;
  }, [allProducts]);

  const OperatorConveyorItem = useMemo(() => {
    return ({ item, navigate, t }) => (
      <div
        className='flex p-6 justify-between items-center self-stretch rounded-[4px] bg-[#57f2c433] flex-1'
        key={item.id}
      >
        <div className='flex items-center gap-4'>
          <img src='/icn/meter_white.svg' alt='meter' className='w-8 h-[25px]' />
          <div className='flex flex-col justify-center items-start'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {`Conveyor #${String.fromCharCode(65 + item.id)}`}
            </span>
            <span className='font-source text-[16px] font-semibold leading-[150%]'>
              {item.taskType === 'inspecting' &&
                `${t('home.runningInspectionTask')}: ${item.productName}`}
              {item.taskType === 'none' &&
                `${t('home.idle')}`}
              {item.taskType === 'programming' && item.goldenId > 0 &&
                `${t('home.definingPCB')}: ${item.productName}`}
            </span>
          </div>
        </div>
        <div
          className='flex w-6 h-6 items-center justify-center rounded-[4px] hover:bg-gray-2 cursor-pointer transition-all duration-300 ease-in-out'
          onClick={() => {
            if (item.taskType === 'inspecting') {
              navigate(`/inspection/live?running-session-id=${item.sessionId}&slot-id=${item.id}&golden-product-id=${item.goldenProductId}`);
            } else if (item.taskType === 'programming') {
              navigate(`/teach?product-id=${item.goldenId}`);
            }
          }}
        >
          <img src='/icn/arrowRight_green.svg' alt='arrow' className='w-[14px] h-[14px]' />
        </div>
      </div>
    );
  }, []);

  if (
    allProductsLoading ||
    sessionsLoading ||
    allProductsFetching ||
    allProductsError ||
    sessionsError ||
    recentSessionsFetching
  ) {
    return (
      <>
        {t('common.loading')}
      </>
    );
  }

  return (
    <Fragment>
      <RemovePCBProgram
        isOpened={isRemovePCBProgramModalOpened}
        setIsOpened={setIsRemovePCBProgramModalOpened}
        curProduct={removeProductTarget}
        refetchAllProducts={refetchAllProducts}
      />
      <NewInspection
        allProducts={allProducts}
        isOpened={isNewInspectionOpened}
        setIsOpened={setIsNewInspectionOpened}
      />
      <HomePageProductRename
        targetProduct={renameTargetProduct}
        isOpened={isRenameProductModalOpened}
        setIsOpened={setIsRenameProductModalOpened}
        refetchAllProducts={refetchAllProducts}
      />
      <div className='flex flex-col px-[140px] py-[48px] gap-4 flex-1 self-stretch bg-[#131313]'>
        {/* header btns */}
        <div className='flex self-stretch items-center gap-4 flex-wrap'>
          <div
            // style={{
            //   background: 'var(--AOI-green, linear-gradient(0deg, #57F2C4 0%, #57F2C4 100%), #81F499)'
            // }}
            style={{
              width: `calc((100vw - 280px - 32px) / 3)`,
            }}
            className={`flex p-6 items-center gap-4 rounded-[4px] transition-all duration-300 ease-in-out
              shadow-[0px 0px 12px 0px rgba(0, 0, 0, 0.25) inset] cursor-pointer bg-AOI-green hover:bg-AOI-green-hover`}
            onClick={() => setIsNewInspectionOpened(true)}
          >
            <div className='flex w-8 h-8 items-center justify-center rounded-[24px] bg-gray-1'>
              <img src='/icn/plusCircled_green.svg' alt='plus' className='w-4 h-4' />
            </div>
            <div className='flex flex-col justify-center items-start'>
              <span className='font-source text-[16px] font-semibold leading-[150%] text-gray-1'>
                {t('home.newInspectionTask')}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] text-gray-1'>
                {t('home.pickAPCB')}
              </span>
            </div>
          </div>
          { userRole !== userRoles.operator &&
            <div
              className={`flex p-6 items-center gap-4 rounded-[4px] bg-AOI-blue cursor-pointer
                shadow-[0px 0px 12px 0px rgba(0, 0, 0, 0.25) inset] hover:bg-AOI-blue-hover transition-all duration-300 ease-in-out `}
              onClick={() => {
                // navigate('/teach');
                navigate('/auto-programming-setup');
              }}
              style={{
                width: `calc((100vw - 280px - 32px) / 3)`,
              }}
            >
              <div className='flex w-8 h-8 items-center justify-center rounded-[24px] bg-gray-1'>
                <img src='/icn/chipCircled_blue.svg' alt='chip' className='w-4 h-4' />
              </div>
              <div className='flex flex-col justify-center items-start'>
                <span className='font-source text-[16px] font-semibold leading-[150%] text-gray-1'>
                  {t('home.teachPCB')}
                </span>
                <span className='font-source text-[14px] font-normal leading-[150%] text-gray-1'>
                  {t('home.registerNewPCB')}
                </span>
              </div>
            </div>
          }
          <div
            className={`flex p-6 items-center gap-4 rounded-[4px] bg-AOI-yellow cursor-pointer
              shadow-[0px 0px 12px 0px rgba(0, 0, 0, 0.25) inset] hover:bg-AOI-yellow-hover transition-all duration-300 ease-in-out `}
            onClick={() => {
              navigate('/worklist');
            }}
            style={{
              width: `calc((100vw - 280px - 32px) / 3)`,
            }}
          >
            <div className='flex w-8 h-8 items-center justify-center rounded-[24px] bg-gray-1'>
              <img src='/icn/checklistCircled_yellow.svg' alt='checklist' className='w-4 h-4' />
            </div>
            <div className='flex flex-col justify-center items-start'>
              <span className='font-source text-[16px] font-semibold leading-[150%] text-gray-1'>
                {t('home.workList')}
              </span>
              <span className='font-source text-[14px] font-normal leading-[150%] text-gray-1'>
                {t('home.reviewLast')}
              </span>
            </div>
          </div>
        </div>
        {/* content tables */}
        { _.includes([userRoles.admin, userRoles.programmer], userRole) &&
          <div className='flex gap-[48px] flex-1 self-stretch'>
            <div className='flex flex-col gap-8 flex-1 self-stretch'>
              <div className='flex flex-col gap-4 self-stretch'>
                <div className='flex h-[48px] py-2 gap-6 self-stretch items-end justify-between'>
                  <div className='flex gap-3 items-center'>
                    <img src='/icn/checklist_white.svg' alt='checklist' className='w-5 h-5' />
                    <span className='font-source text-[20px] font-semibold leading-[normal]'>
                      {t('home.recentInspectionTask')}
                    </span>
                  </div>
                  <div className='flex gap-3 items-center'>
                    <Button
                      type='text'
                      size='small'
                      onClick={() => {
                        initConveyorStatus();
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[normal]'>
                        {t('home.refreshConveyorStatus')}
                      </span>
                    </Button>
                  </div>
                </div>
                {conveyorItems.map(item => (
                  <ConveyorItem key={item.id} item={item} navigate={navigate} t={t} />
                ))}
              </div>
              <div className='flex flex-col self-stretch flex-1'>
                { !sessionsLoading && !sessionsError &&
                  <CommonTable
                    cols={[
                      {
                        key: 'task',
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('common.task')}</span>,
                        render: (text, record) => {
                          return _.map(_.get(record, 'golden_product_ids', []), (pid, index) => {
                            return <span className={'font-source text-[14px] font-semibold leading-[150%]'}>
                              {_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name', 'unknown product')}
                            </span>
                          });
                        },
                        // render: (text, record) => (
                        //   <span className='font-source text-[12px] font-normal leading-[150%]'>
                        //     {`PCB-${_.random(1, 20, false)}`}
                        //   </span>
                        // ),
                      },
                      {
                        key: 'ipc_count',
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.totalProduct')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {record.total_product_count}
                            {/* {_.random(50, 100, false)} */}
                          </span>
                        ),
                      },
                      {
                        key: 'pass_rate',
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.passRate')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-semibold leading-[150%]'>
                            {`${
                              _.isInteger(record.total_product_count) && record.total_product_count > 0 ?
                              _.round((record.total_product_count-record.defective_product_count)/record.total_product_count * 100, 2)
                              : 0
                            }% ${t('common.pass')}`}
                            {/* {`${_.round(_.random(50, 100, true), 4)}% ${t('common.pass')}` } */}
                          </span>
                        ),
                        // sorter: (a, b) => b.pass_rate - a.pass_rate,
                        // sortIcon: ({ sortOrder }) => (
                        //   <img
                        //     src='/icn/arrowUp_white.svg'
                        //     alt='arrow'
                        //     className={`w-[6.5px] h-[10px] ${sortOrder === 'ascend' ? 'rotate-180' : ''} transition-transform duration-300 ease-in-out`}
                        //   />
                        // ),
                      },
                      {
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.startedAt')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {backendTimestampToDisplayString(record.started_at)}
                          </span>
                        ),
                        sorter: (a, b) => convertBackendTimestampToMoment(b.started_at) - convertBackendTimestampToMoment(a.started_at),
                        // sortIcon: ({ sortOrder }) => (
                        //   <img
                        //     src='/icn/arrowUp_white.svg'
                        //     alt='arrow'
                        //     className={`w-[6.5px] h-[10px] ${sortOrder === 'ascend' ? 'rotate-180' : ''} transition-transform duration-300 ease-in-out`}
                        //   />
                        // ),
                      },
                    ]}
                    data={recentSessionsData}
                    total={_.isUndefined(recentSessions)?0:_.get(recentSessions, 'pageCount', 0)}
                    isLoading={sessionsLoading}
                  />
                }
              </div>
            </div>
            <div className='flex flex-col py-4 px-2 gap-2 flex-1 self-stretch bg-[##ffffff08]'>
              <div className='flex px-4 items-center gap-4 self-stretch'>
                <div className='flex flex-1 items-baseline gap-4'>
                  <span className='font-source text-[20px] font-semibold leading-[normal] whitespace-nowrap'>
                    {t('home.managePCB')}
                  </span>
                  <span className='font-source text-[14px] font-normal leading-[normal] whitespace-nowrap'>
                    {`${t('common.total')}: ${totalProductsCount}`}
                  </span>
                </div>
                <Input
                  style={{ width: '200px' }}
                  addonBefore={<SearchOutlined />}
                  value={pcbTypeFilter}
                  onChange={(e) => setPcbTypeFilter(e.target.value)}
                  placeholder={t('common.search')}
                />
              </div>
              <div className='flex flex-col self-stretch flex-1'>
                { !allProductsLoading && !allProductsError &&
                  <CommonTable
                    cols={[
                      {
                        key: 'PCBType',
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.PCBType')}</span>,
                        render: (text, record) => (
                          <div className='flex flex-1 self-stretch items-center gap-4'>
                            <span className='font-source text-[14px] font-semibold leading-[150%]'>
                              {_.get(record, 'product_name', '')}
                            </span>
                            {/* <div className='flex items-center gap-1'>
                              <div className='w-2 h-2 bg-red rounded-full' />
                              <span className='font-source text-[12px] font-normal leading-[150%] italic text-gray-5'>
                                Incomplete
                              </span>
                            </div> */}
                          </div>
                        ),
                      },
                      {
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.lastUpdated')}</span>,
                        render: (text, record) => (
                          <span className='font-source text-[12px] font-normal leading-[150%]'>
                            {backendTimestampToDisplayString(record.last_modified)}
                          </span>
                        ),
                        sorter: (a, b) => convertBackendTimestampToMoment(b.last_modified) - convertBackendTimestampToMoment(a.last_modified),
                      },
                      {
                        title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('common.actions')}</span>,
                        render: (text, record) => (
                          <>
                            <span
                              className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                                italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                                px-2`}
                              onClick={() => {
                                navigate(`/teach?product-id=${record.product_id}&inspectables=${record?.inspectables?.length > 0 ? true : false}`);
                              }}
                            >
                            {t('common.edit')}
                            </span>
                            <span
                              className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                                italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                                px-2`}
                              onClick={async() => {
                                await handleCloneGoldenProduct(record.product_id, record.product_name + `-copy`);
                              }}
                            >
                              {t('common.copy')}
                            </span>
                            <span
                              className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                                italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                                px-2`}
                              onClick={async() => {
                                // await handleDleteProduct(record.product_id)
                                setRemoveProductTarget(record);
                                setIsRemovePCBProgramModalOpened(true);
                              }}
                            >
                              {t('common.delete')}
                            </span>
                            <span
                              className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                              px-2`}
                              onClick={() => {
                                setRenameTargetProduct(record);
                                setIsRenameProductModalOpened(true);
                              }}
                            >
                              {t('common.rename')}
                            </span>
                            <span
                              className={`font-source text-[12px] font-normal leading-[150%] text-AOI-blue cursor-pointer
                              italic hover:text-AOI-blue-hover hover:bg-[#333] rounded-[4px] transition-all duration-300 ease-in-out
                              px-2`}
                              onClick={() => {
                                exportAsFile(record.product_id);
                              }}
                            >
                              {t('common.exportAsFile')}
                            </span>
                          </>
                        ),
                      }
                    ]}
                    data={filteredProducts}
                    total={filteredProducts.length}
                    isLoading={allProductsLoading}
                  />
                }
              </div>
            </div>
          </div>
        }
        { userRole === userRoles.operator &&
          <div className='flex gap-[32px] flex-col flex-1 self-stretch'>
            <div className='flex flex-col gap-4 self-stretch'>
              <div className='flex h-[48px] py-2 gap-6 self-stretch items-end'>
                <div className='flex gap-3 items-center'>
                  <img src='/icn/checklist_white.svg' alt='checklist' className='w-5 h-5' />
                  <span className='font-source text-[20px] font-semibold leading-[normal]'>
                    {t('home.recentInspectionTask')}
                  </span>
                </div>
              </div>
              {curRunningTask > 0 &&
                <div className='flex gap-4 self-stretch'>
                  {conveyorItems.map(item => (
                    <OperatorConveyorItem key={item.id} item={item} navigate={navigate} t={t} />
                  ))}
                </div>
              }
            </div>
            <div className='flex flex-1 self-stretch'>
              { !sessionsLoading && !sessionsError &&
                <CommonTable
                  cols={[
                    {
                      key: 'task',
                      title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('common.task')}</span>,
                      render: (text, record) => {
                        return _.map(_.get(record, 'golden_product_ids', []), (pid, index) => {
                          return <span className={'font-source text-[14px] font-semibold leading-[150%]'}>
                            {_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name', 'unknown product')}
                          </span>
                        });
                      },
                      // render: (text, record) => (
                      //   <span className='font-source text-[12px] font-normal leading-[150%]'>
                      //     {`PCB-${_.random(1, 20, false)}`}
                      //   </span>
                      // ),
                    },
                    {
                      key: 'ipc_count',
                      title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.totalProduct')}</span>,
                      render: (text, record) => (
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {record.total_product_count}
                          {/* {_.random(50, 100, false)} */}
                        </span>
                      ),
                    },
                    {
                      key: 'pass_rate',
                      title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.passRate')}</span>,
                      render: (text, record) => (
                        <span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {`${
                            _.isInteger(record.total_product_count) && record.total_product_count > 0 ?
                            _.round((record.total_product_count-record.defective_product_count)/record.total_product_count * 100, 2)
                            : 0
                          }% ${t('common.pass')}`}
                          {/* {`${_.round(_.random(50, 100, true), 4)}% ${t('common.pass')}` } */}
                        </span>
                      ),
                      // sorter: (a, b) => b.pass_rate - a.pass_rate,
                      // sortIcon: ({ sortOrder }) => (
                      //   <img
                      //     src='/icn/arrowUp_white.svg'
                      //     alt='arrow'
                      //     className={`w-[6.5px] h-[10px] ${sortOrder === 'ascend' ? 'rotate-180' : ''} transition-transform duration-300 ease-in-out`}
                      //   />
                      // ),
                    },
                    {
                      title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('home.startedAt')}</span>,
                      render: (text, record) => (
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          {backendTimestampToDisplayString(record.started_at)}
                        </span>
                      ),
                      sorter: (a, b) => convertBackendTimestampToMoment(b.started_at) - convertBackendTimestampToMoment(a.started_at),
                      // sortIcon: ({ sortOrder }) => (
                      //   <img
                      //     src='/icn/arrowUp_white.svg'
                      //     alt='arrow'
                      //     className={`w-[6.5px] h-[10px] ${sortOrder === 'ascend' ? 'rotate-180' : ''} transition-transform duration-300 ease-in-out`}
                      //   />
                      // ),
                    },
                  ]}
                  data={recentSessionsData}
                  total={_.isUndefined(recentSessions)?0:_.get(recentSessions, 'pageCount', 0)}
                  isLoading={sessionsLoading}
                />
              }
            </div>
          </div>
        }
      </div>
    </Fragment>
  );
};

export default Home;