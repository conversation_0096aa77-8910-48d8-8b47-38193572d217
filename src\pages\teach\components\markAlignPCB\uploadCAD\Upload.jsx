import { Button, Upload } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { localStorageKeys, serverHost, uploadCADSupportedFileExtension } from '../../../../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../../../../common/alert';
import { getMIMETypeByExtension } from '../../../../../common/util';
import { useNavigate } from 'react-router-dom';


const UploadCAD = (props) => {
  const {
    setCurrentFileUri,
    setFileObj,
    setCurrentStep,
    productId,
    isInAutoProgramming,
    setCurrentTranslation,
    setCurrentRotation,
    setHorizontallyFlipped,
    setVerticallyFlipped,
    setParseRules,
  } = props;

  const navigate = useNavigate();

  const { t } = useTranslation();
  
  const handleUpload = (file) => {
    if (_.isEmpty(file)) return;

    const upload = async (fileObj) => {
      if (!_.includes(uploadCADSupportedFileExtension, fileObj.name.split('.').pop())) {
        aoiAlert(t('notification.error.CADUploadfileExtensionNotSupported'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const reader = new FileReader();
      reader.readAsArrayBuffer(file);
      const fileContent = await new Promise((resolve) => {
        reader.onload = () => {
          resolve(reader.result);
        };
      });

      setFileObj(fileObj);

      let res;

      try {
        res = await fetch(`${serverHost}/file?extension=${fileObj.name.split('.').pop()}`, {
          method: 'PUT',
          body: fileContent,
          headers: {
            'Content-Type': getMIMETypeByExtension(fileObj.name.split('.').pop()),
            'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
          },
        });
      } catch (error) {
        console.error(error);
        aoiAlert(t('notification.error.uploadFile'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const data = await res.json();
      setCurrentFileUri(_.get(data, 'data_uri'));
      setCurrentStep(0);

      setParseRules({
        delimiter: ',',
        firstRowIndex: null,
        lastRowIndex: null,
        unitMutiplier: null,
        partNumberCol: undefined,
        packageCol: undefined,
        xCol: undefined,
        yCol: undefined,
        isIgnoreBotLayer: false,
        topLayerId: '',
        botLayerId: '',
        botLayerCol: undefined,
        isRotationEnabled: false,
        rotationCol: undefined,
      });

      setCurrentTranslation({
        x: 0,
        y: 0,
      });
      setCurrentRotation(0);
      setHorizontallyFlipped(false);
      setVerticallyFlipped(false);
    };

    upload(file);

    // prevent antd's upload handle
    return false;
  };

  return (
    <div className='flex items-center justify-center self-stretch flex-1'>
      <div className='flex p-4 gap-4 flex-col gap-4 self-stretch items-center justify-center'>
        <div className='flex flex-col items-center self-stretch'>
          <span className='font-source text-[18px] font-normal leading-[150%] tracking-[0.54px]'>
            {t('productDefine.importPickAndPlaceFile')}
          </span>
          <span className='font-source text-[12px] font-normal leading-[150%] text-gray-4'>
            {t('productDefine.useTheComponent')}
          </span>
        </div>
        <div className='flex items-center gap-2 self-stretch justify-center'>
          {!isInAutoProgramming &&
            <Button
              onClick={() => {
                navigate(`/teach?product-id=${productId}&from-upload-cad=true`);
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('common.cancel')}
              </span>
            </Button>
          }
          <Upload
            beforeUpload={handleUpload}
            showUploadList={false}
          >
            <Button
              type='primary'
            >
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('common.selectFile')}
              </span>
            </Button>
          </Upload>
        </div>
      </div>
    </div>
  );
};

export default UploadCAD;