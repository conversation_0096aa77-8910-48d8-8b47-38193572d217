import { But<PERSON>, ConfigProvider, Tabs, Tooltip } from 'antd';
import { CustomModal } from '../../../../common/styledComponent';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ComponentInfo from './ComponentInfo';
import _ from 'lodash';
import LineItemParams from './LineItemParams';
import TrainingSet from './TrainingSet';
import { useModelUpdateTriggerMutation, useReevaluateExamplesMutation } from '../../../../services/inference';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import SelectedGroupAgentParam from './SelectedGroupAgentParam';
import GroupTrainingSet from './GroupTrainingSet';
import { useLazyGetAgentParamsQuery } from '../../../../services/product';
import { leadGapFeatureType, leadFeatureType } from '../../../../common/const';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setCurTrainingTaskStartTime, setIsAgentParamRegenRunning, setIsContainerLvlLoadingEnabled, setIsTrainingRunning } from '../../../../reducer/setting';
import { LineChartOutlined } from '@ant-design/icons';
import AgentParamStats from '../../../../modal/AgentParamStats';
import { modelTypes } from '../../../../common/const';


const ComponentDetail = (props) => {
  const {
    goldenProductId,
    allComponents,
    allFeatures,
    selectedFid,
    selectedCid,
    setIsAddFromLibraryOpened,
    setIsDrawModeEnabled,
    refetchAllFeatures,
    setSelectedAgentParam,
    refetchAllComponents,
    selectedUngroupedFid,
    trainingSetSelectedDetail,
    setTrainingSetSelectedDetail,
    trainingSetSelectedErrorType,
    setTrainingSetSelectedErrorType,
    refetchAllFeatureReevaluationResult,
    refetchAggregatedReevaluationResult,
    updateAllFeaturesState,
    isRedefiningInspectionRegion,
    setSelectedFid,
    selectedFeatureType,
    componentListGroupMode,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    setSelectedCid,
    setRequiredLocateRect,
    selectedArrayIndex,
    setSelectedArrayIndex,
    mmToPixelRatio,
    isPesudoColorDisplayed,
    setIsPesudoColorDisplayed,
    shouldTrainingSetRefetch,
    setShouldTrainingSetRefetch,
    setSelectedFeatureType,
    setAutoGenAgentParamListeningFinished,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const clickFromTrainingSetCardRef = useRef(false);

  const [activeTab, setActiveTab] = useState('trainingSet');
  const [reevaluateTriggered, setReevaluateTriggered] = useState(false);
  const [selectedGroupFeatureTypeAgentParams, setSelectedGroupFeatureTypeAgentParams] = useState(null);
  const [isAgentParamStatsOpen, setIsAgentParamStatsOpen] = useState(false);
  const [isGenerateParamsModalOpened, setIsGenerateParamsModalOpened] = useState(false);

  const [reevaluateExample] = useReevaluateExamplesMutation();
  const [getAgentParamsInGroup] = useLazyGetAgentParamsQuery();
  const [retrainTrigger] = useModelUpdateTriggerMutation();

  const handleRefetchSelectedGroupAgentParams = async (
    selectedFeatureType,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    selectedScope,
  ) => {
    const payload = {
      feature_type: selectedFeatureType === leadGapFeatureType ? leadFeatureType : selectedFeatureType,
      product_id: goldenProductId,
      step: 0,
    };

    if (_.startsWith(selectedFeatureType, '_text')) {
      payload.component_id = selectedCid;
    } else {
      if (_.isInteger(selectedCid) && selectedScope === 'component') {
        payload.component_id = selectedCid;
      } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
        payload.part_no = selectedPartNo;
      } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
        payload.package_no = selectedPackageNo;
      }
    }

    const res = await getAgentParamsInGroup(payload);

    if (res.error) {
      aoiAlert(t('notification.error.getAgentParamInGroup'), ALERT_TYPES.COMMON_ERROR);
      console.error('get agent params in group failed', res.error.message);
      return;
    }

    setSelectedGroupFeatureTypeAgentParams(res.data);
  };

  const handleSingleComponentAutoGenerateAgentParams = async (
    productId,
    selectedCid,
  ) => {
    if (!_.isInteger(selectedCid)) return;

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));
    
    const res = await retrainTrigger({
      model_types: [
        modelTypes.mounting3DModel,
        modelTypes.lead3DModel,
        modelTypes.solder3DModel,
        modelTypes.lead2DV2Model,
        modelTypes.solder2DModel,
        modelTypes.leadModel,
        modelTypes.mountingModel,
        modelTypes.textDirectionModel,
      ],
      golden_product_id: Number(productId),
      update_parameters: true,
      root_component_id: selectedCid,
    });
    
    if (res.error) {
      aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    // dispatch(setIsAgentParamRegenRunning(true));
    setAutoGenAgentParamListeningFinished(true);
    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
  };

  const handleSingleFeatureReevaluateTrigger = async (goldenProductId, selectedFid, selectedUngroupedFid, selectedCid, allComponents, selectedArrayIndex,
  ) => {
    const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);

    let groupLevel;
    let groupLevelValue;

    if (!componentObj.can_group_by_part_no && !componentObj.can_group_by_package_no) {
      groupLevel = 'component_id';
      groupLevelValue = selectedCid;
    } else if (componentObj.can_group_by_part_no && !componentObj.cna_group_by_part_no) {
      groupLevel = !_.isEmpty(componentObj.part_no) ? 'part_no' : 'component_id';
      groupLevelValue = !_.isEmpty(componentObj.part_no) ? componentObj.part_no : componentObj.region_group_id;
    } else if (componentObj.can_group_by_package_no) {
      groupLevel = 'package_no';
      groupLevelValue = componentObj.package_no;
    }

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.reevaluating')));

    const res = await reevaluateExample({
      golden_product_id: goldenProductId,
      step: 0,
      // feature_id: selectedFid || selectedUngroupedFid,
      // has_feedback: true,
      // component_id: selectedCid,
      [groupLevel]: groupLevelValue,
      has_feedback: true,
    });

    if (res.error) {
      aoiAlert(t('notification.error.reevaluateExample'), ALERT_TYPES.COMMON_ERROR);
      console.error('reevaluate example failed', res.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    if (activeTab === 'trainingSet') setReevaluateTriggered(true);

    await refetchAllFeatureReevaluationResult();
    await refetchAggregatedReevaluationResult();

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    // const res1 = await shouldUpdateModels({
    //   model_types: [
    //     modelTypes.mountingModel,
    //     modelTypes.leadModel
    //   ]
    // });

    // setShouldUpdateModelButtonType(_.some(res1.data, d => d));
  };

  useEffect(() => {
    if (clickFromTrainingSetCardRef.current) {
      // console.log('click from training set card');
      clickFromTrainingSetCardRef.current = false;
      return;
    }

    if (_.isEmpty(selectedFeatureType)) return;

    if (!_.isInteger(selectedCid) && _.isEmpty(selectedPartNo) && _.isEmpty(selectedPackageNo)) {
      setSelectedGroupFeatureTypeAgentParams(null);
      return;
    }

    const run = async (selectedFeatureType, selectedCid, selectedPartNo, selectedPackageNo, goldenProductId, selectedScope) => {
      const payload = {
        feature_type: selectedFeatureType === leadGapFeatureType ? leadFeatureType : selectedFeatureType,
        product_id: goldenProductId,
        step: 0,
      };

      if (_.startsWith(selectedFeatureType, '_text')) {
        payload.component_id = selectedCid;
      } else {
        if (_.isInteger(selectedCid) && selectedScope === 'component') {
          payload.component_id = selectedCid;
        } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
          payload.part_no = selectedPartNo;
        } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
          payload.package_no = selectedPackageNo;
        }
      }

      const res = await getAgentParamsInGroup(payload);

      if (res.error) {
        aoiAlert(t('notification.error.getAgentParamInGroup'), ALERT_TYPES.COMMON_ERROR);
        console.error('get agent params in group failed', res.error.message);
        return;
      }

      setSelectedGroupFeatureTypeAgentParams(res.data);
    };

    run(selectedFeatureType, selectedCid, selectedPartNo, selectedPackageNo, goldenProductId, selectedScope);
  }, [
    selectedFeatureType,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    goldenProductId,
    selectedScope,
    allFeatures,
  ]);

  // when template tab is active, selecting a feature type from the component
  // list should behave the same as selecting from training set. Pick the first
  // feature that matches the current scope and feature type and locate it in
  // the scene.
  // useEffect(() => {
  //   if (activeTab !== 'template') return;
  //   if (_.isEmpty(selectedFeatureType)) return;

  //   const searchType = selectedFeatureType === leadGapFeatureType ? leadFeatureType : selectedFeatureType;

  //   let feature = null;
  //   if (selectedScope === 'component' && _.isInteger(selectedCid)) {
  //     feature = _.find(
  //       allFeatures,
  //       f => f.group_id === selectedCid && f.feature_type === searchType,
  //     );
  //   } else if (selectedScope === 'part' && !_.isEmpty(selectedPartNo)) {
  //     feature = _.find(
  //       allFeatures,
  //       f => f.part_no === selectedPartNo && f.feature_type === searchType,
  //     );
  //   } else if (selectedScope === 'package' && !_.isEmpty(selectedPackageNo)) {
  //     feature = _.find(
  //       allFeatures,
  //       f => f.package_no === selectedPackageNo && f.feature_type === searchType,
  //     );
  //   }

  //   if (feature) {
  //     setSelectedCid(feature.group_id);
  //     setSelectedFid(feature.feature_id);
  //     setSelectedArrayIndex(feature.array_index);
  //     setRequiredLocateRect({ cid: feature.group_id, fid: feature.feature_id });
  //   }
  // }, [
  //   activeTab,
  //   selectedFeatureType,
  //   selectedScope,
  //   selectedCid,
  //   selectedPartNo,
  //   selectedPackageNo,
  //   allFeatures,
  // ]);

  return (
    <Fragment>
    <AgentParamStats
      isOpened={isAgentParamStatsOpen}
      setIsOpened={setIsAgentParamStatsOpen}
      selectedFeatureType={selectedFeatureType}
      selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
      selectedScope={selectedScope}
      goldenProductId={goldenProductId}
      selectedCid={selectedCid}
      selectedPartNo={selectedPartNo}
      selectedPackageNo={selectedPackageNo}
    />
    <div
      className='flex flex-1 flex-col self-stretch'
      style={{
        pointerEvents: isRedefiningInspectionRegion ? 'none' : 'auto',
        opacity: isRedefiningInspectionRegion ? 0.5 : 1,
      }}
    >
      <div className='flex pt-1 items-center self-stretch border-b-[1px] border-b-gray-2'>
        <div className='flex gap-2 items-center self-stretch'>
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  cardPadding: '6px 18px',
                }
              }
            }}
          >
            <Tabs
              style={{ width: '100%' }}
              activeKey={activeTab}
              onChange={(key) => {
                setActiveTab(key)
              }}
              type='card'
              items={[
                {
                  label: <div className='flex gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'trainingSet' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.trainingSet')}
                    </span>
                  </div>,
                  key: 'trainingSet',
                },
                {
                  label: <div className='flex gap-2 items-center justify-center'>
                    <span className={`font-source text-[14px] font-${activeTab === 'template' ? 'semibold' : 'normal'} leading-[normal]`}>
                      {t('productDefine.template')}
                    </span>
                  </div>,
                  key: 'template',
                },
              ]}
            />
          </ConfigProvider>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  primaryColor: '#57f2c433',
                  colorPrimary: '#57f2c433',
                  colorPrimaryHover: '#57f2c433',
                  colorPrimaryActive: '#57f2c433  ',
                  controlHeight: 28,
                }
              }
            }}
          >
            <Button
              type='primary'
              onClick={() => {
                handleSingleFeatureReevaluateTrigger(
                  goldenProductId,
                  selectedFid,
                  selectedUngroupedFid,
                  selectedCid,
                  allComponents,
                  selectedArrayIndex,
                );
              }}
            >
              <div className='flex items-center gap-1'>
                <img src='/icn/play_green.svg' alt='play' className='w-[8.5px] h-[10px]' />
                <span className='font-source text-[12px] font-semibold text-AOI-green leading-[150%]'>
                  {t('common.test')}
                </span>
              </div>
            </Button>
          </ConfigProvider>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  primaryColor: '#57f2c433',
                  colorPrimary: '#57f2c433',
                  colorPrimaryHover: '#57f2c433',
                  colorPrimaryActive: '#57f2c433  ',
                  controlHeight: 28,
                }
              }
            }}
          >
            <Button
              type='primary'
              onClick={() => {
                // setIsGenerateParamsModalOpened(true);
                handleSingleComponentAutoGenerateAgentParams(
                  goldenProductId,
                  selectedCid,
                );
              }}
            >
              <div className='flex items-center gap-1'>
                <img src='/icn/play_green.svg' alt='play' className='w-[8.5px] h-[10px]' />
                <span className='font-source text-[12px] font-semibold text-AOI-green leading-[150%]'>
                  {t('productDefine.generateParams')}
                </span>
              </div>
            </Button>
          </ConfigProvider>
          <Tooltip
            title={<span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.viewStats')}
            </span>}
          >
            <Button
              onClick={() => {
                setIsAgentParamStatsOpen(true);
              }}
            >
              <LineChartOutlined
                className='w-3 h-3'
              />
            </Button>
          </Tooltip>
        </div>
      </div>
      <div
        className='flex pt-2 pb-1 flex-col gap-0.5 self-stretch border-b-[1px] border-b-gray-2 bg-[#ffffff0d] overflow-y-auto'
        style={{ height: 'calc(100vh - 214px + 28px)' }}
      >
        {activeTab === 'template' && !_.isEmpty(selectedScope) &&
          <ComponentInfo
            allComponents={allComponents}
            selectedCid={selectedCid}
            setIsAddFromLibraryOpened={setIsAddFromLibraryOpened}
            setIsDrawModeEnabled={setIsDrawModeEnabled}
            allFeatures={allFeatures}
            refetchAllComponents={refetchAllComponents}
            selectedPackageNo={selectedPackageNo}
            selectedPartNo={selectedPartNo}
            selectedScope={selectedScope}
            goldenProductId={goldenProductId}
            updateAllFeaturesState={updateAllFeaturesState}
            handleRefetchSelectedGroupAgentParams={handleRefetchSelectedGroupAgentParams}
            selectedFeatureType={selectedFeatureType}
          />
        }
        {/* {activeTab === 'template' && _.isInteger(selectedUngroupedFid) &&
          <LineItemParams
            selectedFeature={_.find(allFeatures, f => {
              return _.isInteger(selectedCid) ? f.feature_id === selectedFid && f.group_id === selectedCid : f.feature_id === selectedUngroupedFid
            })}
            refetchAllFeatures={refetchAllFeatures}
            updateAllFeaturesState={updateAllFeaturesState}
            setSelectedAgentParam={setSelectedAgentParam}
            allFeatures={allFeatures}
            refetchAllComponents={refetchAllComponents}
          />
        } */}
        {activeTab === 'template' && !_.isEmpty(selectedScope) && !_.isEmpty(selectedFeatureType) && 
          <SelectedGroupAgentParam
            selectedCid={selectedCid}
            selectedPartNo={selectedPartNo}
            selectedPackageNo={selectedPackageNo}
            selectedFeatureType={selectedFeatureType}
            allFeatures={allFeatures}
            allComponents={allComponents}
            goldenProductId={goldenProductId}
            selectedScope={selectedScope}
            setSelectedAgentParam={setSelectedAgentParam}
            updateAllFeaturesState={updateAllFeaturesState}
            refetchAllComponents={refetchAllComponents}
            selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
            setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
            handleRefetchSelectedGroupAgentParams={handleRefetchSelectedGroupAgentParams}
          />
        }
        {/* {_.isInteger(selectedCid) && _.isInteger(selectedFid) && activeTab === 'trainingSet' &&
          <TrainingSet
            selectedComponent={_.find(allComponents, c => c.region_group_id === selectedCid)}
            selectedFeature={_.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid)}
            reevaluateTriggered={reevaluateTriggered}
            setReevaluateTriggered={setReevaluateTriggered}
            trainingSetSelectedDetail={trainingSetSelectedDetail}
            setTrainingSetSelectedDetail={setTrainingSetSelectedDetail}
            trainingSetSelectedErrorType={trainingSetSelectedErrorType}
            setTrainingSetSelectedErrorType={setTrainingSetSelectedErrorType}
            setSelectedFid={setSelectedFid}
            selectedFeatureType={selectedFeatureType}
            componentListGroupMode={componentListGroupMode}
            selectedPartNo={selectedPartNo}
            selectedPackageNo={selectedPackageNo}
            goldenProductId={goldenProductId}
          />
        } */}
        {activeTab === 'trainingSet' && !_.isEmpty(selectedFeatureType) && !_.isEmpty(selectedScope) && (!_.isEmpty(selectedPackageNo) || !_.isEmpty(selectedPartNo) || _.isInteger(selectedCid)) &&
          <GroupTrainingSet
            isPesudoColorDisplayed={isPesudoColorDisplayed}
            setIsPesudoColorDisplayed={setIsPesudoColorDisplayed}
            reevaluateTriggered={reevaluateTriggered}
            setReevaluateTriggered={setReevaluateTriggered}
            trainingSetSelectedDetail={trainingSetSelectedDetail}
            setTrainingSetSelectedDetail={setTrainingSetSelectedDetail}
            trainingSetSelectedErrorType={trainingSetSelectedErrorType}
            setTrainingSetSelectedErrorType={setTrainingSetSelectedErrorType}
            setSelectedFid={setSelectedFid}
            selectedFeatureType={selectedFeatureType}
            componentListGroupMode={componentListGroupMode}
            selectedPartNo={selectedPartNo}
            selectedPackageNo={selectedPackageNo}
            goldenProductId={goldenProductId}
            selectedScope={selectedScope}
            selectedCid={selectedCid}
            allComponents={allComponents}
            selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
            setSelectedCid={setSelectedCid}
            allFeatures={allFeatures}
            setRequiredLocateRect={setRequiredLocateRect}
            clickFromTrainingSetCardRef={clickFromTrainingSetCardRef}
            selectedArrayIndex={selectedArrayIndex}
            setSelectedArrayIndex={setSelectedArrayIndex}
            mmToPixelRatio={mmToPixelRatio}
            selectedFid={selectedFid}
            shouldTrainingSetRefetch={shouldTrainingSetRefetch}
            setShouldTrainingSetRefetch={setShouldTrainingSetRefetch}
            setSelectedFeatureType={setSelectedFeatureType}
            handleRefetchSelectedGroupAgentParams={handleRefetchSelectedGroupAgentParams}
          />
        }
      </div>
    </div>

    {/* Generate Params Confirmation Modal */}
    <CustomModal
      open={isGenerateParamsModalOpened}
      onCancel={() => setIsGenerateParamsModalOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('productDefine.generateParams')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col gap-4 p-4'>
        <span className='font-source text-[14px] font-normal leading-[150%] tracking-[0.42px]'>
          {t('productDefine.generateParamsConfirmation')}
        </span>
        <div className='flex gap-2 items-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => {
              setIsGenerateParamsModalOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              setIsGenerateParamsModalOpened(false);
              handleSingleComponentAutoGenerateAgentParams(
                goldenProductId,
                selectedCid,
              );
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[normal]'>
              {t('common.confirm')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
    </Fragment>
  );
};

export default ComponentDetail;