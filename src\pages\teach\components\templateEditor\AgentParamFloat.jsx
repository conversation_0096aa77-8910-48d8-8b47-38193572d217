import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { InputNumber } from 'antd';
import { useSelector } from 'react-redux';
import { systemApi } from '../../../../services/system';
import { mmValueDecimal, angleValueDecimal, mmAgentParamsNames, angleAgentParamsNames, percentageAgentParamNames } from '../../../../common/const';

const AgentParamFloat = (props) => {
  const {
    fieldInfo,
    lintItemName,
    selectedGroupFeatureTypeAgentParams,
    agentParamName,
    submit,
    active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
  } = props;

  const { data: systemMetadata } = useSelector((state) =>
    systemApi.endpoints.getSystemMetadata.select()(state)
  );

  let decimal = _.get(
    systemMetadata,
    `default_line_items.${lintItemName}.params.${agentParamName}.param_float.value`,
    0.01
  );

  const decimalStr = decimal.toString();

  if (decimalStr.indexOf('.') === -1) {
    decimal = 2;
  } else {
    decimal = decimalStr.split('.')[1].length;
  }

  if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
    decimal = mmValueDecimal;
  } else if (
    _.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
  ) {
    decimal = angleValueDecimal;
  }

  const [displayedValue, setDisplayedValue] = useState(
    _.round(_.get(fieldInfo, 'value', 0), decimal)
  );

  const displayedValRef = useRef(displayedValue);
  const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

  const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
    const payload = {
      ...selectedGroupFeatureTypeAgentParams,
      line_item_params: {
        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
        [lintItemName]: {
          ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
          params: {
            ..._.get(
              selectedGroupFeatureTypeAgentParams,
              `line_item_params.${lintItemName}.params`
            ),
            [agentParamName]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${agentParamName}`
              ),
              param_float: {
                ..._.get(
                  selectedGroupFeatureTypeAgentParams,
                  `line_item_params.${lintItemName}.params.${agentParamName}.param_float`
                ),
                value: value,
              },
            },
          },
        },
      },
    };

    return payload;
  };

  useEffect(() => {
    setDisplayedValue(_.round(_.get(fieldInfo, 'value', 0), decimal));
    displayedValRef.current = _.round(_.get(fieldInfo, 'value', 0), decimal);
    selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
  }, [fieldInfo, selectedGroupFeatureTypeAgentParams, decimal]);

  useEffect(() => {
    return () => {
      if (displayedValRef.current !== _.round(_.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_float.value`, 0), decimal)) {
        const payload = getPayload(
          displayedValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

  const isPercentage = _.includes(percentageAgentParamNames, agentParamName);

  return (
    <div className="flex items-center gap-1">
      <InputNumber
        disabled={!active}
        step={Math.pow(10, -decimal)}
        precision={decimal}
        style={{ width: '100%' }}
        controls={false}
        min={_.get(fieldInfo, 'min', 0)}
        max={_.get(fieldInfo, 'max', 1)}
        value={active ? displayedValue : null}
        onChange={(value) => {
          setDisplayedValue(value);
          displayedValRef.current = value;
        }}
        onBlur={(e) => {
          const val = Number(e.target.value);
          const payload = getPayload(
            val,
            selectedGroupFeatureTypeAgentParamsRef.current,
            lintItemName,
            agentParamName
          );
          submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
        }}
        onPressEnter={(e) => {
          const val = Number(e.target.value);
          const payload = getPayload(
            val,
            selectedGroupFeatureTypeAgentParamsRef.current,
            lintItemName,
            agentParamName
          );
          submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
        }}
      />
      {isPercentage && (
        <span className="font-source text-[12px] font-normal text-gray-500">%</span>
      )}
    </div>
  );
};

export default AgentParamFloat;
