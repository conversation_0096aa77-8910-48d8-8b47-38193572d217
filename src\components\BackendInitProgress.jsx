import React, { useState, useEffect, useRef } from 'react';
import { Progress } from 'antd';

const BackendInitProgress = ({ isActive = false, onComplete = null }) => {
  const [progress, setProgress] = useState(0);
  const progressInterval = useRef(null);

  useEffect(() => {
    if (isActive) {
      // Start progress animation - go from 0 to 99% in 2 minutes (120 seconds)
      setProgress(0);
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }
      progressInterval.current = setInterval(() => {
        setProgress(prev => {
          if (prev >= 99) {
            return 99; // Cap at 99%
          }
          return prev + (99 / 120); // Increment by 99/120 per second to reach 99% in 2 minutes
        });
      }, 1000); // Update every second
    } else {
      // Clear progress interval when not active
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
      if (onComplete) {
        setProgress(100);
        setTimeout(() => onComplete(), 100); // Small delay to show 100%
      } else {
        setProgress(0);
      }
    }

    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    };
  }, [isActive, onComplete]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    };
  }, []);

  return (
    <Progress
      percent={Math.round(progress)}
      strokeColor={{
        '0%': '#ff4d4f',   // Red at start
        '100%': '#52c41a'  // Green at end
      }}
      style={{ width: '30%' }}
      showInfo={true}
    />
  );
};

export default BackendInitProgress;
