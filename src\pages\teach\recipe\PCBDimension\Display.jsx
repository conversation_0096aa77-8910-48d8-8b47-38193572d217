import React, { useRef } from 'react';
import { CustomSegmented } from '../../../../common/styledComponent';
import PCBDimensionViewer from '../../../../viewer/PCBDimensionViewer';


const Display = (props) => {
  const {
    current2DUri,
    currentDepthImgUri,
    currentThumbnailUri,
    selectedTool,
    setSelectedTool,
    curProduct,
    refetchCurProduct,
    setCurBottemLeftPos,
    setCurTopRightPos,
    handlePickPointUpdateCameraPos,
    cameraPosition,
  } = props;

  const viewerContainerRef = useRef(null);

  return (
    <div className='relative w-full h-full bg-[#000]'>
      {/* scene starts */}
      <div
        className='absolute top-0 left-0 w-full h-full z-[10]'
        ref={viewerContainerRef}
      >
        <PCBDimensionViewer
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          viewerContainerRef={viewerContainerRef}
          imageUri={current2DUri}
          thumbnailUri={currentThumbnailUri}
          depthImgUri={currentDepthImgUri}
          curProduct={curProduct}
          refetchCurProduct={refetchCurProduct}
          setCurBottemLeftPos={setCurBottemLeftPos}
          setCurTopRightPos={setCurTopRightPos}
          handlePickPointUpdateCameraPos={handlePickPointUpdateCameraPos}
          cameraPosition={cameraPosition}
        />
      </div>
      {/* scene ends */}
      {/* tools starts */}
      <div className='absolute top-[50%] right-[8px] z-[11]'>
        <CustomSegmented
          style={{ width: '36px' }}
          vertical
          value={selectedTool === 'transform' ? 'transform' : 'selectPixel'}
          onChange={(value) => setSelectedTool(value)}
          options={[
            {
              value: 'selectPixel',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/navigator_white.svg'
                  alt='navigator'
                  className='w-3 h-3'
                />
              </div>,
              disabled: true,
            },
            {
              value: 'transform',
              label: <div className='flex w-8 h-8 justify-center items-center'>
                <img
                  src='/icn/backHand_white.svg'
                  alt=''
                  className='w-4 h-4'
                />
              </div>,
            }
          ]}
        />
      </div>
      {/* tools ends */}
    </div>
  );
};

export default Display;