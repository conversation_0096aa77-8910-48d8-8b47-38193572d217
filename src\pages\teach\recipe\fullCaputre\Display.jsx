import React, { useEffect, useRef, useState } from 'react';
import WindowedCroppedPointCloudDisplay from './WindowCropped3DDisplay';
import _ from 'lodash';
import FullPCBCaptureViewer from '../../../../viewer/FullPCBCaptureViewer';
import { CustomSegmented } from '../../../../common/styledComponent';
import { serverHost, isAOI2DSMT } from '../../../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { setCurrent3dSelectRectInfo } from '../../../../reducer/display';
import { Tooltip } from 'antd';


const Display = (props) => {
  const {
    curImageUri,
    curDepthImgUri,
    manuallUpdateState,
    fullCaptureDisplayed,
    isInAutoGenSetting,
    selectedTool,
    setSelectedTool,
    autoProgramInspectionRegion,
    setAutoProgramInspectionRegion,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const viewerContainerRef = useRef(null);

  // const [selectedTool, setSelectedTool] = useState('transform');
  const [threedViewerIdList, setThreedViewerIdList] = useState([]);
  const [toolOpts, setToolOpts] = useState([]);

  const handleSubmitSelect3DArea = (curImageUri, curDepthImgUri, pMin, pMax) => {
    dispatch(setCurrent3dSelectRectInfo({
      pMin,
      pMax,
      curImageUri,
      curDepthImgUri
    }));

    const id = _.uniqueId();
    setThreedViewerIdList([...threedViewerIdList, id]);
  };

  useEffect(() => {
    const newOpts = [
      {
        value: 'transform',
        label: <Tooltip
          title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.panZoom')}</span>}
          placement='left'
        >
          <div className='flex w-8 h-8 justify-center items-center'>
            <img
              src='/icn/backHand_white.svg'
              alt=''
              className='w-4 h-4'
            />
          </div>
        </Tooltip>,
      },
      ...(!isAOI2DSMT ? [{
        value: 'select3DArea',
        label: (
          <Tooltip
            title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.crop3DView')}</span>}
            placement='left'
          >
            <div className='flex w-8 h-8 justify-center items-center'>
              <img src='/icn/viewIn3D_white.svg' alt='navigator' className='w-3 h-3' />
            </div>
          </Tooltip>
        ),
      }] : [])
    ];

    if (isInAutoGenSetting) {
      newOpts.push({
        value: 'selectAutoGenArea',
        label: <Tooltip
          title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('autoProgramming.setInspectionRegion')}</span>}
          placement='left'
        >
          <div className='flex w-8 h-8 justify-center items-center'>
            <img
              src='/icn/squareDot_white.svg'
              alt='navigator'
              className='w-3 h-3'
            />
          </div>
        </Tooltip>,
      });
    }

    setToolOpts(newOpts);
  }, [isInAutoGenSetting]);

  return (
    <div className='relative w-full h-full bg-[#000]'>
      {/* 3d windowed displays start */}
      {!isAOI2DSMT && _.map(threedViewerIdList, (id) => (
        <WindowedCroppedPointCloudDisplay
          key={id}
          id={id}
          selfUnmount={(id) => {
            setThreedViewerIdList(
              _.filter(threedViewerIdList, (threedViewerId) => threedViewerId !== id)
            );
          }}
        />
      ))}
      {/* 3d windowed displays end */}
      {/* 2d scene starts */}
      <div className='absolute top-0 left-0 w-full h-full z-[10]' ref={viewerContainerRef}>
        <FullPCBCaptureViewer
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          imageUri={curImageUri}
          depthUri={curDepthImgUri}
          handleSubmitSelect3DArea={(curImageUri, curDepthImgUri, pMin, pMax) => {
            if (!isAOI2DSMT) {
              handleSubmitSelect3DArea(curImageUri, curDepthImgUri, pMin, pMax);
            }
          }}
          manuallUpdateState={manuallUpdateState}
          handleSubmitSelectAutoGenArea={(pmin, pmax) => {
            setAutoProgramInspectionRegion({ pmin, pmax });
          }}
          autoProgramInspectionRegion={autoProgramInspectionRegion}
        />
      </div>
      {/* 2d scene ends */}
      {/* control bar starts */}
      {fullCaptureDisplayed &&
        <div className='absolute top-[50%] right-[8px] z-[11]'>
          <CustomSegmented
            style={{ width: '36px' }}
            vertical
            value={selectedTool}
            onChange={(value) => {
              if (value === 'selectAutoGenArea') setAutoProgramInspectionRegion(null);
              setSelectedTool(value);
            }}
            options={toolOpts}
          />
        </div>
      }
    </div>
  );
};

export default Display;