import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';


const ComponentStatus = (props) => {
  const {
    allComponents,
    allFeatureReevaluationResult,
    componentReevaluationResultMap,
    componentListGroupMode,
  } = props;

  const { t } = useTranslation();

  const [passedComponentCount, setPassedComponentCount] = useState(0);
  const [failedComponentCount, setFailedComponentCount] = useState(0);
  const [undefinedComponentCount, setUndefinedComponentCount] = useState(0); // unhealthy component
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    if (_.isEmpty(allComponents)) {
      setPassedComponentCount(0);
      setFailedComponentCount(0);
      setUndefinedComponentCount(0);
      setTotalCount(0);
      return;
    }

    const mode = componentListGroupMode || 'component';
    let passedCount = 0;
    let failedCount = 0;
    let undefinedCount = 0;
    let total = 0;

    if (mode === 'component') {
      total = allComponents.length;
      for (const c of allComponents) {
        const result = componentReevaluationResultMap?.[c.region_group_id] || {};
        const numPass = _.get(result, 'num_passing', 0);
        const numFail = _.get(result, 'num_failing', 0);
        const containsWhite = _.get(result, 'contains_white_feature', false) || (!componentReevaluationResultMap || !_.has(componentReevaluationResultMap, c.region_group_id)) || (numPass === 0 && numFail === 0);

        if (!c.healthy) {
          undefinedCount++;
        } else if (numFail > 0) {
          failedCount++;
        } else if (!containsWhite && numPass > 0) {
          passedCount++;
        }
      }
    } else {
      const groupMap = {};

      const getPartKey = (comp) => {
        if (!_.isEmpty(comp.part_no) && comp.can_group_by_part_no === true) {
          return `part_${comp.part_no}`;
        }
        return `component_${comp.region_group_id}`;
      };

      const getPackageKey = (comp) => {
        if (!_.isEmpty(comp.package_no) && comp.can_group_by_package_no === true && comp.can_group_by_part_no === true) {
          return `package_${comp.package_no}`;
        }
        if (!_.isEmpty(comp.package_no)) {
          if (comp.can_group_by_part_no !== true) {
            return `component_${comp.region_group_id}`;
          }
          if (comp.can_group_by_package_no === false) {
            if (_.isEmpty(comp.part_no)) {
              return `component_${comp.region_group_id}`;
            }
            return `part_${comp.package_no}_${comp.part_no}`;
          }
          return `package_${comp.package_no}`;
        }
        if (!_.isEmpty(comp.part_no) && comp.can_group_by_part_no === true) {
          return `part_${comp.part_no}`;
        }
        return `component_${comp.region_group_id}`;
      };

      for (const c of allComponents) {
        const key = mode === 'part' ? getPartKey(c) : getPackageKey(c);
        if (!groupMap[key]) {
          groupMap[key] = { num_passing: 0, num_failing: 0, contains_white: false, has_result: false, healthy: c.healthy };
        } else {
          groupMap[key].healthy = groupMap[key].healthy && c.healthy;
        }

        const result = componentReevaluationResultMap?.[c.region_group_id];
        if (result) {
          groupMap[key].num_passing += _.get(result, 'num_passing', 0);
          groupMap[key].num_failing += _.get(result, 'num_failing', 0);
          if (_.get(result, 'num_failing', 0) === 0 && _.get(result, 'num_passing', 0) === 0) {
            groupMap[key].contains_white = true;
          }
          if (_.get(result, 'contains_white_feature', false)) {
            groupMap[key].contains_white = true;
          }
          groupMap[key].has_result = true;
        } else {
          groupMap[key].contains_white = true;
        }
      }

      total = Object.keys(groupMap).length;
      for (const key of Object.keys(groupMap)) {
        const g = groupMap[key];
        if (!g.healthy) {
          undefinedCount++;
        } else if (g.num_failing > 0) {
          failedCount++;
        } else if (!g.contains_white && g.num_passing > 0) {
          passedCount++;
        }
      }
    }

    setPassedComponentCount(passedCount);
    setFailedComponentCount(failedCount);
    setUndefinedComponentCount(undefinedCount);
    setTotalCount(total);
  }, [allComponents, componentReevaluationResultMap, componentListGroupMode]);

  return (
    <div style={{ width: '100%' }}>
      <div className='flex h-[30px] items-center self-stretch '>
        <div className='flex h-[30px] justify-center items-center gap-0.5 self-stretch px-3 py-1 rounded-[4px_4px_0px_0px]'>
          <span className='font-source text-xs font-normal leading-[normal] uppercase'>
            {t('common.information')}
          </span>
        </div>
      </div>
      <div className='flex flex-col justify-center items-start gap-1 self-stretch [background:#1E1E1E] p-2'>
        <div className='flex items-start gap-2 self-stretch'>
          <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/checkFilledCircle_green.svg'
                  className='w-[12px] h-[12px]'
                  alt='checkCircled'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.passed')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {passedComponentCount}/{totalCount}
            </span>
          </div>
          <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/failedCircled_red.svg'
                  className='w-[12px] h-[12px]'
                  alt='failedCircled'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.failed')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {failedComponentCount}
            </span>
          </div>
        </div>
        <div className='flex items-start gap-2 self-stretch'>
          {/* <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/undefined_purple.svg'
                  className='w-[12px] h-[12px]'
                  alt='undefined'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.undefined')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {}
            </span>
          </div> */}
          <div className='flex items-center gap-2 flex-[1_0_0]'>
            <div className='flex w-[92px] items-center'>
              <div className='flex w-6 h-6 flex-col justify-center items-center gap-2.5 shrink-0 px-[3px] py-1.5'>
                <img
                  src='/icn/unknown_gray.svg'
                  className='w-[12px] h-[12px]'
                  alt='unknown'
                />
              </div>
              <span className='font-source text-xs font-normal leading-[150%] pt-0.5'>
                {t('common.unknown')}
              </span>
            </div>
            <span className='text-gray-4 font-source text-xs font-normal leading-[150%]'>
              {undefinedComponentCount}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentStatus;