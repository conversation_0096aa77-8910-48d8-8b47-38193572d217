import { createSlice } from '@reduxjs/toolkit';


const initialState = {
  agentParamUserAction: null,
};

const productDefine = createSlice({
  name: 'productDefine',
  initialState,
  reducers: {
    setAgentParamUserAction: (state, action) => {
      state.agentParamUserAction = action.payload;
    },
  },
});

export const {
  setAgentParamUserAction,
} = productDefine.actions;

export default productDefine.reducer;