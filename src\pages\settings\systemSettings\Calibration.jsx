import { Button } from 'antd';
import React, { Fragment, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CalibrationProcess from '../../../modal/CalibrationProcess';


const Calibration = () => {
  const { t } = useTranslation();

  const [isCalibrateModalOpened, setIsCalibrateModalOpened] = useState(false);

	return (
    <Fragment>
      <CalibrationProcess
        isOpened={isCalibrateModalOpened}
        setIsOpened={setIsCalibrateModalOpened}
      />
      <div className="flex py-4 px-[140px] flex-1 self-stretch justify-center">
        <div className="flex w-[800px] flex-col gap-2 self-stretch p-6">
          <div className="flex items-center self-stretch py-1.5">
            <span className="font-source text-[20px] font-normal leading-[normal] tracking-[0.6px]">
              {t('settings.cameraCalibration')}
            </span>
          </div>
          <div className='flex items-center gap-6 self-stretch w-full'>
            <div className='flex flex-1 self-stretch items-center'>
              <Button
                style={{  width: '100%' }}
                onClick={() => {
                  setIsCalibrateModalOpened(true);
                }}
              >
                <span className='font-source text-[12px] font-normal leading-[normal]'>
                  {t('settings.startCalibration')}
                </span>
              </Button>
            </div>
            <div className='flex flex-1 self-stretch items-center'>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
	);
};

export default Calibration;
