import React, { useState } from 'react';
import Upload from '../../teach/components/markAlignPCB/uploadCAD/Upload';
import Parse from '../../teach/components/markAlignPCB/uploadCAD/Parse';
import AlignCoord from '../../teach/components/markAlignPCB/uploadCAD/AlignCoord';
import ConfigRO<PERSON> from '../../teach/components/markAlignPCB/uploadCAD/ConfigROI';


const UploadCAD = (props) => {
  const {
    curProduct,
    currentStep,
    setCurrentStep,
    selectedInspectionTypes,
    autoProgramInspectionRegion,
  } = props;

  // const [currentStep, setCurrentStep] = useState(-1); // -1: upload  0: parse, user does not need to align and config in auto programming's case
  const [fileObj, setFileObj] = useState(null);
  const [currentFileUri, setCurrentFileUri] = useState('');
  const [parseRules, setParseRules] = useState({
    delimiter: ',',
    firstRowIndex: null,
    lastRowIndex: null,
    unitMutiplier: null,
    partNumberCol: undefined,
    packageCol: undefined,
    xCol: undefined,
    yCol: undefined,
    isIgnoreBotLayer: false,
    topLayerId: '',
    botLayerId: '',
    botLayerCol: undefined,
    isRotationEnabled: false,
    rotationCol: undefined,
    designatorCol: undefined,
  });
  const [parsedComponentInfo, setParsedComponentInfo] = useState([]);

  const [currentTranslation, setCurrentTranslation] = useState({
    x: 0,
    y: 0,
  });
  const [currentRotation, setCurrentRotation] = useState(0);
  const [horizontallyFlipped, setHorizontallyFlipped] = useState(false);
  const [verticallyFlipped, setVerticallyFlipped] = useState(false);

  return (
    <div className='flex flex-1 self-stretch flex-col items-center gap-8'>
      {currentStep === -1 &&
        <Upload
          setCurrentFileUri={setCurrentFileUri}
          setFileObj={setFileObj}
          setCurrentStep={setCurrentStep}
          productId={Number(curProduct.product_id)}
          isInAutoProgramming={true}
          setCurrentTranslation={setCurrentTranslation}
          setCurrentRotation={setCurrentRotation}
          setHorizontallyFlipped={setHorizontallyFlipped}
          setVerticallyFlipped={setVerticallyFlipped}
          setParseRules={setParseRules}
        />
      }
      {currentStep === 0 && (
        <Parse
          fileObj={fileObj}
          currentFileUri={currentFileUri}
          parseRules={parseRules}
          setParseRules={setParseRules}
          setCurrentFileUri={setCurrentFileUri}
          setFileObj={setFileObj}
          productId={Number(curProduct.product_id)}
          setParsedComponentInfo={setParsedComponentInfo}
          setCurrentStep={setCurrentStep}
          isInAutoProgramming={true}
          selectedInspectionTypes={selectedInspectionTypes}
          autoProgramInspectionRegion={autoProgramInspectionRegion}
        />
      )}
      {currentStep === 1 &&
        <AlignCoord
          parsedComponentInfo={parsedComponentInfo}
          setCurrentStep={setCurrentStep}
          curProduct={curProduct}
          currentFileUri={currentFileUri}
          parseRules={parseRules}
          currentTranslation={currentTranslation}
          setCurrentTranslation={setCurrentTranslation}
          currentRotation={currentRotation}
          setCurrentRotation={setCurrentRotation}
          horizontallyFlipped={horizontallyFlipped}
          setHorizontallyFlipped={setHorizontallyFlipped}
          verticallyFlipped={verticallyFlipped}
          setVerticallyFlipped={setVerticallyFlipped}
          isInAutoProgramming={true}
          selectedInspectionTypes={selectedInspectionTypes}
          autoProgramInspectionRegion={autoProgramInspectionRegion}
        />
      }
      {currentStep === 2 &&
        <ConfigROI
          setCurrentStep={setCurrentStep}
          parsedComponentInfo={parsedComponentInfo}
          parseRules={parseRules}
          currentTranslation={currentTranslation}
          currentRotation={currentRotation}
          curProduct={curProduct}
          currentFileUri={currentFileUri}
          horizontallyFlipped={horizontallyFlipped}
          verticallyFlipped={verticallyFlipped}
        />
      }
    </div>
  );
};

export default UploadCAD;