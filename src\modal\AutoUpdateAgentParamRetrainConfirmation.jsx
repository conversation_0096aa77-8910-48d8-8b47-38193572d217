import React from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';


const AutoUpdateAgentParamRetrainConfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;
  
  const { t } = useTranslation();

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('productDefine.retrainConfirmation')}
      </span>}
      footer={null}
    >
      <div className='flex p-4 flex-col gap-2 self-stretch'>
        <span className='font-source text-[14px] font-normal leading-[150%]'>
          {t('productDefine.')}
        </span>
      </div>
    </CustomModal>
  );
};

export default AutoUpdateAgentParamRetrainConfirmation;