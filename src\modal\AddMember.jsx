import React, { useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, ConfigProvider, Input, Select } from 'antd';
import { fieldConstraints, userRoles } from '../common/const';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useNewAccountMutation } from '../services/account';


const AddMember = (props) => {
  const {
    isOpened,
    setIsOpened,
    selectedRole,
    setSelectedRole,
    refetchAccountList,
  } = props;
  
  const { t } = useTranslation();

  const [newUsername, setNewUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [isPwdCharLengthValid, setIsPwdCharLengthValid] = useState(false);
  const [isPwdAtLeastOneLetter, setIsPwdAtLeastOneLetter] = useState(false);
  const [isPwdAtLeastOneNumber, setIsPwdAtLeastOneNumber] = useState(false);

  const [newAccount] = useNewAccountMutation();
  
  const handleNewAccSubmit = async (username, pwd, selectedRole) => {
    if (_.isEmpty(username) || _.isEmpty(pwd)) {
      aoiAlert(t('notification.error.pleaseFillInAllRequiredFields'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (!isPwdCharLengthValid || !isPwdAtLeastOneLetter || !isPwdAtLeastOneNumber) {
      aoiAlert(t('notification.error.pleaseUseAStrongPwd'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await newAccount({
      name: username,
      password: pwd,
      role: selectedRole,
    });

    if (res.error) {
      aoiAlert(t('notification.error.newAccount'), ALERT_TYPES.COMMON_ERROR);
      console.error('create new account failed', res.error.message);
      return;
    }

    setNewUsername('');
    setNewPassword('');

    refetchAccountList();
    setIsOpened(false);
  };

  useEffect(() => {
    if (_.isEmpty(newPassword)) {
      setIsPwdCharLengthValid(false);
      setIsPwdAtLeastOneLetter(false);
      setIsPwdAtLeastOneNumber(false);
      return;
    }

    setIsPwdCharLengthValid(newPassword.length >= 8);
    setIsPwdAtLeastOneLetter(/[a-zA-Z]/.test(newPassword));
    setIsPwdAtLeastOneNumber(/[0-9]/.test(newPassword));
  }, [newPassword]);

  return (
    <CustomModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('settings.addMember')}
      </span>}
      footer={null}
    >
      <div className='flex py-6 px-4 flex-col gap-8 self-stretch'>
        <div className='flex flex-col gap-4 self-stretch'>
          <ConfigProvider
            theme={{
              components: {
                Select: {
                  selectorBg: '#333',
                  colorBorder: '#4F4F4F',
                },
              }
            }}
          >
            <Select
              style={{ height: '59px' }}
              value={selectedRole}
              onChange={(v) => setSelectedRole(v)}
              options={[
                {
                  value: userRoles.programmer,
                  label: <div className='flex flex-col gap-1'>
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {t('settings.newProgrammer')}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('settings.teachPCB')}
                    </span>
                  </div>
                },
                {
                  value: userRoles.operator,
                  label: <div className='flex flex-col gap-1'>
                    <span className='font-source text-[14px] font-semibold leading-[150%]'>
                      {t('settings.newOperator')}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('settings.onlyCanOperate')}
                    </span>
                  </div>,
                }
              ]}
            />
          </ConfigProvider>
          <div className='flex gap-6 items-center self-stretch'>
            <span className='font-source text-[14px] font-semibold leading-[150%] w-[64px]'>
              {t('settings.username')}
            </span>
            <div className='flex flex-1 items-center self-stretch'>
              <Input
                value={newUsername}
                onChange={(e) => setNewUsername(e.target.value)}
                style={{ width: '100%' }}
                minLength={fieldConstraints.login.username.minLength}
                maxLength={fieldConstraints.login.username.maxLength}
              />
            </div>
          </div>
          <div className='flex gap-6 items-center self-stretch'>
            <span className='font-source text-[14px] font-semibold leading-[150%] w-[64px]'>
              {t('settings.password')}
            </span>
            <div className='flex flex-1 items-center self-stretch'>
              <Input.Password
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                style={{ width: '100%' }}
                minLength={fieldConstraints.login.password.minLength}
                maxLength={fieldConstraints.login.password.maxLength}
              />
            </div>
          </div>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('settings.passwordMustContain')}
            </span>
            <div className='flex gap-2 items-center'>
              <img src={`/icn/check_${isPwdCharLengthValid ? 'blue' : 'gray'}.svg`} alt='check' className='h-[10px] w-[7.38px]' />
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('settings.atLeast8')}
              </span>
            </div>
            <div className='flex gap-2 items-center'>
              <img src={`/icn/check_${isPwdAtLeastOneLetter ? 'blue' : 'gray'}.svg`} alt='check' className='h-[10px] w-[7.38px]' />
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('settings.atLeastOneLetter')}
              </span>
            </div>
            <div className='flex gap-2 items-center'>
              <img src={`/icn/check_${isPwdAtLeastOneNumber ? 'blue' : 'gray'}.svg`} alt='check' className='h-[10px] w-[7.38px]' />
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('settings.atLeastOneNumber')}
              </span>
            </div>
          </div>
        </div>
        <div className='flex self-stretch items-center'>
          <span className='font-source text-[12px] font-normal leading-[150%] flex-1'>
            {t('settings.onlyAdminCanChange')}
          </span>
        </div>
      </div>
      <div className='flex p-4 gap-2 self-stretch items-center self-stretch'>
        <Button
          style={{ width: '50%' }}
          onClick={() => setIsOpened(false)}
        >
          <span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('common.cancel')}
          </span>
        </Button>
        <Button
          type='primary'
          style={{ width: '50%' }}
          onClick={() => handleNewAccSubmit(newUsername, newPassword, selectedRole)}
        >
          <span className='font-source text-[12px] font-semibold leading-[150%]'>
            {t('settings.createAccount')}
          </span>
        </Button>
      </div>
    </CustomModal>
  );
};

export default AddMember;