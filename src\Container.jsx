import { ConfigProvider, theme } from 'antd';
import { I18nextProvider } from 'react-i18next';
import React from 'react';
import i18n from './i18n';
import { Provider } from 'react-redux';
import { store, persistor } from './store';
import { PersistGate } from 'redux-persist/integration/react';
import Router from './Router';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';


const Container = () => {
  const { darkAlgorithm } = theme;

  return (
    <I18nextProvider i18n={i18n}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <ConfigProvider
            locale={i18n.language === 'en' ? enUS : zhCN}
            theme={{
              algorithm: darkAlgorithm,
              components: {
                Input: {
                  fontFamily: 'Source Sans Pro, sans-serif',
                  inputFontSize: '12px',
                  activeBg: '#00000033',
                  controlHeight: 36,
                  borderRadius: 4,
                },
                InputNumber: {
                  fontFamily: 'Source Sans Pro, sans-serif',
                  inputFontSize: '12px',
                  activeBg: '#00000033',
                  controlHeight: 32,
                  borderRadius: 2,
                },
                Button: {
                  primaryColor: '#333',
                  colorPrimaryBg: '#56ccf21a',
                  controlHeight: 28,
                },
                Table: {
                  headerBg: '#131313',
                  cellPaddingBlock: 8,
                  colorBgContainer: '#ffffff08',
                  borderColor: '#131313',
                  lineWidth: 4,
                  headerBorderRadius: 0,
                },
                Menu: {
                  colorBgContainer: '#ffffff08',
                  itemPaddingInline: 0,
                  itemHeight: 34,
                  itemMarginInline: 0,
                  itemMarginBlock: 0,
                  itemSelectedColor: '#fff',
                  itemSelectedBg: '#ffffff1a',
                  itemBorderRadius: 4,
                },
                Select: {
                  selectorBg: '#131313',
                  colorBorder: '#131313',
                  optionSelectedBg: '#3C8EA9',
                  optionSelectedColor: '#333',
                  optionSelectedFontWeight: 1000,
                },
                Tabs: {
                  cardPadding: '10px 24px',
                  horizontalMargin: '4px 16px 0 16px',
                  colorBgContainer: '#ffffff0d',
                  colorBorder: '#4F4F4F',
                  cardBg: '#1E1E1E',
                  itemSelectedColor: '#fff',
                  itemHoverColor: '#fff',
                  itemActiveColor: '#fff',
                  // lineWidth: 2,
                },
                Collapse: {
                  headerBg: '#ffffff1a',
                  contentBg: '#ffffff0d',
                  contentPadding: '0',
                  headerPadding: '4px 8px',
                  colorBorder: 'transparent',
                  borderRadiusLG: 0,
                },
                Segmented: {
                  itemSelectedBg: '#2F80ED',
                },
                Steps: {
                  iconSize: 24,
                  titleLineHeight: 24,
                  colorTextLightSolid: '#333',
                },
                Switch: {
                  handleBg: '#333',
                },
              },
              token: {
                colorText: '#fff',
                colorBgContainer: '#333',
                colorBorder: '#555',
                colorPrimary: '#56CCF2',
              },
            }}
          >
            <Router />
          </ConfigProvider>
        </PersistGate>
      </Provider>
    </I18nextProvider>
  );
};

export default Container;