import { ConfigProvider } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { CustomCollapse } from '../../../../../common/styledComponent';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';


const UngroupedFeature = (props) => {
  const {
    allFeatures,
    selectedUngroupedFid,
    setSelectedUngroupedFid,
    setRequiredLocateRect,
    setSelectedAgentParam,
    setSelectedPartNo,
    setSelectedPackageNo,
    setSelectedScope,
    setSelectedCid,
    setSelectedFid,
    allUngroupedFeaturesColumnRef,
  } = props;

  const { t } = useTranslation();

  const [ungroupedFeatures, setUngroupedFeatures] = useState([]);

  useEffect(() => {
    setUngroupedFeatures(_.filter(allFeatures, f => !_.isInteger(f.group_id)));
  }, [allFeatures]);

  return (
    <div
      className='flex flex-col'
    >
      <ConfigProvider
        theme={{
          components: {
            Collapse: {
              headerPadding: '0px 0 0px 8px',
              contentPadding: '0 0 0 8px',
            }
          }
        }}
      >
        <CustomCollapse
          ref={allUngroupedFeaturesColumnRef}
          style={{ width: '100%' }}
          items={[{
            key: 'ungroupedFeatures',
            label: <div className={`flex items-center gap-2 justify-between px-2 h-[32px] transition-all duration-300 ease-in-out`}>
              <span className='font-source text-[12px] font-normal leading-[150%] text-[#EB5E28] italic'>
                {t('productDefine.ungroupedFeature')}
              </span>
            </div>,
            children: <div className='flex flex-col gap-0.5'>
              {_.map(ungroupedFeatures, (f) => {
                return <div
                  className={`flex h-[28px] px-2 gap-2 items-center self-stretch border-b-[1px] border-b-[#ffffff0f] 
                    cursor-pointer transition-all duration-300 ease-in-out justify-between ${selectedUngroupedFid === Number(f.feature_id) ? 'bg-[#56ccf21a]' : 'bg-[#00000033]'}`}
                  onClick={() => {
                    
                    setSelectedAgentParam(null);
                    setSelectedPartNo(null);
                    setSelectedPackageNo(null);
                    setSelectedScope(null);
                    setSelectedCid(null);
                    setSelectedFid(null);
                    selectedUngroupedFid === Number(f.feature_id) ? setSelectedUngroupedFid(null) : setSelectedUngroupedFid(Number(f.feature_id));
                    if (selectedUngroupedFid !== Number(f.feature_id)) {
                      setRequiredLocateRect({
                        cid: null,
                        fid: Number(f.feature_id),
                      });
                    }
                  }}
                >
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {f.feature_type}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%] text-[#EB5E28] italic'>
                      {t('productDefine.ungroupedFeature')}
                    </span>
                  </div>
                </div>;
              })}
            </div>,
          }]}
        />
      </ConfigProvider>
    </div>
  );
};

export default UngroupedFeature;